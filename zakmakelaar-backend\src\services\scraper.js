const { scrapeFunda } = require('./scrapers/fundaScraper');
const { scrapePararius } = require('./scrapers/parariusScraper');
const { scrapeHuurwoningen } = require('./scrapers/huurwoningenScraper');
const {
  cleanup,
  getScrapingMetrics
} = require('./scraperUtils');

// Agent management methods
let agentStatus = {
  isRunning: false,
  currentTask: "idle",
  config: {
    scrapeInterval: 15 * 60 * 1000, // 15 minutes
    maxRetries: 3,
    timeout: 30000,
    activeScrapers: ["pararius", "funda", "huurwoningen"],
  },
};

const getAgentStatus = () => {
  return { ...agentStatus };
};

const startAgent = async () => {
  if (agentStatus.isRunning) {
    return { message: "Agent is already running" };
  }

  agentStatus.isRunning = true;
  agentStatus.currentTask = "starting";

  // Start the scraping process
  try {
    agentStatus.currentTask = "scraping pararius";
    await scrapePararius();

    agentStatus.currentTask = "scraping funda";
    await scrapeFunda();

    agentStatus.currentTask = "scraping huurwoningen";
    await scrapeHuurwoningen();

    agentStatus.currentTask = "idle";
    return {
      message: "Agent started successfully and completed initial scraping",
    };
  } catch (error) {
    agentStatus.isRunning = false;
    agentStatus.currentTask = "error";
    throw error;
  }
};

const stopAgent = async () => {
  if (!agentStatus.isRunning) {
    return { message: "Agent is not running" };
  }

  agentStatus.isRunning = false;
  agentStatus.currentTask = "stopping";

  // Clean up any running processes
  await cleanup();

  agentStatus.currentTask = "idle";
  return { message: "Agent stopped successfully" };
};

const updateAgentConfig = async (newConfig) => {
  // Validate the new configuration
  const validScrapers = ["pararius", "funda", "huurwoningen"];
  const validScrapersInConfig = newConfig.activeScrapers?.filter((scraper) =>
    validScrapers.includes(scraper)
  );

  if (validScrapersInConfig && validScrapersInConfig.length === 0) {
    throw new Error("At least one valid scraper must be active");
  }

  // Update the configuration
  agentStatus.config = {
    ...agentStatus.config,
    ...newConfig,
  };

  return { message: "Agent configuration updated successfully" };
};

// Export functions
module.exports = {
  scrapePararius,
  scrapeFunda,
  scrapeHuurwoningen,
  cleanup,
  getScrapingMetrics,
  getAgentStatus,
  startAgent,
  stopAgent,
  updateAgentConfig,
};