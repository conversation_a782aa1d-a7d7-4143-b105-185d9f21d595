{"version": 3, "sources": ["webpack://user-agents/webpack/universalModuleDefinition", "webpack://user-agents/./src/index.js", "webpack://user-agents/./src/user-agent.js", "webpack://user-agents/./node_modules/lodash.clonedeep/index.js", "webpack://user-agents/webpack/bootstrap", "webpack://user-agents/webpack/startup", "webpack://user-agents/webpack/runtime/node module decorator"], "names": ["root", "factory", "exports", "module", "define", "amd", "global", "UserAgent", "makeCumulativeWeightIndexPairs", "weightIndexPairs", "totalWeight", "reduce", "sum", "weight", "map", "index", "defaultWeightIndexPairs", "userAgents", "defaultCumulativeWeightIndexPairs", "constructFilter", "filters", "accessor", "parentObject", "childFilters", "RegExp", "value", "userAgent", "test", "Array", "childFilter", "Object", "entries", "key", "valueFilter", "every", "error", "setCumulativeWeightIndexPairs", "cumulativeWeightIndexPairs", "defineProperty", "configurable", "enumerable", "writable", "Symbol", "toPrimitive", "data", "randomize", "randomNumber", "Math", "random", "find", "cumulativeWeight", "rawUserAgent", "filter", "for<PERSON>ach", "push", "constructCumulativeWeightIndexPairsFromFilters", "length", "Error", "Proxy", "apply", "get", "target", "property", "receiver", "prototype", "hasOwnProperty", "call", "propertyIsEnumerable", "undefined", "Reflect", "Function", "HASH_UNDEFINED", "MAX_SAFE_INTEGER", "argsTag", "boolTag", "dateTag", "funcTag", "genTag", "mapTag", "numberTag", "objectTag", "promiseTag", "regexpTag", "setTag", "stringTag", "symbolTag", "weakMapTag", "arrayBufferTag", "dataViewTag", "float32Tag", "float64Tag", "int8Tag", "int16Tag", "int32Tag", "uint8Tag", "uint8ClampedTag", "uint16Tag", "uint32Tag", "reFlags", "reIsHostCtor", "reIsUint", "cloneableTags", "freeGlobal", "freeSelf", "self", "freeExports", "nodeType", "freeModule", "moduleExports", "addMapEntry", "pair", "set", "addSetEntry", "add", "arrayReduce", "array", "iteratee", "accumulator", "initAccum", "isHostObject", "result", "toString", "e", "mapToArray", "size", "overArg", "func", "transform", "arg", "setToArray", "uid", "arrayProto", "funcProto", "objectProto", "coreJsData", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "exec", "keys", "IE_PROTO", "funcToString", "objectToString", "reIsNative", "replace", "<PERSON><PERSON><PERSON>", "Uint8Array", "getPrototype", "getPrototypeOf", "objectCreate", "create", "splice", "nativeGetSymbols", "getOwnPropertySymbols", "nativeIsBuffer", "<PERSON><PERSON><PERSON><PERSON>", "nativeKeys", "DataView", "getNative", "Map", "Promise", "Set", "WeakMap", "nativeCreate", "dataViewCtorString", "toSource", "mapCtorString", "promiseCtorString", "setCtorString", "weakMapCtorString", "symbol<PERSON>roto", "symbolValueOf", "valueOf", "Hash", "this", "clear", "entry", "ListCache", "MapCache", "<PERSON><PERSON>", "__data__", "arrayLikeKeys", "inherited", "isArray", "isObjectLike", "isArrayLike", "isArrayLikeObject", "isArguments", "n", "baseTimes", "String", "skipIndexes", "isIndex", "assignValue", "object", "objValue", "eq", "assocIndexOf", "baseClone", "isDeep", "isFull", "customizer", "stack", "isObject", "isArr", "constructor", "input", "initCloneArray", "source", "copyArray", "tag", "getTag", "isFunc", "buffer", "slice", "copy", "<PERSON><PERSON><PERSON><PERSON>", "isPrototype", "proto", "initCloneObject", "copyObject", "getSymbols", "copySymbols", "baseAssign", "cloneFunc", "Ctor", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dataView", "byteOffset", "byteLength", "cloneDataView", "typedArray", "cloneTypedArray", "cloneMap", "regexp", "lastIndex", "cloneRegExp", "cloneSet", "symbol", "initCloneByTag", "stacked", "props", "keysFunc", "symbolsFunc", "values", "offset", "arrayPush", "baseGetAllKeys", "getAllKeys", "arrayEach", "subValue", "baseIsNative", "isFunction", "arrayBuffer", "newValue", "getMapData", "type", "getValue", "has", "pop", "cache", "pairs", "LARGE_ARRAY_SIZE", "other", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "resolve", "ctorString", "<PERSON><PERSON><PERSON><PERSON>", "baseKeys", "__webpack_module_cache__", "__webpack_require__", "moduleId", "id", "loaded", "__webpack_modules__", "nmd", "paths", "children"], "mappings": "CAAA,SAA2CA,EAAMC,GAC1B,iBAAZC,SAA0C,iBAAXC,OACxCA,OAAOD,QAAUD,IACQ,mBAAXG,QAAyBA,OAAOC,IAC9CD,OAAO,cAAe,GAAIH,GACA,iBAAZC,QACdA,QAAQ,eAAiBD,IAEzBD,EAAK,eAAiBC,IARxB,CASGK,QAAQ,WACX,M,wCCVA,I,8EAAA,S,4BAGeC,Q,8HCHf,gBAEA,Y,0/CAIA,MAAMC,EAAkCC,IACtC,MAAMC,EAAcD,EAAiBE,QAAO,CAACC,GAAMC,KAAYD,EAAMC,GAAQ,GAC7E,IAAID,EAAM,EACV,OAAOH,EAAiBK,KAAI,EAAED,EAAQE,MACpCH,GAAOC,EAASH,EACT,CAACE,EAAKG,OAKXC,EAA0BC,UAAWH,KAAI,EAAGD,UAAUE,IAAU,CAACF,EAAQE,KACzEG,EAAoCV,EAA+BQ,GAInEG,EAAkB,CAACC,EAASC,EAAWC,IAAgBA,MAC3D,IAAIC,EA2BJ,OAzBEA,EADqB,mBAAZH,EACM,CAACA,GACPA,aAAmBI,OACb,CACbC,GACmB,iBAAVA,GAAsBA,GAASA,EAAMC,UACxCN,EAAQO,KAAKF,EAAMC,WACnBN,EAAQO,KAAKF,IAGZL,aAAmBQ,MACbR,EAAQN,KAAIe,GAAeV,EAAgBU,KAC9B,iBAAZT,EACDU,OAAOC,QAAQX,GAASN,KAAI,EAAEkB,EAAKC,KAChDd,EAAgBc,GAAaX,GAAgBA,EAAaU,OAG7C,CACbP,GACmB,iBAAVA,GAAsBA,GAASA,EAAMC,UACxCN,IAAYK,EAAMC,UAClBN,IAAYK,GAKdH,IACN,IACE,MAAMG,EAAQJ,EAASC,GACvB,OAAOC,EAAaW,OAAML,GAAeA,EAAYJ,KACrD,MAAOU,GAEP,OAAO,KAwBPC,EAAgC,CAACV,EAAWW,KAChDP,OAAOQ,eAAeZ,EAAW,6BAA8B,CAC7Da,cAAc,EACdC,YAAY,EACZC,UAAU,EACVhB,MAAOY,K,EA6CRK,OAAOC,Y,IAxCWpC,E,uZACnB,WAAYa,GAAS,MAGnB,G,4FAHmB,aACnB,gBADmB,GAuCE,IACrB,EAAKwB,KAAKlB,YAxCS,mBA2CV,IACT,EAAKkB,KAAKlB,YA5CS,iBA+CZ,KACP,MAAMA,EAAY,IAAInB,EAGtB,OAFA6B,EAA8BV,EAAW,EAAKW,4BAC9CX,EAAUmB,YACHnB,KAnDY,oBAsDT,KAEV,MAAMoB,EAAeC,KAAKC,UACnB,CAAEjC,GAAS,EAAKsB,2BACpBY,MAAK,EAAEC,KAAsBA,EAAmBJ,IAC7CK,EAAelC,UAAWF,GAEhC,EAAK6B,MAAO,aAAUO,MA3DtBf,EAA8B,EAAD,GA9BuBhB,KACtD,IAAKA,EACH,OAAOF,EAGT,MAAMkC,EAASjC,EAAgBC,GAEzBX,EAAmB,GAMzB,OALAQ,UAAWoC,SAAQ,CAACF,EAAcpC,KAC5BqC,EAAOD,IACT1C,EAAiB6C,KAAK,CAACH,EAAatC,OAAQE,OAGzCP,EAA+BC,IAiBA8C,CAA+CnC,IACpC,IAA3C,EAAKiB,2BAA2BmB,OAClC,MAAM,IAAIC,MAAM,wCAKlB,OAFA,EAAKZ,YAEL,IAAO,IAAIa,MAAJ,KAAgB,CACrBC,MAAO,IAAM,EAAKX,SAClBY,IAAK,CAACC,EAAQC,EAAUC,KAItB,GAHsBF,EAAOjB,MAA4B,iBAAbkB,GACvChC,OAAOkC,UAAUC,eAAeC,KAAKL,EAAOjB,KAAMkB,IAClDhC,OAAOkC,UAAUG,qBAAqBD,KAAKL,EAAOjB,KAAMkB,GAC1C,CACjB,MAAMrC,EAAQoC,EAAOjB,KAAKkB,GAC1B,QAAcM,IAAV3C,EACF,OAAOA,EAIX,OAAO4C,QAAQT,IAAIC,EAAQC,EAAUC,O,YAvBNO,W,cAAlB/D,E,UA4BFa,IACf,IACE,OAAO,IAAIb,EAAUa,GACrB,MAAOe,GACP,OAAO,S,8CC/Gb,IAGIoC,EAAiB,4BAGjBC,EAAmB,iBAGnBC,EAAU,qBAEVC,EAAU,mBACVC,EAAU,gBAEVC,EAAU,oBACVC,EAAS,6BACTC,EAAS,eACTC,EAAY,kBACZC,EAAY,kBACZC,EAAa,mBACbC,EAAY,kBACZC,EAAS,eACTC,EAAY,kBACZC,EAAY,kBACZC,EAAa,mBAEbC,EAAiB,uBACjBC,EAAc,oBACdC,EAAa,wBACbC,EAAa,wBACbC,EAAU,qBACVC,EAAW,sBACXC,EAAW,sBACXC,EAAW,sBACXC,EAAkB,6BAClBC,EAAY,uBACZC,EAAY,uBASZC,EAAU,OAGVC,EAAe,8BAGfC,EAAW,mBAGXC,EAAgB,GACpBA,EAAc5B,GAAW4B,EA7CV,kBA8CfA,EAAcd,GAAkBc,EAAcb,GAC9Ca,EAAc3B,GAAW2B,EAAc1B,GACvC0B,EAAcZ,GAAcY,EAAcX,GAC1CW,EAAcV,GAAWU,EAAcT,GACvCS,EAAcR,GAAYQ,EAAcvB,GACxCuB,EAActB,GAAasB,EAAcrB,GACzCqB,EAAcnB,GAAamB,EAAclB,GACzCkB,EAAcjB,GAAaiB,EAAchB,GACzCgB,EAAcP,GAAYO,EAAcN,GACxCM,EAAcL,GAAaK,EAAcJ,IAAa,EACtDI,EArDe,kBAqDWA,EAAczB,GACxCyB,EAAcf,IAAc,EAG5B,IAAIgB,EAA8B,iBAAVhG,QAAsBA,QAAUA,OAAOwB,SAAWA,QAAUxB,OAGhFiG,EAA0B,iBAARC,MAAoBA,MAAQA,KAAK1E,SAAWA,QAAU0E,KAGxExG,EAAOsG,GAAcC,GAAYjC,SAAS,cAATA,GAGjCmC,EAA4CvG,IAAYA,EAAQwG,UAAYxG,EAG5EyG,EAAaF,GAA4CtG,IAAWA,EAAOuG,UAAYvG,EAGvFyG,EAAgBD,GAAcA,EAAWzG,UAAYuG,EAUzD,SAASI,EAAY/F,EAAKgG,GAGxB,OADAhG,EAAIiG,IAAID,EAAK,GAAIA,EAAK,IACfhG,EAWT,SAASkG,EAAYD,EAAKtF,GAGxB,OADAsF,EAAIE,IAAIxF,GACDsF,EAuDT,SAASG,EAAYC,EAAOC,EAAUC,EAAaC,GACjD,IAAIvG,GAAS,EACTyC,EAAS2D,EAAQA,EAAM3D,OAAS,EAKpC,IAHI8D,GAAa9D,IACf6D,EAAcF,IAAQpG,MAEfA,EAAQyC,GACf6D,EAAcD,EAASC,EAAaF,EAAMpG,GAAQA,EAAOoG,GAE3D,OAAOE,EAyCT,SAASE,EAAa9F,GAGpB,IAAI+F,GAAS,EACb,GAAa,MAAT/F,GAA0C,mBAAlBA,EAAMgG,SAChC,IACED,KAAY/F,EAAQ,IACpB,MAAOiG,IAEX,OAAOF,EAUT,SAASG,EAAW7G,GAClB,IAAIC,GAAS,EACTyG,EAAS5F,MAAMd,EAAI8G,MAKvB,OAHA9G,EAAIuC,SAAQ,SAAS5B,EAAOO,GAC1BwF,IAASzG,GAAS,CAACiB,EAAKP,MAEnB+F,EAWT,SAASK,EAAQC,EAAMC,GACrB,OAAO,SAASC,GACd,OAAOF,EAAKC,EAAUC,KAW1B,SAASC,EAAWlB,GAClB,IAAIhG,GAAS,EACTyG,EAAS5F,MAAMmF,EAAIa,MAKvB,OAHAb,EAAI1D,SAAQ,SAAS5B,GACnB+F,IAASzG,GAASU,KAEb+F,EAIT,IASMU,EATFC,EAAavG,MAAMoC,UACnBoE,EAAY9D,SAASN,UACrBqE,EAAcvG,OAAOkC,UAGrBsE,EAAatI,EAAK,sBAGlBuI,GACEL,EAAM,SAASM,KAAKF,GAAcA,EAAWG,MAAQH,EAAWG,KAAKC,UAAY,KACvE,iBAAmBR,EAAO,GAItCS,EAAeP,EAAUX,SAGzBxD,GAAiBoE,EAAYpE,eAO7B2E,GAAiBP,EAAYZ,SAG7BoB,GAAarH,OAAO,IACtBmH,EAAazE,KAAKD,IAAgB6E,QAzQjB,sBAyQuC,QACvDA,QAAQ,yDAA0D,SAAW,KAI5EC,GAASnC,EAAgB5G,EAAK+I,YAAS3E,EACvC1B,GAAS1C,EAAK0C,OACdsG,GAAahJ,EAAKgJ,WAClBC,GAAepB,EAAQ/F,OAAOoH,eAAgBpH,QAC9CqH,GAAerH,OAAOsH,OACtBjF,GAAuBkE,EAAYlE,qBACnCkF,GAASlB,EAAWkB,OAGpBC,GAAmBxH,OAAOyH,sBAC1BC,GAAiBT,GAASA,GAAOU,cAAWrF,EAC5CsF,GAAa7B,EAAQ/F,OAAO2G,KAAM3G,QAGlC6H,GAAWC,GAAU5J,EAAM,YAC3B6J,GAAMD,GAAU5J,EAAM,OACtB8J,GAAUF,GAAU5J,EAAM,WAC1B+J,GAAMH,GAAU5J,EAAM,OACtBgK,GAAUJ,GAAU5J,EAAM,WAC1BiK,GAAeL,GAAU9H,OAAQ,UAGjCoI,GAAqBC,GAASR,IAC9BS,GAAgBD,GAASN,IACzBQ,GAAoBF,GAASL,IAC7BQ,GAAgBH,GAASJ,IACzBQ,GAAoBJ,GAASH,IAG7BQ,GAAc9H,GAASA,GAAOsB,eAAYI,EAC1CqG,GAAgBD,GAAcA,GAAYE,aAAUtG,EASxD,SAASuG,GAAK5I,GACZ,IAAIhB,GAAS,EACTyC,EAASzB,EAAUA,EAAQyB,OAAS,EAGxC,IADAoH,KAAKC,UACI9J,EAAQyC,GAAQ,CACvB,IAAIsH,EAAQ/I,EAAQhB,GACpB6J,KAAK7D,IAAI+D,EAAM,GAAIA,EAAM,KA2F7B,SAASC,GAAUhJ,GACjB,IAAIhB,GAAS,EACTyC,EAASzB,EAAUA,EAAQyB,OAAS,EAGxC,IADAoH,KAAKC,UACI9J,EAAQyC,GAAQ,CACvB,IAAIsH,EAAQ/I,EAAQhB,GACpB6J,KAAK7D,IAAI+D,EAAM,GAAIA,EAAM,KAyG7B,SAASE,GAASjJ,GAChB,IAAIhB,GAAS,EACTyC,EAASzB,EAAUA,EAAQyB,OAAS,EAGxC,IADAoH,KAAKC,UACI9J,EAAQyC,GAAQ,CACvB,IAAIsH,EAAQ/I,EAAQhB,GACpB6J,KAAK7D,IAAI+D,EAAM,GAAIA,EAAM,KAuF7B,SAASG,GAAMlJ,GACb6I,KAAKM,SAAW,IAAIH,GAAUhJ,GA4FhC,SAASoJ,GAAc1J,EAAO2J,GAG5B,IAAI5D,EAAU6D,GAAQ5J,IAsrBxB,SAAqBA,GAEnB,OAmFF,SAA2BA,GACzB,OAmIF,SAAsBA,GACpB,QAASA,GAAyB,iBAATA,EApIlB6J,CAAa7J,IAAU8J,GAAY9J,GApFnC+J,CAAkB/J,IAAUwC,GAAeC,KAAKzC,EAAO,aAC1D0C,GAAqBD,KAAKzC,EAAO,WAAamH,GAAe1E,KAAKzC,IAAUgD,GAzrBhDgH,CAAYhK,GAljB9C,SAAmBiK,EAAGtE,GAIpB,IAHA,IAAIrG,GAAS,EACTyG,EAAS5F,MAAM8J,KAEV3K,EAAQ2K,GACflE,EAAOzG,GAASqG,EAASrG,GAE3B,OAAOyG,EA4iBHmE,CAAUlK,EAAM+B,OAAQoI,QACxB,GAEApI,EAASgE,EAAOhE,OAChBqI,IAAgBrI,EAEpB,IAAK,IAAIxB,KAAOP,GACT2J,IAAanH,GAAeC,KAAKzC,EAAOO,IACvC6J,IAAuB,UAAP7J,GAAmB8J,GAAQ9J,EAAKwB,KACpDgE,EAAOlE,KAAKtB,GAGhB,OAAOwF,EAaT,SAASuE,GAAYC,EAAQhK,EAAKP,GAChC,IAAIwK,EAAWD,EAAOhK,GAChBiC,GAAeC,KAAK8H,EAAQhK,IAAQkK,GAAGD,EAAUxK,UACxC2C,IAAV3C,GAAyBO,KAAOgK,KACnCA,EAAOhK,GAAOP,GAYlB,SAAS0K,GAAahF,EAAOnF,GAE3B,IADA,IAAIwB,EAAS2D,EAAM3D,OACZA,KACL,GAAI0I,GAAG/E,EAAM3D,GAAQ,GAAIxB,GACvB,OAAOwB,EAGX,OAAQ,EA8BV,SAAS4I,GAAU3K,EAAO4K,EAAQC,EAAQC,EAAYvK,EAAKgK,EAAQQ,GACjE,IAAIhF,EAIJ,GAHI+E,IACF/E,EAASwE,EAASO,EAAW9K,EAAOO,EAAKgK,EAAQQ,GAASD,EAAW9K,SAExD2C,IAAXoD,EACF,OAAOA,EAET,IAAKiF,GAAShL,GACZ,OAAOA,EAET,IAAIiL,EAAQrB,GAAQ5J,GACpB,GAAIiL,GAEF,GADAlF,EA2XJ,SAAwBL,GACtB,IAAI3D,EAAS2D,EAAM3D,OACfgE,EAASL,EAAMwF,YAAYnJ,GAG3BA,GAA6B,iBAAZ2D,EAAM,IAAkBlD,GAAeC,KAAKiD,EAAO,WACtEK,EAAOzG,MAAQoG,EAAMpG,MACrByG,EAAOoF,MAAQzF,EAAMyF,OAEvB,OAAOpF,EApYIqF,CAAepL,IACnB4K,EACH,OA6ON,SAAmBS,EAAQ3F,GACzB,IAAIpG,GAAS,EACTyC,EAASsJ,EAAOtJ,OAEpB2D,IAAUA,EAAQvF,MAAM4B,IACxB,OAASzC,EAAQyC,GACf2D,EAAMpG,GAAS+L,EAAO/L,GAExB,OAAOoG,EArPI4F,CAAUtL,EAAO+F,OAErB,CACL,IAAIwF,EAAMC,GAAOxL,GACbyL,EAASF,GAAOpI,GAAWoI,GAAOnI,EAEtC,GAAI4E,GAAShI,GACX,OA0HN,SAAqB0L,EAAQd,GAC3B,GAAIA,EACF,OAAOc,EAAOC,QAEhB,IAAI5F,EAAS,IAAI2F,EAAOR,YAAYQ,EAAO3J,QAE3C,OADA2J,EAAOE,KAAK7F,GACLA,EAhII8F,CAAY7L,EAAO4K,GAE5B,GAAIW,GAAOhI,GAAagI,GAAOvI,GAAYyI,IAAWlB,EAAS,CAC7D,GAAIzE,EAAa9F,GACf,OAAOuK,EAASvK,EAAQ,GAG1B,GADA+F,EA+XN,SAAyBwE,GACvB,MAAqC,mBAAtBA,EAAOW,aAA8BY,GAAYvB,GAE5D,IAzVcwB,EAwVHvE,GAAa+C,GAvVrBS,GAASe,GAASrE,GAAaqE,GAAS,IADjD,IAAoBA,EAzCLC,CAAgBP,EAAS,GAAKzL,IAClC4K,EACH,OA6QR,SAAqBS,EAAQd,GAC3B,OAAO0B,GAAWZ,EAAQa,GAAWb,GAASd,GA9QjC4B,CAAYnM,EAhD3B,SAAoBuK,EAAQc,GAC1B,OAAOd,GAAU0B,GAAWZ,EAAQrE,GAAKqE,GAASd,GA+ClB6B,CAAWrG,EAAQ/F,QAE1C,CACL,IAAK4E,EAAc2G,GACjB,OAAOhB,EAASvK,EAAQ,GAE1B+F,EA0YN,SAAwBwE,EAAQgB,EAAKc,EAAWzB,GAC9C,IAAI0B,EAAO/B,EAAOW,YAClB,OAAQK,GACN,KAAKzH,EACH,OAAOyI,GAAiBhC,GAE1B,KAAKtH,EACL,KAAKC,EACH,OAAO,IAAIoJ,GAAM/B,GAEnB,KAAKxG,EACH,OA3QN,SAAuByI,EAAU5B,GAC/B,IAAIc,EAASd,EAAS2B,GAAiBC,EAASd,QAAUc,EAASd,OACnE,OAAO,IAAIc,EAAStB,YAAYQ,EAAQc,EAASC,WAAYD,EAASE,YAyQ3DC,CAAcpC,EAAQK,GAE/B,KAAK5G,EAAY,KAAKC,EACtB,KAAKC,EAAS,KAAKC,EAAU,KAAKC,EAClC,KAAKC,EAAU,KAAKC,EAAiB,KAAKC,EAAW,KAAKC,EACxD,OA/MN,SAAyBoI,EAAYhC,GACnC,IAAIc,EAASd,EAAS2B,GAAiBK,EAAWlB,QAAUkB,EAAWlB,OACvE,OAAO,IAAIkB,EAAW1B,YAAYQ,EAAQkB,EAAWH,WAAYG,EAAW7K,QA6MjE8K,CAAgBtC,EAAQK,GAEjC,KAAKvH,EACH,OArQN,SAAkBhE,EAAKuL,EAAQyB,GAE7B,OAAO5G,EADKmF,EAASyB,EAAUnG,EAAW7G,IAAM,GAAQ6G,EAAW7G,GACzC+F,EAAa,IAAI/F,EAAI6L,aAmQpC4B,CAASvC,EAAQK,EAAQyB,GAElC,KAAK/I,EACL,KAAKK,EACH,OAAO,IAAI2I,EAAK/B,GAElB,KAAK9G,EACH,OAhQN,SAAqBsJ,GACnB,IAAIhH,EAAS,IAAIgH,EAAO7B,YAAY6B,EAAO1B,OAAQ5G,EAAQsC,KAAKgG,IAEhE,OADAhH,EAAOiH,UAAYD,EAAOC,UACnBjH,EA6PIkH,CAAY1C,GAErB,KAAK7G,EACH,OApPN,SAAkB4B,EAAKsF,EAAQyB,GAE7B,OAAO5G,EADKmF,EAASyB,EAAU7F,EAAWlB,IAAM,GAAQkB,EAAWlB,GACzCC,EAAa,IAAID,EAAI4F,aAkPpCgC,CAAS3C,EAAQK,EAAQyB,GAElC,KAAKzI,EACH,OA3OeuJ,EA2OI5C,EA1OhBvB,GAAgB3I,OAAO2I,GAAcvG,KAAK0K,IAAW,GAD9D,IAAqBA,EA/LNC,CAAepN,EAAOuL,EAAKZ,GAAWC,IAInDG,IAAUA,EAAQ,IAAIvB,IACtB,IAAI6D,EAAUtC,EAAM5I,IAAInC,GACxB,GAAIqN,EACF,OAAOA,EAIT,GAFAtC,EAAMzF,IAAItF,EAAO+F,IAEZkF,EACH,IAAIqC,EAAQzC,EAsQhB,SAAoBN,GAClB,OAnOF,SAAwBA,EAAQgD,EAAUC,GACxC,IAAIzH,EAASwH,EAAShD,GACtB,OAAOX,GAAQW,GAAUxE,EApwB3B,SAAmBL,EAAO+H,GAKxB,IAJA,IAAInO,GAAS,EACTyC,EAAS0L,EAAO1L,OAChB2L,EAAShI,EAAM3D,SAEVzC,EAAQyC,GACf2D,EAAMgI,EAASpO,GAASmO,EAAOnO,GAEjC,OAAOoG,EA4vB2BiI,CAAU5H,EAAQyH,EAAYjD,IAiOzDqD,CAAerD,EAAQvD,GAAMkF,IAvQb2B,CAAW7N,GAASgH,GAAKhH,GAUhD,OA5vBF,SAAmB0F,EAAOC,GAIxB,IAHA,IAAIrG,GAAS,EACTyC,EAAS2D,EAAQA,EAAM3D,OAAS,IAE3BzC,EAAQyC,IAC8B,IAAzC4D,EAASD,EAAMpG,GAAQA,EAAOoG,MA+uBpCoI,CAAUR,GAAStN,GAAO,SAAS+N,EAAUxN,GACvC+M,IAEFS,EAAW/N,EADXO,EAAMwN,IAIRzD,GAAYvE,EAAQxF,EAAKoK,GAAUoD,EAAUnD,EAAQC,EAAQC,EAAYvK,EAAKP,EAAO+K,OAEhFhF,EAkDT,SAASiI,GAAahO,GACpB,SAAKgL,GAAShL,KAyYEqG,EAzYiBrG,EA0YxB8G,GAAeA,KAAcT,MAvYvB4H,GAAWjO,IAAU8F,EAAa9F,GAAUoH,GAAa1C,GACzDxE,KAAKwI,GAAS1I,IAqY/B,IAAkBqG,EAtVlB,SAASkG,GAAiB2B,GACxB,IAAInI,EAAS,IAAImI,EAAYhD,YAAYgD,EAAYxB,YAErD,OADA,IAAInF,GAAWxB,GAAQT,IAAI,IAAIiC,GAAW2G,IACnCnI,EA8GT,SAASkG,GAAWZ,EAAQiC,EAAO/C,EAAQO,GACzCP,IAAWA,EAAS,IAKpB,IAHA,IAAIjL,GAAS,EACTyC,EAASuL,EAAMvL,SAEVzC,EAAQyC,GAAQ,CACvB,IAAIxB,EAAM+M,EAAMhO,GAEZ6O,EAAWrD,EACXA,EAAWP,EAAOhK,GAAM8K,EAAO9K,GAAMA,EAAKgK,EAAQc,QAClD1I,EAEJ2H,GAAYC,EAAQhK,OAAkBoC,IAAbwL,EAAyB9C,EAAO9K,GAAO4N,GAElE,OAAO5D,EAkCT,SAAS6D,GAAW/O,EAAKkB,GACvB,IAqKiBP,EACbqO,EAtKAlN,EAAO9B,EAAIoK,SACf,OAsKgB,WADZ4E,SADarO,EApKAO,KAsKmB,UAAR8N,GAA4B,UAARA,GAA4B,WAARA,EACrD,cAAVrO,EACU,OAAVA,GAvKDmB,EAAmB,iBAAPZ,EAAkB,SAAW,QACzCY,EAAK9B,IAWX,SAAS8I,GAAUoC,EAAQhK,GACzB,IAAIP,EAj8BN,SAAkBuK,EAAQhK,GACxB,OAAiB,MAAVgK,OAAiB5H,EAAY4H,EAAOhK,GAg8B/B+N,CAAS/D,EAAQhK,GAC7B,OAAOyN,GAAahO,GAASA,OAAQ2C,EA7tBvCuG,GAAK3G,UAAU6G,MAnEf,WACED,KAAKM,SAAWjB,GAAeA,GAAa,MAAQ,IAmEtDU,GAAK3G,UAAkB,OAtDvB,SAAoBhC,GAClB,OAAO4I,KAAKoF,IAAIhO,WAAe4I,KAAKM,SAASlJ,IAsD/C2I,GAAK3G,UAAUJ,IA1Cf,SAAiB5B,GACf,IAAIY,EAAOgI,KAAKM,SAChB,GAAIjB,GAAc,CAChB,IAAIzC,EAAS5E,EAAKZ,GAClB,OAAOwF,IAAWjD,OAAiBH,EAAYoD,EAEjD,OAAOvD,GAAeC,KAAKtB,EAAMZ,GAAOY,EAAKZ,QAAOoC,GAqCtDuG,GAAK3G,UAAUgM,IAzBf,SAAiBhO,GACf,IAAIY,EAAOgI,KAAKM,SAChB,OAAOjB,QAA6B7F,IAAdxB,EAAKZ,GAAqBiC,GAAeC,KAAKtB,EAAMZ,IAwB5E2I,GAAK3G,UAAU+C,IAXf,SAAiB/E,EAAKP,GAGpB,OAFWmJ,KAAKM,SACXlJ,GAAQiI,SAA0B7F,IAAV3C,EAAuB8C,EAAiB9C,EAC9DmJ,MAoHTG,GAAU/G,UAAU6G,MAjFpB,WACED,KAAKM,SAAW,IAiFlBH,GAAU/G,UAAkB,OArE5B,SAAyBhC,GACvB,IAAIY,EAAOgI,KAAKM,SACZnK,EAAQoL,GAAavJ,EAAMZ,GAE/B,QAAIjB,EAAQ,KAIRA,GADY6B,EAAKY,OAAS,EAE5BZ,EAAKqN,MAEL5G,GAAOnF,KAAKtB,EAAM7B,EAAO,IAEpB,IAyDTgK,GAAU/G,UAAUJ,IA7CpB,SAAsB5B,GACpB,IAAIY,EAAOgI,KAAKM,SACZnK,EAAQoL,GAAavJ,EAAMZ,GAE/B,OAAOjB,EAAQ,OAAIqD,EAAYxB,EAAK7B,GAAO,IA0C7CgK,GAAU/G,UAAUgM,IA9BpB,SAAsBhO,GACpB,OAAOmK,GAAavB,KAAKM,SAAUlJ,IAAQ,GA8B7C+I,GAAU/G,UAAU+C,IAjBpB,SAAsB/E,EAAKP,GACzB,IAAImB,EAAOgI,KAAKM,SACZnK,EAAQoL,GAAavJ,EAAMZ,GAO/B,OALIjB,EAAQ,EACV6B,EAAKU,KAAK,CAACtB,EAAKP,IAEhBmB,EAAK7B,GAAO,GAAKU,EAEZmJ,MAkGTI,GAAShH,UAAU6G,MA/DnB,WACED,KAAKM,SAAW,CACd,KAAQ,IAAIP,GACZ,IAAO,IAAKd,IAAOkB,IACnB,OAAU,IAAIJ,KA4DlBK,GAAShH,UAAkB,OA/C3B,SAAwBhC,GACtB,OAAO6N,GAAWjF,KAAM5I,GAAa,OAAEA,IA+CzCgJ,GAAShH,UAAUJ,IAnCnB,SAAqB5B,GACnB,OAAO6N,GAAWjF,KAAM5I,GAAK4B,IAAI5B,IAmCnCgJ,GAAShH,UAAUgM,IAvBnB,SAAqBhO,GACnB,OAAO6N,GAAWjF,KAAM5I,GAAKgO,IAAIhO,IAuBnCgJ,GAAShH,UAAU+C,IAVnB,SAAqB/E,EAAKP,GAExB,OADAoO,GAAWjF,KAAM5I,GAAK+E,IAAI/E,EAAKP,GACxBmJ,MAgGTK,GAAMjH,UAAU6G,MApEhB,WACED,KAAKM,SAAW,IAAIH,IAoEtBE,GAAMjH,UAAkB,OAxDxB,SAAqBhC,GACnB,OAAO4I,KAAKM,SAAiB,OAAElJ,IAwDjCiJ,GAAMjH,UAAUJ,IA5ChB,SAAkB5B,GAChB,OAAO4I,KAAKM,SAAStH,IAAI5B,IA4C3BiJ,GAAMjH,UAAUgM,IAhChB,SAAkBhO,GAChB,OAAO4I,KAAKM,SAAS8E,IAAIhO,IAgC3BiJ,GAAMjH,UAAU+C,IAnBhB,SAAkB/E,EAAKP,GACrB,IAAIyO,EAAQtF,KAAKM,SACjB,GAAIgF,aAAiBnF,GAAW,CAC9B,IAAIoF,EAAQD,EAAMhF,SAClB,IAAKrB,IAAQsG,EAAM3M,OAAS4M,IAE1B,OADAD,EAAM7M,KAAK,CAACtB,EAAKP,IACVmJ,KAETsF,EAAQtF,KAAKM,SAAW,IAAIF,GAASmF,GAGvC,OADAD,EAAMnJ,IAAI/E,EAAKP,GACRmJ,MAicT,IAAI+C,GAAarE,GAAmBzB,EAAQyB,GAAkBxH,QAyhB9D,WACE,MAAO,IAjhBLmL,GAtQJ,SAAoBxL,GAClB,OAAOmH,GAAe1E,KAAKzC,IAyX7B,SAASqK,GAAQrK,EAAO+B,GAEtB,SADAA,EAAmB,MAAVA,EAAiBgB,EAAmBhB,KAE1B,iBAAT/B,GAAqB2E,EAASzE,KAAKF,KAC1CA,GAAS,GAAKA,EAAQ,GAAK,GAAKA,EAAQ+B,EAmC7C,SAAS+J,GAAY9L,GACnB,IAAIsM,EAAOtM,GAASA,EAAMkL,YAG1B,OAAOlL,KAFqB,mBAARsM,GAAsBA,EAAK/J,WAAcqE,GAY/D,SAAS8B,GAASrC,GAChB,GAAY,MAARA,EAAc,CAChB,IACE,OAAOa,EAAazE,KAAK4D,GACzB,MAAOJ,IACT,IACE,OAAQI,EAAO,GACf,MAAOJ,KAEX,MAAO,GAyDT,SAASwE,GAAGzK,EAAO4O,GACjB,OAAO5O,IAAU4O,GAAU5O,GAAUA,GAAS4O,GAAUA,GAxOrD1G,IAAYsD,GAAO,IAAItD,GAAS,IAAI2G,YAAY,MAAQ9K,GACxDqE,IAAOoD,GAAO,IAAIpD,KAAQ/E,GAC1BgF,IAAWmD,GAAOnD,GAAQyG,YAActL,GACxC8E,IAAOkD,GAAO,IAAIlD,KAAQ5E,GAC1B6E,IAAWiD,GAAO,IAAIjD,KAAY1E,KACrC2H,GAAS,SAASxL,GAChB,IAAI+F,EAASoB,GAAe1E,KAAKzC,GAC7BsM,EAAOvG,GAAUxC,EAAYvD,EAAMkL,iBAAcvI,EACjDoM,EAAazC,EAAO5D,GAAS4D,QAAQ3J,EAEzC,GAAIoM,EACF,OAAQA,GACN,KAAKtG,GAAoB,OAAO1E,EAChC,KAAK4E,GAAe,OAAOtF,EAC3B,KAAKuF,GAAmB,OAAOpF,EAC/B,KAAKqF,GAAe,OAAOnF,EAC3B,KAAKoF,GAAmB,OAAOjF,EAGnC,OAAOkC,IAuQX,IAAI6D,GAAUzJ,MAAMyJ,QA2BpB,SAASE,GAAY9J,GACnB,OAAgB,MAATA,GAqGT,SAAkBA,GAChB,MAAuB,iBAATA,GACZA,GAAS,GAAKA,EAAQ,GAAK,GAAKA,GAAS+C,EAvGnBiM,CAAShP,EAAM+B,UAAYkM,GAAWjO,GAiDhE,IAAIgI,GAAWD,IAsLf,WACE,OAAO,GApKT,SAASkG,GAAWjO,GAGlB,IAAIuL,EAAMP,GAAShL,GAASmH,GAAe1E,KAAKzC,GAAS,GACzD,OAAOuL,GAAOpI,GAAWoI,GAAOnI,EA2DlC,SAAS4H,GAAShL,GAChB,IAAIqO,SAAcrO,EAClB,QAASA,IAAkB,UAARqO,GAA4B,YAARA,GA2DzC,SAASrH,GAAKuD,GACZ,OAAOT,GAAYS,GAAUb,GAAca,GAtuB7C,SAAkBA,GAChB,IAAKuB,GAAYvB,GACf,OAAOtC,GAAWsC,GAEpB,IAAIxE,EAAS,GACb,IAAK,IAAIxF,KAAOF,OAAOkK,GACjB/H,GAAeC,KAAK8H,EAAQhK,IAAe,eAAPA,GACtCwF,EAAOlE,KAAKtB,GAGhB,OAAOwF,EA4tB8CkJ,CAAS1E,GA0ChE7L,EAAOD,QA9VP,SAAmBuB,GACjB,OAAO2K,GAAU3K,GAAO,GAAM,K,yh80HCr3C5BkP,EAA2B,GAG/B,SAASC,EAAoBC,GAE5B,GAAGF,EAAyBE,GAC3B,OAAOF,EAAyBE,GAAU3Q,QAG3C,IAAIC,EAASwQ,EAAyBE,GAAY,CACjDC,GAAID,EACJE,QAAQ,EACR7Q,QAAS,IAUV,OANA8Q,EAAoBH,GAAU1Q,EAAQA,EAAOD,QAAS0Q,GAGtDzQ,EAAO4Q,QAAS,EAGT5Q,EAAOD,QCpBf,OCHA0Q,EAAoBK,IAAO9Q,IAC1BA,EAAO+Q,MAAQ,GACV/Q,EAAOgR,WAAUhR,EAAOgR,SAAW,IACjChR,GDADyQ,EAAoB,M", "file": "index.js", "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"user-agents\", [], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"user-agents\"] = factory();\n\telse\n\t\troot[\"user-agents\"] = factory();\n})(global, function() {\nreturn ", "import UserAgent from './user-agent';\n\n\nexport default UserAgent;\n", "import cloneDeep from 'lodash.clonedeep';\n\nimport userAgents from './user-agents.json';\n\n\n// Normalizes the total weight to 1 and constructs a cumulative distribution.\nconst makeCumulativeWeightIndexPairs = (weightIndexPairs) => {\n  const totalWeight = weightIndexPairs.reduce((sum, [weight]) => sum + weight, 0);\n  let sum = 0;\n  return weightIndexPairs.map(([weight, index]) => {\n    sum += weight / totalWeight;\n    return [sum, index];\n  });\n};\n\n// Precompute these so that we can quickly generate unfiltered user agents.\nconst defaultWeightIndexPairs = userAgents.map(({ weight }, index) => [weight, index]);\nconst defaultCumulativeWeightIndexPairs = makeCumulativeWeightIndexPairs(defaultWeightIndexPairs);\n\n\n// Turn the various filter formats into a single filter function that acts on raw user agents.\nconst constructFilter = (filters, accessor = parentObject => parentObject) => {\n  let childFilters;\n  if (typeof filters === 'function') {\n    childFilters = [filters];\n  } else if (filters instanceof RegExp) {\n    childFilters = [\n      value => (\n        typeof value === 'object' && value && value.userAgent\n          ? filters.test(value.userAgent)\n          : filters.test(value)\n      ),\n    ];\n  } else if (filters instanceof Array) {\n    childFilters = filters.map(childFilter => constructFilter(childFilter));\n  } else if (typeof filters === 'object') {\n    childFilters = Object.entries(filters).map(([key, valueFilter]) => (\n      constructFilter(valueFilter, parentObject => parentObject[key])\n    ));\n  } else {\n    childFilters = [\n      value => (\n        typeof value === 'object' && value && value.userAgent\n          ? filters === value.userAgent\n          : filters === value\n      ),\n    ];\n  }\n\n  return (parentObject) => {\n    try {\n      const value = accessor(parentObject);\n      return childFilters.every(childFilter => childFilter(value));\n    } catch (error) {\n      // This happens when a user-agent lacks a nested property.\n      return false;\n    }\n  };\n};\n\n\n// Construct normalized cumulative weight index pairs given the filters.\nconst constructCumulativeWeightIndexPairsFromFilters = (filters) => {\n  if (!filters) {\n    return defaultCumulativeWeightIndexPairs;\n  }\n\n  const filter = constructFilter(filters);\n\n  const weightIndexPairs = [];\n  userAgents.forEach((rawUserAgent, index) => {\n    if (filter(rawUserAgent)) {\n      weightIndexPairs.push([rawUserAgent.weight, index]);\n    }\n  });\n  return makeCumulativeWeightIndexPairs(weightIndexPairs);\n};\n\n\nconst setCumulativeWeightIndexPairs = (userAgent, cumulativeWeightIndexPairs) => {\n  Object.defineProperty(userAgent, 'cumulativeWeightIndexPairs', {\n    configurable: true,\n    enumerable: false,\n    writable: false,\n    value: cumulativeWeightIndexPairs,\n  });\n};\n\n\nexport default class UserAgent extends Function {\n  constructor(filters) {\n    super();\n    setCumulativeWeightIndexPairs(this, constructCumulativeWeightIndexPairsFromFilters(filters));\n    if (this.cumulativeWeightIndexPairs.length === 0) {\n      throw new Error('No user agents matched your filters.');\n    }\n\n    this.randomize();\n\n    return new Proxy(this, {\n      apply: () => this.random(),\n      get: (target, property, receiver) => {\n        const dataCandidate = target.data && typeof property === 'string'\n          && Object.prototype.hasOwnProperty.call(target.data, property)\n          && Object.prototype.propertyIsEnumerable.call(target.data, property);\n        if (dataCandidate) {\n          const value = target.data[property];\n          if (value !== undefined) {\n            return value;\n          }\n        }\n\n        return Reflect.get(target, property, receiver);\n      },\n    });\n  }\n\n  static random = (filters) => {\n    try {\n      return new UserAgent(filters);\n    } catch (error) {\n      return null;\n    }\n  };\n\n  //\n  // Standard Object Methods\n  //\n\n  [Symbol.toPrimitive] = () => (\n    this.data.userAgent\n  );\n\n  toString = () => (\n    this.data.userAgent\n  );\n\n  random = () => {\n    const userAgent = new UserAgent();\n    setCumulativeWeightIndexPairs(userAgent, this.cumulativeWeightIndexPairs);\n    userAgent.randomize();\n    return userAgent;\n  };\n\n  randomize = () => {\n    // Find a random raw random user agent.\n    const randomNumber = Math.random();\n    const [, index] = this.cumulativeWeightIndexPairs\n      .find(([cumulativeWeight]) => cumulativeWeight > randomNumber);\n    const rawUserAgent = userAgents[index];\n\n    this.data = cloneDeep(rawUserAgent);\n  }\n}\n", "/**\n * lodash (Custom Build) <https://lodash.com/>\n * Build: `lodash modularize exports=\"npm\" -o ./`\n * Copyright jQuery Foundation and other contributors <https://jquery.org/>\n * Released under MIT license <https://lodash.com/license>\n * Based on Underscore.js 1.8.3 <http://underscorejs.org/LICENSE>\n * Copyright <PERSON>, DocumentCloud and Investigative Reporters & Editors\n */\n\n/** Used as the size to enable large array optimizations. */\nvar LARGE_ARRAY_SIZE = 200;\n\n/** Used to stand-in for `undefined` hash values. */\nvar HASH_UNDEFINED = '__lodash_hash_undefined__';\n\n/** Used as references for various `Number` constants. */\nvar MAX_SAFE_INTEGER = 9007199254740991;\n\n/** `Object#toString` result references. */\nvar argsTag = '[object Arguments]',\n    arrayTag = '[object Array]',\n    boolTag = '[object Boolean]',\n    dateTag = '[object Date]',\n    errorTag = '[object Error]',\n    funcTag = '[object Function]',\n    genTag = '[object GeneratorFunction]',\n    mapTag = '[object Map]',\n    numberTag = '[object Number]',\n    objectTag = '[object Object]',\n    promiseTag = '[object Promise]',\n    regexpTag = '[object RegExp]',\n    setTag = '[object Set]',\n    stringTag = '[object String]',\n    symbolTag = '[object Symbol]',\n    weakMapTag = '[object WeakMap]';\n\nvar arrayBufferTag = '[object ArrayBuffer]',\n    dataViewTag = '[object DataView]',\n    float32Tag = '[object Float32Array]',\n    float64Tag = '[object Float64Array]',\n    int8Tag = '[object Int8Array]',\n    int16Tag = '[object Int16Array]',\n    int32Tag = '[object Int32Array]',\n    uint8Tag = '[object Uint8Array]',\n    uint8ClampedTag = '[object Uint8ClampedArray]',\n    uint16Tag = '[object Uint16Array]',\n    uint32Tag = '[object Uint32Array]';\n\n/**\n * Used to match `RegExp`\n * [syntax characters](http://ecma-international.org/ecma-262/7.0/#sec-patterns).\n */\nvar reRegExpChar = /[\\\\^$.*+?()[\\]{}|]/g;\n\n/** Used to match `RegExp` flags from their coerced string values. */\nvar reFlags = /\\w*$/;\n\n/** Used to detect host constructors (Safari). */\nvar reIsHostCtor = /^\\[object .+?Constructor\\]$/;\n\n/** Used to detect unsigned integer values. */\nvar reIsUint = /^(?:0|[1-9]\\d*)$/;\n\n/** Used to identify `toStringTag` values supported by `_.clone`. */\nvar cloneableTags = {};\ncloneableTags[argsTag] = cloneableTags[arrayTag] =\ncloneableTags[arrayBufferTag] = cloneableTags[dataViewTag] =\ncloneableTags[boolTag] = cloneableTags[dateTag] =\ncloneableTags[float32Tag] = cloneableTags[float64Tag] =\ncloneableTags[int8Tag] = cloneableTags[int16Tag] =\ncloneableTags[int32Tag] = cloneableTags[mapTag] =\ncloneableTags[numberTag] = cloneableTags[objectTag] =\ncloneableTags[regexpTag] = cloneableTags[setTag] =\ncloneableTags[stringTag] = cloneableTags[symbolTag] =\ncloneableTags[uint8Tag] = cloneableTags[uint8ClampedTag] =\ncloneableTags[uint16Tag] = cloneableTags[uint32Tag] = true;\ncloneableTags[errorTag] = cloneableTags[funcTag] =\ncloneableTags[weakMapTag] = false;\n\n/** Detect free variable `global` from Node.js. */\nvar freeGlobal = typeof global == 'object' && global && global.Object === Object && global;\n\n/** Detect free variable `self`. */\nvar freeSelf = typeof self == 'object' && self && self.Object === Object && self;\n\n/** Used as a reference to the global object. */\nvar root = freeGlobal || freeSelf || Function('return this')();\n\n/** Detect free variable `exports`. */\nvar freeExports = typeof exports == 'object' && exports && !exports.nodeType && exports;\n\n/** Detect free variable `module`. */\nvar freeModule = freeExports && typeof module == 'object' && module && !module.nodeType && module;\n\n/** Detect the popular CommonJS extension `module.exports`. */\nvar moduleExports = freeModule && freeModule.exports === freeExports;\n\n/**\n * Adds the key-value `pair` to `map`.\n *\n * @private\n * @param {Object} map The map to modify.\n * @param {Array} pair The key-value pair to add.\n * @returns {Object} Returns `map`.\n */\nfunction addMapEntry(map, pair) {\n  // Don't return `map.set` because it's not chainable in IE 11.\n  map.set(pair[0], pair[1]);\n  return map;\n}\n\n/**\n * Adds `value` to `set`.\n *\n * @private\n * @param {Object} set The set to modify.\n * @param {*} value The value to add.\n * @returns {Object} Returns `set`.\n */\nfunction addSetEntry(set, value) {\n  // Don't return `set.add` because it's not chainable in IE 11.\n  set.add(value);\n  return set;\n}\n\n/**\n * A specialized version of `_.forEach` for arrays without support for\n * iteratee shorthands.\n *\n * @private\n * @param {Array} [array] The array to iterate over.\n * @param {Function} iteratee The function invoked per iteration.\n * @returns {Array} Returns `array`.\n */\nfunction arrayEach(array, iteratee) {\n  var index = -1,\n      length = array ? array.length : 0;\n\n  while (++index < length) {\n    if (iteratee(array[index], index, array) === false) {\n      break;\n    }\n  }\n  return array;\n}\n\n/**\n * Appends the elements of `values` to `array`.\n *\n * @private\n * @param {Array} array The array to modify.\n * @param {Array} values The values to append.\n * @returns {Array} Returns `array`.\n */\nfunction arrayPush(array, values) {\n  var index = -1,\n      length = values.length,\n      offset = array.length;\n\n  while (++index < length) {\n    array[offset + index] = values[index];\n  }\n  return array;\n}\n\n/**\n * A specialized version of `_.reduce` for arrays without support for\n * iteratee shorthands.\n *\n * @private\n * @param {Array} [array] The array to iterate over.\n * @param {Function} iteratee The function invoked per iteration.\n * @param {*} [accumulator] The initial value.\n * @param {boolean} [initAccum] Specify using the first element of `array` as\n *  the initial value.\n * @returns {*} Returns the accumulated value.\n */\nfunction arrayReduce(array, iteratee, accumulator, initAccum) {\n  var index = -1,\n      length = array ? array.length : 0;\n\n  if (initAccum && length) {\n    accumulator = array[++index];\n  }\n  while (++index < length) {\n    accumulator = iteratee(accumulator, array[index], index, array);\n  }\n  return accumulator;\n}\n\n/**\n * The base implementation of `_.times` without support for iteratee shorthands\n * or max array length checks.\n *\n * @private\n * @param {number} n The number of times to invoke `iteratee`.\n * @param {Function} iteratee The function invoked per iteration.\n * @returns {Array} Returns the array of results.\n */\nfunction baseTimes(n, iteratee) {\n  var index = -1,\n      result = Array(n);\n\n  while (++index < n) {\n    result[index] = iteratee(index);\n  }\n  return result;\n}\n\n/**\n * Gets the value at `key` of `object`.\n *\n * @private\n * @param {Object} [object] The object to query.\n * @param {string} key The key of the property to get.\n * @returns {*} Returns the property value.\n */\nfunction getValue(object, key) {\n  return object == null ? undefined : object[key];\n}\n\n/**\n * Checks if `value` is a host object in IE < 9.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a host object, else `false`.\n */\nfunction isHostObject(value) {\n  // Many host objects are `Object` objects that can coerce to strings\n  // despite having improperly defined `toString` methods.\n  var result = false;\n  if (value != null && typeof value.toString != 'function') {\n    try {\n      result = !!(value + '');\n    } catch (e) {}\n  }\n  return result;\n}\n\n/**\n * Converts `map` to its key-value pairs.\n *\n * @private\n * @param {Object} map The map to convert.\n * @returns {Array} Returns the key-value pairs.\n */\nfunction mapToArray(map) {\n  var index = -1,\n      result = Array(map.size);\n\n  map.forEach(function(value, key) {\n    result[++index] = [key, value];\n  });\n  return result;\n}\n\n/**\n * Creates a unary function that invokes `func` with its argument transformed.\n *\n * @private\n * @param {Function} func The function to wrap.\n * @param {Function} transform The argument transform.\n * @returns {Function} Returns the new function.\n */\nfunction overArg(func, transform) {\n  return function(arg) {\n    return func(transform(arg));\n  };\n}\n\n/**\n * Converts `set` to an array of its values.\n *\n * @private\n * @param {Object} set The set to convert.\n * @returns {Array} Returns the values.\n */\nfunction setToArray(set) {\n  var index = -1,\n      result = Array(set.size);\n\n  set.forEach(function(value) {\n    result[++index] = value;\n  });\n  return result;\n}\n\n/** Used for built-in method references. */\nvar arrayProto = Array.prototype,\n    funcProto = Function.prototype,\n    objectProto = Object.prototype;\n\n/** Used to detect overreaching core-js shims. */\nvar coreJsData = root['__core-js_shared__'];\n\n/** Used to detect methods masquerading as native. */\nvar maskSrcKey = (function() {\n  var uid = /[^.]+$/.exec(coreJsData && coreJsData.keys && coreJsData.keys.IE_PROTO || '');\n  return uid ? ('Symbol(src)_1.' + uid) : '';\n}());\n\n/** Used to resolve the decompiled source of functions. */\nvar funcToString = funcProto.toString;\n\n/** Used to check objects for own properties. */\nvar hasOwnProperty = objectProto.hasOwnProperty;\n\n/**\n * Used to resolve the\n * [`toStringTag`](http://ecma-international.org/ecma-262/7.0/#sec-object.prototype.tostring)\n * of values.\n */\nvar objectToString = objectProto.toString;\n\n/** Used to detect if a method is native. */\nvar reIsNative = RegExp('^' +\n  funcToString.call(hasOwnProperty).replace(reRegExpChar, '\\\\$&')\n  .replace(/hasOwnProperty|(function).*?(?=\\\\\\()| for .+?(?=\\\\\\])/g, '$1.*?') + '$'\n);\n\n/** Built-in value references. */\nvar Buffer = moduleExports ? root.Buffer : undefined,\n    Symbol = root.Symbol,\n    Uint8Array = root.Uint8Array,\n    getPrototype = overArg(Object.getPrototypeOf, Object),\n    objectCreate = Object.create,\n    propertyIsEnumerable = objectProto.propertyIsEnumerable,\n    splice = arrayProto.splice;\n\n/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeGetSymbols = Object.getOwnPropertySymbols,\n    nativeIsBuffer = Buffer ? Buffer.isBuffer : undefined,\n    nativeKeys = overArg(Object.keys, Object);\n\n/* Built-in method references that are verified to be native. */\nvar DataView = getNative(root, 'DataView'),\n    Map = getNative(root, 'Map'),\n    Promise = getNative(root, 'Promise'),\n    Set = getNative(root, 'Set'),\n    WeakMap = getNative(root, 'WeakMap'),\n    nativeCreate = getNative(Object, 'create');\n\n/** Used to detect maps, sets, and weakmaps. */\nvar dataViewCtorString = toSource(DataView),\n    mapCtorString = toSource(Map),\n    promiseCtorString = toSource(Promise),\n    setCtorString = toSource(Set),\n    weakMapCtorString = toSource(WeakMap);\n\n/** Used to convert symbols to primitives and strings. */\nvar symbolProto = Symbol ? Symbol.prototype : undefined,\n    symbolValueOf = symbolProto ? symbolProto.valueOf : undefined;\n\n/**\n * Creates a hash object.\n *\n * @private\n * @constructor\n * @param {Array} [entries] The key-value pairs to cache.\n */\nfunction Hash(entries) {\n  var index = -1,\n      length = entries ? entries.length : 0;\n\n  this.clear();\n  while (++index < length) {\n    var entry = entries[index];\n    this.set(entry[0], entry[1]);\n  }\n}\n\n/**\n * Removes all key-value entries from the hash.\n *\n * @private\n * @name clear\n * @memberOf Hash\n */\nfunction hashClear() {\n  this.__data__ = nativeCreate ? nativeCreate(null) : {};\n}\n\n/**\n * Removes `key` and its value from the hash.\n *\n * @private\n * @name delete\n * @memberOf Hash\n * @param {Object} hash The hash to modify.\n * @param {string} key The key of the value to remove.\n * @returns {boolean} Returns `true` if the entry was removed, else `false`.\n */\nfunction hashDelete(key) {\n  return this.has(key) && delete this.__data__[key];\n}\n\n/**\n * Gets the hash value for `key`.\n *\n * @private\n * @name get\n * @memberOf Hash\n * @param {string} key The key of the value to get.\n * @returns {*} Returns the entry value.\n */\nfunction hashGet(key) {\n  var data = this.__data__;\n  if (nativeCreate) {\n    var result = data[key];\n    return result === HASH_UNDEFINED ? undefined : result;\n  }\n  return hasOwnProperty.call(data, key) ? data[key] : undefined;\n}\n\n/**\n * Checks if a hash value for `key` exists.\n *\n * @private\n * @name has\n * @memberOf Hash\n * @param {string} key The key of the entry to check.\n * @returns {boolean} Returns `true` if an entry for `key` exists, else `false`.\n */\nfunction hashHas(key) {\n  var data = this.__data__;\n  return nativeCreate ? data[key] !== undefined : hasOwnProperty.call(data, key);\n}\n\n/**\n * Sets the hash `key` to `value`.\n *\n * @private\n * @name set\n * @memberOf Hash\n * @param {string} key The key of the value to set.\n * @param {*} value The value to set.\n * @returns {Object} Returns the hash instance.\n */\nfunction hashSet(key, value) {\n  var data = this.__data__;\n  data[key] = (nativeCreate && value === undefined) ? HASH_UNDEFINED : value;\n  return this;\n}\n\n// Add methods to `Hash`.\nHash.prototype.clear = hashClear;\nHash.prototype['delete'] = hashDelete;\nHash.prototype.get = hashGet;\nHash.prototype.has = hashHas;\nHash.prototype.set = hashSet;\n\n/**\n * Creates an list cache object.\n *\n * @private\n * @constructor\n * @param {Array} [entries] The key-value pairs to cache.\n */\nfunction ListCache(entries) {\n  var index = -1,\n      length = entries ? entries.length : 0;\n\n  this.clear();\n  while (++index < length) {\n    var entry = entries[index];\n    this.set(entry[0], entry[1]);\n  }\n}\n\n/**\n * Removes all key-value entries from the list cache.\n *\n * @private\n * @name clear\n * @memberOf ListCache\n */\nfunction listCacheClear() {\n  this.__data__ = [];\n}\n\n/**\n * Removes `key` and its value from the list cache.\n *\n * @private\n * @name delete\n * @memberOf ListCache\n * @param {string} key The key of the value to remove.\n * @returns {boolean} Returns `true` if the entry was removed, else `false`.\n */\nfunction listCacheDelete(key) {\n  var data = this.__data__,\n      index = assocIndexOf(data, key);\n\n  if (index < 0) {\n    return false;\n  }\n  var lastIndex = data.length - 1;\n  if (index == lastIndex) {\n    data.pop();\n  } else {\n    splice.call(data, index, 1);\n  }\n  return true;\n}\n\n/**\n * Gets the list cache value for `key`.\n *\n * @private\n * @name get\n * @memberOf ListCache\n * @param {string} key The key of the value to get.\n * @returns {*} Returns the entry value.\n */\nfunction listCacheGet(key) {\n  var data = this.__data__,\n      index = assocIndexOf(data, key);\n\n  return index < 0 ? undefined : data[index][1];\n}\n\n/**\n * Checks if a list cache value for `key` exists.\n *\n * @private\n * @name has\n * @memberOf ListCache\n * @param {string} key The key of the entry to check.\n * @returns {boolean} Returns `true` if an entry for `key` exists, else `false`.\n */\nfunction listCacheHas(key) {\n  return assocIndexOf(this.__data__, key) > -1;\n}\n\n/**\n * Sets the list cache `key` to `value`.\n *\n * @private\n * @name set\n * @memberOf ListCache\n * @param {string} key The key of the value to set.\n * @param {*} value The value to set.\n * @returns {Object} Returns the list cache instance.\n */\nfunction listCacheSet(key, value) {\n  var data = this.__data__,\n      index = assocIndexOf(data, key);\n\n  if (index < 0) {\n    data.push([key, value]);\n  } else {\n    data[index][1] = value;\n  }\n  return this;\n}\n\n// Add methods to `ListCache`.\nListCache.prototype.clear = listCacheClear;\nListCache.prototype['delete'] = listCacheDelete;\nListCache.prototype.get = listCacheGet;\nListCache.prototype.has = listCacheHas;\nListCache.prototype.set = listCacheSet;\n\n/**\n * Creates a map cache object to store key-value pairs.\n *\n * @private\n * @constructor\n * @param {Array} [entries] The key-value pairs to cache.\n */\nfunction MapCache(entries) {\n  var index = -1,\n      length = entries ? entries.length : 0;\n\n  this.clear();\n  while (++index < length) {\n    var entry = entries[index];\n    this.set(entry[0], entry[1]);\n  }\n}\n\n/**\n * Removes all key-value entries from the map.\n *\n * @private\n * @name clear\n * @memberOf MapCache\n */\nfunction mapCacheClear() {\n  this.__data__ = {\n    'hash': new Hash,\n    'map': new (Map || ListCache),\n    'string': new Hash\n  };\n}\n\n/**\n * Removes `key` and its value from the map.\n *\n * @private\n * @name delete\n * @memberOf MapCache\n * @param {string} key The key of the value to remove.\n * @returns {boolean} Returns `true` if the entry was removed, else `false`.\n */\nfunction mapCacheDelete(key) {\n  return getMapData(this, key)['delete'](key);\n}\n\n/**\n * Gets the map value for `key`.\n *\n * @private\n * @name get\n * @memberOf MapCache\n * @param {string} key The key of the value to get.\n * @returns {*} Returns the entry value.\n */\nfunction mapCacheGet(key) {\n  return getMapData(this, key).get(key);\n}\n\n/**\n * Checks if a map value for `key` exists.\n *\n * @private\n * @name has\n * @memberOf MapCache\n * @param {string} key The key of the entry to check.\n * @returns {boolean} Returns `true` if an entry for `key` exists, else `false`.\n */\nfunction mapCacheHas(key) {\n  return getMapData(this, key).has(key);\n}\n\n/**\n * Sets the map `key` to `value`.\n *\n * @private\n * @name set\n * @memberOf MapCache\n * @param {string} key The key of the value to set.\n * @param {*} value The value to set.\n * @returns {Object} Returns the map cache instance.\n */\nfunction mapCacheSet(key, value) {\n  getMapData(this, key).set(key, value);\n  return this;\n}\n\n// Add methods to `MapCache`.\nMapCache.prototype.clear = mapCacheClear;\nMapCache.prototype['delete'] = mapCacheDelete;\nMapCache.prototype.get = mapCacheGet;\nMapCache.prototype.has = mapCacheHas;\nMapCache.prototype.set = mapCacheSet;\n\n/**\n * Creates a stack cache object to store key-value pairs.\n *\n * @private\n * @constructor\n * @param {Array} [entries] The key-value pairs to cache.\n */\nfunction Stack(entries) {\n  this.__data__ = new ListCache(entries);\n}\n\n/**\n * Removes all key-value entries from the stack.\n *\n * @private\n * @name clear\n * @memberOf Stack\n */\nfunction stackClear() {\n  this.__data__ = new ListCache;\n}\n\n/**\n * Removes `key` and its value from the stack.\n *\n * @private\n * @name delete\n * @memberOf Stack\n * @param {string} key The key of the value to remove.\n * @returns {boolean} Returns `true` if the entry was removed, else `false`.\n */\nfunction stackDelete(key) {\n  return this.__data__['delete'](key);\n}\n\n/**\n * Gets the stack value for `key`.\n *\n * @private\n * @name get\n * @memberOf Stack\n * @param {string} key The key of the value to get.\n * @returns {*} Returns the entry value.\n */\nfunction stackGet(key) {\n  return this.__data__.get(key);\n}\n\n/**\n * Checks if a stack value for `key` exists.\n *\n * @private\n * @name has\n * @memberOf Stack\n * @param {string} key The key of the entry to check.\n * @returns {boolean} Returns `true` if an entry for `key` exists, else `false`.\n */\nfunction stackHas(key) {\n  return this.__data__.has(key);\n}\n\n/**\n * Sets the stack `key` to `value`.\n *\n * @private\n * @name set\n * @memberOf Stack\n * @param {string} key The key of the value to set.\n * @param {*} value The value to set.\n * @returns {Object} Returns the stack cache instance.\n */\nfunction stackSet(key, value) {\n  var cache = this.__data__;\n  if (cache instanceof ListCache) {\n    var pairs = cache.__data__;\n    if (!Map || (pairs.length < LARGE_ARRAY_SIZE - 1)) {\n      pairs.push([key, value]);\n      return this;\n    }\n    cache = this.__data__ = new MapCache(pairs);\n  }\n  cache.set(key, value);\n  return this;\n}\n\n// Add methods to `Stack`.\nStack.prototype.clear = stackClear;\nStack.prototype['delete'] = stackDelete;\nStack.prototype.get = stackGet;\nStack.prototype.has = stackHas;\nStack.prototype.set = stackSet;\n\n/**\n * Creates an array of the enumerable property names of the array-like `value`.\n *\n * @private\n * @param {*} value The value to query.\n * @param {boolean} inherited Specify returning inherited property names.\n * @returns {Array} Returns the array of property names.\n */\nfunction arrayLikeKeys(value, inherited) {\n  // Safari 8.1 makes `arguments.callee` enumerable in strict mode.\n  // Safari 9 makes `arguments.length` enumerable in strict mode.\n  var result = (isArray(value) || isArguments(value))\n    ? baseTimes(value.length, String)\n    : [];\n\n  var length = result.length,\n      skipIndexes = !!length;\n\n  for (var key in value) {\n    if ((inherited || hasOwnProperty.call(value, key)) &&\n        !(skipIndexes && (key == 'length' || isIndex(key, length)))) {\n      result.push(key);\n    }\n  }\n  return result;\n}\n\n/**\n * Assigns `value` to `key` of `object` if the existing value is not equivalent\n * using [`SameValueZero`](http://ecma-international.org/ecma-262/7.0/#sec-samevaluezero)\n * for equality comparisons.\n *\n * @private\n * @param {Object} object The object to modify.\n * @param {string} key The key of the property to assign.\n * @param {*} value The value to assign.\n */\nfunction assignValue(object, key, value) {\n  var objValue = object[key];\n  if (!(hasOwnProperty.call(object, key) && eq(objValue, value)) ||\n      (value === undefined && !(key in object))) {\n    object[key] = value;\n  }\n}\n\n/**\n * Gets the index at which the `key` is found in `array` of key-value pairs.\n *\n * @private\n * @param {Array} array The array to inspect.\n * @param {*} key The key to search for.\n * @returns {number} Returns the index of the matched value, else `-1`.\n */\nfunction assocIndexOf(array, key) {\n  var length = array.length;\n  while (length--) {\n    if (eq(array[length][0], key)) {\n      return length;\n    }\n  }\n  return -1;\n}\n\n/**\n * The base implementation of `_.assign` without support for multiple sources\n * or `customizer` functions.\n *\n * @private\n * @param {Object} object The destination object.\n * @param {Object} source The source object.\n * @returns {Object} Returns `object`.\n */\nfunction baseAssign(object, source) {\n  return object && copyObject(source, keys(source), object);\n}\n\n/**\n * The base implementation of `_.clone` and `_.cloneDeep` which tracks\n * traversed objects.\n *\n * @private\n * @param {*} value The value to clone.\n * @param {boolean} [isDeep] Specify a deep clone.\n * @param {boolean} [isFull] Specify a clone including symbols.\n * @param {Function} [customizer] The function to customize cloning.\n * @param {string} [key] The key of `value`.\n * @param {Object} [object] The parent object of `value`.\n * @param {Object} [stack] Tracks traversed objects and their clone counterparts.\n * @returns {*} Returns the cloned value.\n */\nfunction baseClone(value, isDeep, isFull, customizer, key, object, stack) {\n  var result;\n  if (customizer) {\n    result = object ? customizer(value, key, object, stack) : customizer(value);\n  }\n  if (result !== undefined) {\n    return result;\n  }\n  if (!isObject(value)) {\n    return value;\n  }\n  var isArr = isArray(value);\n  if (isArr) {\n    result = initCloneArray(value);\n    if (!isDeep) {\n      return copyArray(value, result);\n    }\n  } else {\n    var tag = getTag(value),\n        isFunc = tag == funcTag || tag == genTag;\n\n    if (isBuffer(value)) {\n      return cloneBuffer(value, isDeep);\n    }\n    if (tag == objectTag || tag == argsTag || (isFunc && !object)) {\n      if (isHostObject(value)) {\n        return object ? value : {};\n      }\n      result = initCloneObject(isFunc ? {} : value);\n      if (!isDeep) {\n        return copySymbols(value, baseAssign(result, value));\n      }\n    } else {\n      if (!cloneableTags[tag]) {\n        return object ? value : {};\n      }\n      result = initCloneByTag(value, tag, baseClone, isDeep);\n    }\n  }\n  // Check for circular references and return its corresponding clone.\n  stack || (stack = new Stack);\n  var stacked = stack.get(value);\n  if (stacked) {\n    return stacked;\n  }\n  stack.set(value, result);\n\n  if (!isArr) {\n    var props = isFull ? getAllKeys(value) : keys(value);\n  }\n  arrayEach(props || value, function(subValue, key) {\n    if (props) {\n      key = subValue;\n      subValue = value[key];\n    }\n    // Recursively populate clone (susceptible to call stack limits).\n    assignValue(result, key, baseClone(subValue, isDeep, isFull, customizer, key, value, stack));\n  });\n  return result;\n}\n\n/**\n * The base implementation of `_.create` without support for assigning\n * properties to the created object.\n *\n * @private\n * @param {Object} prototype The object to inherit from.\n * @returns {Object} Returns the new object.\n */\nfunction baseCreate(proto) {\n  return isObject(proto) ? objectCreate(proto) : {};\n}\n\n/**\n * The base implementation of `getAllKeys` and `getAllKeysIn` which uses\n * `keysFunc` and `symbolsFunc` to get the enumerable property names and\n * symbols of `object`.\n *\n * @private\n * @param {Object} object The object to query.\n * @param {Function} keysFunc The function to get the keys of `object`.\n * @param {Function} symbolsFunc The function to get the symbols of `object`.\n * @returns {Array} Returns the array of property names and symbols.\n */\nfunction baseGetAllKeys(object, keysFunc, symbolsFunc) {\n  var result = keysFunc(object);\n  return isArray(object) ? result : arrayPush(result, symbolsFunc(object));\n}\n\n/**\n * The base implementation of `getTag`.\n *\n * @private\n * @param {*} value The value to query.\n * @returns {string} Returns the `toStringTag`.\n */\nfunction baseGetTag(value) {\n  return objectToString.call(value);\n}\n\n/**\n * The base implementation of `_.isNative` without bad shim checks.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a native function,\n *  else `false`.\n */\nfunction baseIsNative(value) {\n  if (!isObject(value) || isMasked(value)) {\n    return false;\n  }\n  var pattern = (isFunction(value) || isHostObject(value)) ? reIsNative : reIsHostCtor;\n  return pattern.test(toSource(value));\n}\n\n/**\n * The base implementation of `_.keys` which doesn't treat sparse arrays as dense.\n *\n * @private\n * @param {Object} object The object to query.\n * @returns {Array} Returns the array of property names.\n */\nfunction baseKeys(object) {\n  if (!isPrototype(object)) {\n    return nativeKeys(object);\n  }\n  var result = [];\n  for (var key in Object(object)) {\n    if (hasOwnProperty.call(object, key) && key != 'constructor') {\n      result.push(key);\n    }\n  }\n  return result;\n}\n\n/**\n * Creates a clone of  `buffer`.\n *\n * @private\n * @param {Buffer} buffer The buffer to clone.\n * @param {boolean} [isDeep] Specify a deep clone.\n * @returns {Buffer} Returns the cloned buffer.\n */\nfunction cloneBuffer(buffer, isDeep) {\n  if (isDeep) {\n    return buffer.slice();\n  }\n  var result = new buffer.constructor(buffer.length);\n  buffer.copy(result);\n  return result;\n}\n\n/**\n * Creates a clone of `arrayBuffer`.\n *\n * @private\n * @param {ArrayBuffer} arrayBuffer The array buffer to clone.\n * @returns {ArrayBuffer} Returns the cloned array buffer.\n */\nfunction cloneArrayBuffer(arrayBuffer) {\n  var result = new arrayBuffer.constructor(arrayBuffer.byteLength);\n  new Uint8Array(result).set(new Uint8Array(arrayBuffer));\n  return result;\n}\n\n/**\n * Creates a clone of `dataView`.\n *\n * @private\n * @param {Object} dataView The data view to clone.\n * @param {boolean} [isDeep] Specify a deep clone.\n * @returns {Object} Returns the cloned data view.\n */\nfunction cloneDataView(dataView, isDeep) {\n  var buffer = isDeep ? cloneArrayBuffer(dataView.buffer) : dataView.buffer;\n  return new dataView.constructor(buffer, dataView.byteOffset, dataView.byteLength);\n}\n\n/**\n * Creates a clone of `map`.\n *\n * @private\n * @param {Object} map The map to clone.\n * @param {Function} cloneFunc The function to clone values.\n * @param {boolean} [isDeep] Specify a deep clone.\n * @returns {Object} Returns the cloned map.\n */\nfunction cloneMap(map, isDeep, cloneFunc) {\n  var array = isDeep ? cloneFunc(mapToArray(map), true) : mapToArray(map);\n  return arrayReduce(array, addMapEntry, new map.constructor);\n}\n\n/**\n * Creates a clone of `regexp`.\n *\n * @private\n * @param {Object} regexp The regexp to clone.\n * @returns {Object} Returns the cloned regexp.\n */\nfunction cloneRegExp(regexp) {\n  var result = new regexp.constructor(regexp.source, reFlags.exec(regexp));\n  result.lastIndex = regexp.lastIndex;\n  return result;\n}\n\n/**\n * Creates a clone of `set`.\n *\n * @private\n * @param {Object} set The set to clone.\n * @param {Function} cloneFunc The function to clone values.\n * @param {boolean} [isDeep] Specify a deep clone.\n * @returns {Object} Returns the cloned set.\n */\nfunction cloneSet(set, isDeep, cloneFunc) {\n  var array = isDeep ? cloneFunc(setToArray(set), true) : setToArray(set);\n  return arrayReduce(array, addSetEntry, new set.constructor);\n}\n\n/**\n * Creates a clone of the `symbol` object.\n *\n * @private\n * @param {Object} symbol The symbol object to clone.\n * @returns {Object} Returns the cloned symbol object.\n */\nfunction cloneSymbol(symbol) {\n  return symbolValueOf ? Object(symbolValueOf.call(symbol)) : {};\n}\n\n/**\n * Creates a clone of `typedArray`.\n *\n * @private\n * @param {Object} typedArray The typed array to clone.\n * @param {boolean} [isDeep] Specify a deep clone.\n * @returns {Object} Returns the cloned typed array.\n */\nfunction cloneTypedArray(typedArray, isDeep) {\n  var buffer = isDeep ? cloneArrayBuffer(typedArray.buffer) : typedArray.buffer;\n  return new typedArray.constructor(buffer, typedArray.byteOffset, typedArray.length);\n}\n\n/**\n * Copies the values of `source` to `array`.\n *\n * @private\n * @param {Array} source The array to copy values from.\n * @param {Array} [array=[]] The array to copy values to.\n * @returns {Array} Returns `array`.\n */\nfunction copyArray(source, array) {\n  var index = -1,\n      length = source.length;\n\n  array || (array = Array(length));\n  while (++index < length) {\n    array[index] = source[index];\n  }\n  return array;\n}\n\n/**\n * Copies properties of `source` to `object`.\n *\n * @private\n * @param {Object} source The object to copy properties from.\n * @param {Array} props The property identifiers to copy.\n * @param {Object} [object={}] The object to copy properties to.\n * @param {Function} [customizer] The function to customize copied values.\n * @returns {Object} Returns `object`.\n */\nfunction copyObject(source, props, object, customizer) {\n  object || (object = {});\n\n  var index = -1,\n      length = props.length;\n\n  while (++index < length) {\n    var key = props[index];\n\n    var newValue = customizer\n      ? customizer(object[key], source[key], key, object, source)\n      : undefined;\n\n    assignValue(object, key, newValue === undefined ? source[key] : newValue);\n  }\n  return object;\n}\n\n/**\n * Copies own symbol properties of `source` to `object`.\n *\n * @private\n * @param {Object} source The object to copy symbols from.\n * @param {Object} [object={}] The object to copy symbols to.\n * @returns {Object} Returns `object`.\n */\nfunction copySymbols(source, object) {\n  return copyObject(source, getSymbols(source), object);\n}\n\n/**\n * Creates an array of own enumerable property names and symbols of `object`.\n *\n * @private\n * @param {Object} object The object to query.\n * @returns {Array} Returns the array of property names and symbols.\n */\nfunction getAllKeys(object) {\n  return baseGetAllKeys(object, keys, getSymbols);\n}\n\n/**\n * Gets the data for `map`.\n *\n * @private\n * @param {Object} map The map to query.\n * @param {string} key The reference key.\n * @returns {*} Returns the map data.\n */\nfunction getMapData(map, key) {\n  var data = map.__data__;\n  return isKeyable(key)\n    ? data[typeof key == 'string' ? 'string' : 'hash']\n    : data.map;\n}\n\n/**\n * Gets the native function at `key` of `object`.\n *\n * @private\n * @param {Object} object The object to query.\n * @param {string} key The key of the method to get.\n * @returns {*} Returns the function if it's native, else `undefined`.\n */\nfunction getNative(object, key) {\n  var value = getValue(object, key);\n  return baseIsNative(value) ? value : undefined;\n}\n\n/**\n * Creates an array of the own enumerable symbol properties of `object`.\n *\n * @private\n * @param {Object} object The object to query.\n * @returns {Array} Returns the array of symbols.\n */\nvar getSymbols = nativeGetSymbols ? overArg(nativeGetSymbols, Object) : stubArray;\n\n/**\n * Gets the `toStringTag` of `value`.\n *\n * @private\n * @param {*} value The value to query.\n * @returns {string} Returns the `toStringTag`.\n */\nvar getTag = baseGetTag;\n\n// Fallback for data views, maps, sets, and weak maps in IE 11,\n// for data views in Edge < 14, and promises in Node.js.\nif ((DataView && getTag(new DataView(new ArrayBuffer(1))) != dataViewTag) ||\n    (Map && getTag(new Map) != mapTag) ||\n    (Promise && getTag(Promise.resolve()) != promiseTag) ||\n    (Set && getTag(new Set) != setTag) ||\n    (WeakMap && getTag(new WeakMap) != weakMapTag)) {\n  getTag = function(value) {\n    var result = objectToString.call(value),\n        Ctor = result == objectTag ? value.constructor : undefined,\n        ctorString = Ctor ? toSource(Ctor) : undefined;\n\n    if (ctorString) {\n      switch (ctorString) {\n        case dataViewCtorString: return dataViewTag;\n        case mapCtorString: return mapTag;\n        case promiseCtorString: return promiseTag;\n        case setCtorString: return setTag;\n        case weakMapCtorString: return weakMapTag;\n      }\n    }\n    return result;\n  };\n}\n\n/**\n * Initializes an array clone.\n *\n * @private\n * @param {Array} array The array to clone.\n * @returns {Array} Returns the initialized clone.\n */\nfunction initCloneArray(array) {\n  var length = array.length,\n      result = array.constructor(length);\n\n  // Add properties assigned by `RegExp#exec`.\n  if (length && typeof array[0] == 'string' && hasOwnProperty.call(array, 'index')) {\n    result.index = array.index;\n    result.input = array.input;\n  }\n  return result;\n}\n\n/**\n * Initializes an object clone.\n *\n * @private\n * @param {Object} object The object to clone.\n * @returns {Object} Returns the initialized clone.\n */\nfunction initCloneObject(object) {\n  return (typeof object.constructor == 'function' && !isPrototype(object))\n    ? baseCreate(getPrototype(object))\n    : {};\n}\n\n/**\n * Initializes an object clone based on its `toStringTag`.\n *\n * **Note:** This function only supports cloning values with tags of\n * `Boolean`, `Date`, `Error`, `Number`, `RegExp`, or `String`.\n *\n * @private\n * @param {Object} object The object to clone.\n * @param {string} tag The `toStringTag` of the object to clone.\n * @param {Function} cloneFunc The function to clone values.\n * @param {boolean} [isDeep] Specify a deep clone.\n * @returns {Object} Returns the initialized clone.\n */\nfunction initCloneByTag(object, tag, cloneFunc, isDeep) {\n  var Ctor = object.constructor;\n  switch (tag) {\n    case arrayBufferTag:\n      return cloneArrayBuffer(object);\n\n    case boolTag:\n    case dateTag:\n      return new Ctor(+object);\n\n    case dataViewTag:\n      return cloneDataView(object, isDeep);\n\n    case float32Tag: case float64Tag:\n    case int8Tag: case int16Tag: case int32Tag:\n    case uint8Tag: case uint8ClampedTag: case uint16Tag: case uint32Tag:\n      return cloneTypedArray(object, isDeep);\n\n    case mapTag:\n      return cloneMap(object, isDeep, cloneFunc);\n\n    case numberTag:\n    case stringTag:\n      return new Ctor(object);\n\n    case regexpTag:\n      return cloneRegExp(object);\n\n    case setTag:\n      return cloneSet(object, isDeep, cloneFunc);\n\n    case symbolTag:\n      return cloneSymbol(object);\n  }\n}\n\n/**\n * Checks if `value` is a valid array-like index.\n *\n * @private\n * @param {*} value The value to check.\n * @param {number} [length=MAX_SAFE_INTEGER] The upper bounds of a valid index.\n * @returns {boolean} Returns `true` if `value` is a valid index, else `false`.\n */\nfunction isIndex(value, length) {\n  length = length == null ? MAX_SAFE_INTEGER : length;\n  return !!length &&\n    (typeof value == 'number' || reIsUint.test(value)) &&\n    (value > -1 && value % 1 == 0 && value < length);\n}\n\n/**\n * Checks if `value` is suitable for use as unique object key.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is suitable, else `false`.\n */\nfunction isKeyable(value) {\n  var type = typeof value;\n  return (type == 'string' || type == 'number' || type == 'symbol' || type == 'boolean')\n    ? (value !== '__proto__')\n    : (value === null);\n}\n\n/**\n * Checks if `func` has its source masked.\n *\n * @private\n * @param {Function} func The function to check.\n * @returns {boolean} Returns `true` if `func` is masked, else `false`.\n */\nfunction isMasked(func) {\n  return !!maskSrcKey && (maskSrcKey in func);\n}\n\n/**\n * Checks if `value` is likely a prototype object.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a prototype, else `false`.\n */\nfunction isPrototype(value) {\n  var Ctor = value && value.constructor,\n      proto = (typeof Ctor == 'function' && Ctor.prototype) || objectProto;\n\n  return value === proto;\n}\n\n/**\n * Converts `func` to its source code.\n *\n * @private\n * @param {Function} func The function to process.\n * @returns {string} Returns the source code.\n */\nfunction toSource(func) {\n  if (func != null) {\n    try {\n      return funcToString.call(func);\n    } catch (e) {}\n    try {\n      return (func + '');\n    } catch (e) {}\n  }\n  return '';\n}\n\n/**\n * This method is like `_.clone` except that it recursively clones `value`.\n *\n * @static\n * @memberOf _\n * @since 1.0.0\n * @category Lang\n * @param {*} value The value to recursively clone.\n * @returns {*} Returns the deep cloned value.\n * @see _.clone\n * @example\n *\n * var objects = [{ 'a': 1 }, { 'b': 2 }];\n *\n * var deep = _.cloneDeep(objects);\n * console.log(deep[0] === objects[0]);\n * // => false\n */\nfunction cloneDeep(value) {\n  return baseClone(value, true, true);\n}\n\n/**\n * Performs a\n * [`SameValueZero`](http://ecma-international.org/ecma-262/7.0/#sec-samevaluezero)\n * comparison between two values to determine if they are equivalent.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to compare.\n * @param {*} other The other value to compare.\n * @returns {boolean} Returns `true` if the values are equivalent, else `false`.\n * @example\n *\n * var object = { 'a': 1 };\n * var other = { 'a': 1 };\n *\n * _.eq(object, object);\n * // => true\n *\n * _.eq(object, other);\n * // => false\n *\n * _.eq('a', 'a');\n * // => true\n *\n * _.eq('a', Object('a'));\n * // => false\n *\n * _.eq(NaN, NaN);\n * // => true\n */\nfunction eq(value, other) {\n  return value === other || (value !== value && other !== other);\n}\n\n/**\n * Checks if `value` is likely an `arguments` object.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is an `arguments` object,\n *  else `false`.\n * @example\n *\n * _.isArguments(function() { return arguments; }());\n * // => true\n *\n * _.isArguments([1, 2, 3]);\n * // => false\n */\nfunction isArguments(value) {\n  // Safari 8.1 makes `arguments.callee` enumerable in strict mode.\n  return isArrayLikeObject(value) && hasOwnProperty.call(value, 'callee') &&\n    (!propertyIsEnumerable.call(value, 'callee') || objectToString.call(value) == argsTag);\n}\n\n/**\n * Checks if `value` is classified as an `Array` object.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is an array, else `false`.\n * @example\n *\n * _.isArray([1, 2, 3]);\n * // => true\n *\n * _.isArray(document.body.children);\n * // => false\n *\n * _.isArray('abc');\n * // => false\n *\n * _.isArray(_.noop);\n * // => false\n */\nvar isArray = Array.isArray;\n\n/**\n * Checks if `value` is array-like. A value is considered array-like if it's\n * not a function and has a `value.length` that's an integer greater than or\n * equal to `0` and less than or equal to `Number.MAX_SAFE_INTEGER`.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is array-like, else `false`.\n * @example\n *\n * _.isArrayLike([1, 2, 3]);\n * // => true\n *\n * _.isArrayLike(document.body.children);\n * // => true\n *\n * _.isArrayLike('abc');\n * // => true\n *\n * _.isArrayLike(_.noop);\n * // => false\n */\nfunction isArrayLike(value) {\n  return value != null && isLength(value.length) && !isFunction(value);\n}\n\n/**\n * This method is like `_.isArrayLike` except that it also checks if `value`\n * is an object.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is an array-like object,\n *  else `false`.\n * @example\n *\n * _.isArrayLikeObject([1, 2, 3]);\n * // => true\n *\n * _.isArrayLikeObject(document.body.children);\n * // => true\n *\n * _.isArrayLikeObject('abc');\n * // => false\n *\n * _.isArrayLikeObject(_.noop);\n * // => false\n */\nfunction isArrayLikeObject(value) {\n  return isObjectLike(value) && isArrayLike(value);\n}\n\n/**\n * Checks if `value` is a buffer.\n *\n * @static\n * @memberOf _\n * @since 4.3.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a buffer, else `false`.\n * @example\n *\n * _.isBuffer(new Buffer(2));\n * // => true\n *\n * _.isBuffer(new Uint8Array(2));\n * // => false\n */\nvar isBuffer = nativeIsBuffer || stubFalse;\n\n/**\n * Checks if `value` is classified as a `Function` object.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a function, else `false`.\n * @example\n *\n * _.isFunction(_);\n * // => true\n *\n * _.isFunction(/abc/);\n * // => false\n */\nfunction isFunction(value) {\n  // The use of `Object#toString` avoids issues with the `typeof` operator\n  // in Safari 8-9 which returns 'object' for typed array and other constructors.\n  var tag = isObject(value) ? objectToString.call(value) : '';\n  return tag == funcTag || tag == genTag;\n}\n\n/**\n * Checks if `value` is a valid array-like length.\n *\n * **Note:** This method is loosely based on\n * [`ToLength`](http://ecma-international.org/ecma-262/7.0/#sec-tolength).\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a valid length, else `false`.\n * @example\n *\n * _.isLength(3);\n * // => true\n *\n * _.isLength(Number.MIN_VALUE);\n * // => false\n *\n * _.isLength(Infinity);\n * // => false\n *\n * _.isLength('3');\n * // => false\n */\nfunction isLength(value) {\n  return typeof value == 'number' &&\n    value > -1 && value % 1 == 0 && value <= MAX_SAFE_INTEGER;\n}\n\n/**\n * Checks if `value` is the\n * [language type](http://www.ecma-international.org/ecma-262/7.0/#sec-ecmascript-language-types)\n * of `Object`. (e.g. arrays, functions, objects, regexes, `new Number(0)`, and `new String('')`)\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is an object, else `false`.\n * @example\n *\n * _.isObject({});\n * // => true\n *\n * _.isObject([1, 2, 3]);\n * // => true\n *\n * _.isObject(_.noop);\n * // => true\n *\n * _.isObject(null);\n * // => false\n */\nfunction isObject(value) {\n  var type = typeof value;\n  return !!value && (type == 'object' || type == 'function');\n}\n\n/**\n * Checks if `value` is object-like. A value is object-like if it's not `null`\n * and has a `typeof` result of \"object\".\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is object-like, else `false`.\n * @example\n *\n * _.isObjectLike({});\n * // => true\n *\n * _.isObjectLike([1, 2, 3]);\n * // => true\n *\n * _.isObjectLike(_.noop);\n * // => false\n *\n * _.isObjectLike(null);\n * // => false\n */\nfunction isObjectLike(value) {\n  return !!value && typeof value == 'object';\n}\n\n/**\n * Creates an array of the own enumerable property names of `object`.\n *\n * **Note:** Non-object values are coerced to objects. See the\n * [ES spec](http://ecma-international.org/ecma-262/7.0/#sec-object.keys)\n * for more details.\n *\n * @static\n * @since 0.1.0\n * @memberOf _\n * @category Object\n * @param {Object} object The object to query.\n * @returns {Array} Returns the array of property names.\n * @example\n *\n * function Foo() {\n *   this.a = 1;\n *   this.b = 2;\n * }\n *\n * Foo.prototype.c = 3;\n *\n * _.keys(new Foo);\n * // => ['a', 'b'] (iteration order is not guaranteed)\n *\n * _.keys('hi');\n * // => ['0', '1']\n */\nfunction keys(object) {\n  return isArrayLike(object) ? arrayLikeKeys(object) : baseKeys(object);\n}\n\n/**\n * This method returns a new empty array.\n *\n * @static\n * @memberOf _\n * @since 4.13.0\n * @category Util\n * @returns {Array} Returns the new empty array.\n * @example\n *\n * var arrays = _.times(2, _.stubArray);\n *\n * console.log(arrays);\n * // => [[], []]\n *\n * console.log(arrays[0] === arrays[1]);\n * // => false\n */\nfunction stubArray() {\n  return [];\n}\n\n/**\n * This method returns `false`.\n *\n * @static\n * @memberOf _\n * @since 4.13.0\n * @category Util\n * @returns {boolean} Returns `false`.\n * @example\n *\n * _.times(2, _.stubFalse);\n * // => [false, false]\n */\nfunction stubFalse() {\n  return false;\n}\n\nmodule.exports = cloneDeep;\n", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tif(__webpack_module_cache__[moduleId]) {\n\t\treturn __webpack_module_cache__[moduleId].exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\tid: moduleId,\n\t\tloaded: false,\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Flag the module as loaded\n\tmodule.loaded = true;\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "// module exports must be returned from runtime so entry inlining is disabled\n// startup\n// Load entry module and return exports\nreturn __webpack_require__(442);\n", "__webpack_require__.nmd = (module) => {\n\tmodule.paths = [];\n\tif (!module.children) module.children = [];\n\treturn module;\n};"], "sourceRoot": ""}