// Test script for price extraction
const testPrices = [
  '€3,950 per month',
  '€ 4 per maand',
  '€3.950 per maand',
  '€3,950',
  '€ 3,950 per month',
  '€ 3.950,00 per maand'
];

function extractPrice(priceString) {
  console.log(`\nTesting price: "${priceString}"`);
  
  // Clean up HTML entities, non-breaking spaces, and extra content
  let normalizedPrice = priceString
    .replace(/&nbsp;/g, " ")
    .replace(/\u00A0/g, " ") // Non-breaking space character (160)
    .replace(/\s+/g, " ")
    .trim();

  // Extract only the price part (before any newlines or extra content)
  const priceLineMatch = normalizedPrice.match(/^([^\\n]+)/);
  if (priceLineMatch) {
    normalizedPrice = priceLineMatch[1].trim();
  }

  // Handle common Dutch price formats
  if (
    normalizedPrice.toLowerCase().includes("op aanvraag") ||
    normalizedPrice.toLowerCase().includes("on request")
  ) {
    normalizedPrice = "Prijs op aanvraag";
    console.log(`Result: ${normalizedPrice}`);
    return normalizedPrice;
  } else {
    // Extract numeric value and format consistently
    const priceMatch = normalizedPrice.match(/€\s*([\d.,]+)/);
    if (priceMatch) {
      // Remove all non-numeric characters except the last comma or period (decimal separator)
      const cleanPrice = priceMatch[1].replace(/[^\d,.]/g, '').replace(/[,.](?=.*[,.])/g, '');
      const numericPrice = parseFloat(cleanPrice.replace(',', '.'));
      
      console.log(`Extracted: ${priceMatch[1]}`);
      console.log(`Cleaned: ${cleanPrice}`);
      console.log(`Numeric: ${numericPrice}`);
      
      if (numericPrice > 0) {
        const formattedPrice = `€ ${Math.round(numericPrice).toLocaleString('nl-NL')} per maand`;
        console.log(`Result: ${formattedPrice}`);
        return formattedPrice;
      }
    }
  }
  
  console.log("Failed to extract price");
  return "Prijs op aanvraag";
}

// Test all price formats
testPrices.forEach(price => extractPrice(price));
