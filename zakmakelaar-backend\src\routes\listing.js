const express = require("express");
const listingController = require("../controllers/listingController");
const {
  validateListingQuery,
  validateObjectId,
} = require("../middleware/validation");
const { cacheConfigs } = require("../middleware/cache");

const router = express.Router();

/**
 * @swagger
 * components:
 *   schemas:
 *     Listing:
 *       type: object
 *       properties:
 *         _id:
 *           type: string
 *           description: Unique identifier for the listing
 *         title:
 *           type: string
 *           description: Title of the property listing
 *         price:
 *           type: string
 *           description: Price of the property
 *         location:
 *           type: string
 *           description: Location of the property
 *         url:
 *           type: string
 *           description: URL to the original listing
 *         propertyType:
 *           type: string
 *           description: Type of property (apartment, house, etc.)
 *         rooms:
 *           type: number
 *           description: Number of rooms
 *         dateAdded:
 *           type: string
 *           format: date-time
 *           description: Date when listing was added
 *         description:
 *           type: string
 *           description: Property description
 *         timestamp:
 *           type: string
 *           format: date-time
 *           description: Timestamp of last update
 *
 *     PaginationInfo:
 *       type: object
 *       properties:
 *         currentPage:
 *           type: number
 *           description: Current page number
 *         totalPages:
 *           type: number
 *           description: Total number of pages
 *         totalCount:
 *           type: number
 *           description: Total number of items
 *         hasNextPage:
 *           type: boolean
 *           description: Whether there is a next page
 *         hasPrevPage:
 *           type: boolean
 *           description: Whether there is a previous page
 *         limit:
 *           type: number
 *           description: Number of items per page
 *
 * /api/listings:
 *   get:
 *     summary: Get listings with advanced search and filtering
 *     tags: [Listings]
 *     parameters:
 *       - in: query
 *         name: q
 *         schema:
 *           type: string
 *         description: Text search query (searches title, description, location)
 *       - in: query
 *         name: location
 *         schema:
 *           type: string
 *         description: Filter by location (partial match, case-insensitive)
 *       - in: query
 *         name: minPrice
 *         schema:
 *           type: number
 *         description: Minimum price filter
 *       - in: query
 *         name: maxPrice
 *         schema:
 *           type: number
 *         description: Maximum price filter
 *       - in: query
 *         name: propertyType
 *         schema:
 *           type: string
 *         description: Filter by property type
 *       - in: query
 *         name: minRooms
 *         schema:
 *           type: number
 *         description: Minimum number of rooms
 *       - in: query
 *         name: maxRooms
 *         schema:
 *           type: number
 *         description: Maximum number of rooms
 *       - in: query
 *         name: dateFrom
 *         schema:
 *           type: string
 *           format: date
 *         description: Filter listings added from this date
 *       - in: query
 *         name: dateTo
 *         schema:
 *           type: string
 *           format: date
 *         description: Filter listings added until this date
 *       - in: query
 *         name: sortBy
 *         schema:
 *           type: string
 *           enum: [dateAdded, price, title, location, timestamp]
 *           default: dateAdded
 *         description: Field to sort by
 *       - in: query
 *         name: sortOrder
 *         schema:
 *           type: string
 *           enum: [asc, desc]
 *           default: desc
 *         description: Sort order
 *       - in: query
 *         name: page
 *         schema:
 *           type: number
 *           minimum: 1
 *           default: 1
 *         description: Page number for pagination
 *       - in: query
 *         name: limit
 *         schema:
 *           type: number
 *           minimum: 1
 *           maximum: 100
 *           default: 20
 *         description: Number of items per page
 *     responses:
 *       200:
 *         description: Successfully retrieved listings
 *         headers:
 *           X-Cache:
 *             description: Cache status (HIT or MISS)
 *             schema:
 *               type: string
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 results:
 *                   type: number
 *                   description: Number of listings returned
 *                 pagination:
 *                   $ref: '#/components/schemas/PaginationInfo'
 *                 data:
 *                   type: object
 *                   properties:
 *                     listings:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/Listing'
 *       400:
 *         description: Invalid query parameters
 *       429:
 *         description: Too many requests
 *       500:
 *         description: Internal server error
 */
router.get(
  "/listings",
  validateListingQuery,
  cacheConfigs.listings,
  listingController.getListings
);

/**
 * @swagger
 * /api/listings/{id}:
 *   get:
 *     summary: Get a specific listing by ID
 *     tags: [Listings]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: MongoDB ObjectId of the listing
 *     responses:
 *       200:
 *         description: Successfully retrieved listing
 *         headers:
 *           X-Cache:
 *             description: Cache status (HIT or MISS)
 *             schema:
 *               type: string
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 data:
 *                   type: object
 *                   properties:
 *                     listing:
 *                       $ref: '#/components/schemas/Listing'
 *       400:
 *         description: Invalid listing ID format
 *       404:
 *         description: Listing not found
 *       500:
 *         description: Internal server error
 */
router.get(
  "/listings/:id",
  validateObjectId,
  cacheConfigs.listings,
  listingController.getListingById
);

/**
 * @swagger
 * /api/search/suggestions:
 *   get:
 *     summary: Get search suggestions for auto-complete
 *     tags: [Search]
 *     parameters:
 *       - in: query
 *         name: q
 *         required: true
 *         schema:
 *           type: string
 *         description: Query string to get suggestions for
 *       - in: query
 *         name: type
 *         schema:
 *           type: string
 *           enum: [location, propertyType]
 *           default: location
 *         description: Type of suggestions to return
 *     responses:
 *       200:
 *         description: Successfully retrieved suggestions
 *         headers:
 *           X-Cache:
 *             description: Cache status (HIT or MISS)
 *             schema:
 *               type: string
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 results:
 *                   type: number
 *                   description: Number of suggestions returned
 *                 data:
 *                   type: object
 *                   properties:
 *                     suggestions:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           suggestion:
 *                             type: string
 *                             description: The suggested text
 *                           count:
 *                             type: number
 *                             description: Number of listings matching this suggestion
 *       400:
 *         description: Missing or invalid query parameter
 *       500:
 *         description: Internal server error
 */
router.get(
  "/search/suggestions",
  cacheConfigs.short,
  listingController.getSearchSuggestions
);

/**
 * @swagger
 * /api/search/stats:
 *   get:
 *     summary: Get search statistics and database analytics
 *     tags: [Search]
 *     responses:
 *       200:
 *         description: Successfully retrieved search statistics
 *         headers:
 *           X-Cache:
 *             description: Cache status (HIT or MISS)
 *             schema:
 *               type: string
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 data:
 *                   type: object
 *                   properties:
 *                     stats:
 *                       type: object
 *                       properties:
 *                         totalListings:
 *                           type: number
 *                           description: Total number of listings in database
 *                         avgPrice:
 *                           type: number
 *                           description: Average price of all listings
 *                         minPrice:
 *                           type: number
 *                           description: Minimum price found
 *                         maxPrice:
 *                           type: number
 *                           description: Maximum price found
 *                         uniqueLocations:
 *                           type: number
 *                           description: Number of unique locations
 *                         uniquePropertyTypes:
 *                           type: number
 *                           description: Number of unique property types
 *                         locations:
 *                           type: array
 *                           items:
 *                             type: string
 *                           description: List of available locations (top 20)
 *                         propertyTypes:
 *                           type: array
 *                           items:
 *                             type: string
 *                           description: List of available property types (top 20)
 *       500:
 *         description: Internal server error
 */
router.get(
  "/search/stats",
  cacheConfigs.general,
  listingController.getSearchStats
);

module.exports = router;
