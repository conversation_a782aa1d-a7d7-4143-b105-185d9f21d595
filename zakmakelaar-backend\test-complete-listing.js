const mongoose = require('mongoose');
const Listing = require('./src/models/Listing');

async function testCompleteListing() {
  try {
    await mongoose.connect('mongodb://localhost:27017/zakmakelaar');
    console.log('✅ Connected to MongoDB');
    
    // Find one of our complete listings
    const completeListing = await Listing.findOne({
      title: { $regex: /Luxury Canal Apartment|Modern Penthouse|Charming Family House|Stylish Studio/ }
    });
    
    if (completeListing) {
      console.log('🏠 Found complete listing:');
      console.log(`Title: ${completeListing.title}`);
      console.log(`ID: ${completeListing._id}`);
      console.log(`Price: ${completeListing.price}`);
      console.log(`Location: ${completeListing.location}`);
      console.log(`Size: ${completeListing.size}`);
      console.log(`Bedrooms: ${completeListing.bedrooms}`);
      console.log(`Rooms: ${completeListing.rooms}`);
      console.log(`Year: ${completeListing.year}`);
      console.log(`Property Type: ${completeListing.propertyType}`);
      console.log(`Interior: ${completeListing.interior}`);
      console.log(`Images: ${completeListing.images?.length || 0}`);
      console.log(`Description: ${completeListing.description?.substring(0, 100)}...`);
      
      // Test the API endpoint for this specific listing
      console.log(`\n🧪 Testing API endpoint for this listing...`);
      console.log(`URL: http://localhost:3000/api/listings/${completeListing._id}`);
      
    } else {
      console.log('❌ No complete listings found');
    }
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await mongoose.connection.close();
    console.log('🔌 Disconnected');
  }
}

testCompleteListing();
