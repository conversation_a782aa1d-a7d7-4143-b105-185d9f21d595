const mongoose = require("mongoose");
const config = require("./config");

// Database connection with improved configuration
const connectDB = async () => {
  try {
    const conn = await mongoose.connect(config.mongoURI, {
      // Connection options for better performance and reliability
      maxPoolSize: 10, // Maintain up to 10 socket connections
      serverSelectionTimeoutMS: 5000, // Keep trying to send operations for 5 seconds
      socketTimeoutMS: 45000, // Close sockets after 45 seconds
      // bufferMaxEntries and bufferCommands are deprecated in newer versions
    });

    console.log(`✅ MongoDB Connected: ${conn.connection.host}`);

    // Create indexes for better performance
    await createIndexes();

    // Handle connection events
    mongoose.connection.on("error", (err) => {
      console.error("❌ MongoDB connection error:", err);
    });

    mongoose.connection.on("disconnected", () => {
      console.log("⚠️ MongoDB disconnected");
    });

    mongoose.connection.on("reconnected", () => {
      console.log("✅ MongoDB reconnected");
    });

    return conn;
  } catch (error) {
    console.error("❌ MongoDB connection failed:", error);
    process.exit(1);
  }
};

// Create database indexes for better query performance
const createIndexes = async () => {
  try {
    const db = mongoose.connection.db;

    // Listings collection indexes
    await db.collection("listings").createIndex({ url: 1 }, { unique: true });
    await db.collection("listings").createIndex({ dateAdded: -1 });
    await db.collection("listings").createIndex({ location: 1 });
    await db.collection("listings").createIndex({ price: 1 });
    await db.collection("listings").createIndex({ propertyType: 1 });
    await db.collection("listings").createIndex({
      location: "text",
      title: "text",
      description: "text",
    });

    // Users collection indexes
    await db.collection("users").createIndex({ email: 1 }, { unique: true });
    await db.collection("users").createIndex({ "preferences.location": 1 });
    await db.collection("users").createIndex({ "preferences.budget": 1 });

    // Applications collection indexes (if used)
    await db.collection("applications").createIndex({ user: 1 });
    await db.collection("applications").createIndex({ listing: 1 });
    await db.collection("applications").createIndex({ date: -1 });

    console.log("✅ Database indexes created successfully");
  } catch (error) {
    console.error("⚠️ Error creating indexes:", error.message);
    // Don't exit process for index creation errors
  }
};

// Graceful shutdown
const closeDB = async () => {
  try {
    await mongoose.connection.close();
    console.log("✅ MongoDB connection closed");
  } catch (error) {
    console.error("❌ Error closing MongoDB connection:", error);
  }
};

module.exports = {
  connectDB,
  closeDB,
  createIndexes,
};
