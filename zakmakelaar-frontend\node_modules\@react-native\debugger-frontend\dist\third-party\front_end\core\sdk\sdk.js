import*as e from"../common/common.js";import*as t from"../../models/cpu_profile/cpu_profile.js";import*as n from"../../models/text_utils/text_utils.js";import*as r from"../i18n/i18n.js";import*as s from"../platform/platform.js";import{assertNotNullOrUndefined as i}from"../platform/platform.js";import*as o from"../root/root.js";import*as a from"../host/host.js";import*as l from"../protocol_client/protocol_client.js";import*as d from"../../third_party/codemirror.next/codemirror.next.js";const c=new Map;class h extends e.ObjectWrapper.ObjectWrapper{#e;constructor(e){super(),this.#e=e}target(){return this.#e}async preSuspendModel(e){}async suspendModel(e){}async resumeModel(){}async postResumeModel(){}dispose(){}static register(e,t){if(t.early&&!t.autostart)throw new Error(`Error registering model ${e.name}: early models must be autostarted.`);c.set(e,t)}static get registeredModels(){return c}}var u=Object.freeze({__proto__:null,SDKModel:h});const g=[{longhands:["animation-duration","animation-timing-function","animation-delay","animation-iteration-count","animation-direction","animation-fill-mode","animation-play-state","animation-name","animation-timeline","animation-range-start","animation-range-end"],name:"-alternative-animation-with-timeline"},{longhands:["position-try-order","position-try-fallbacks"],name:"-alternative-position-try"},{inherited:!0,name:"-webkit-border-horizontal-spacing"},{name:"-webkit-border-image"},{inherited:!0,name:"-webkit-border-vertical-spacing"},{keywords:["stretch","start","center","end","baseline"],name:"-webkit-box-align"},{keywords:["slice","clone"],name:"-webkit-box-decoration-break"},{keywords:["normal","reverse"],name:"-webkit-box-direction"},{name:"-webkit-box-flex"},{name:"-webkit-box-ordinal-group"},{keywords:["horizontal","vertical"],name:"-webkit-box-orient"},{keywords:["start","center","end","justify"],name:"-webkit-box-pack"},{name:"-webkit-box-reflect"},{longhands:["break-after"],name:"-webkit-column-break-after"},{longhands:["break-before"],name:"-webkit-column-break-before"},{longhands:["break-inside"],name:"-webkit-column-break-inside"},{inherited:!0,name:"-webkit-font-smoothing"},{inherited:!0,keywords:["auto","loose","normal","strict","after-white-space","anywhere"],name:"-webkit-line-break"},{name:"-webkit-line-clamp"},{inherited:!0,name:"-webkit-locale"},{longhands:["-webkit-mask-box-image-source","-webkit-mask-box-image-slice","-webkit-mask-box-image-width","-webkit-mask-box-image-outset","-webkit-mask-box-image-repeat"],name:"-webkit-mask-box-image"},{name:"-webkit-mask-box-image-outset"},{name:"-webkit-mask-box-image-repeat"},{name:"-webkit-mask-box-image-slice"},{name:"-webkit-mask-box-image-source"},{name:"-webkit-mask-box-image-width"},{name:"-webkit-mask-position-x"},{name:"-webkit-mask-position-y"},{name:"-webkit-perspective-origin-x"},{name:"-webkit-perspective-origin-y"},{inherited:!0,keywords:["economy","exact"],name:"-webkit-print-color-adjust"},{inherited:!0,keywords:["logical","visual"],name:"-webkit-rtl-ordering"},{inherited:!0,name:"-webkit-ruby-position"},{inherited:!0,name:"-webkit-tap-highlight-color"},{inherited:!0,name:"-webkit-text-combine"},{inherited:!0,name:"-webkit-text-decorations-in-effect"},{inherited:!0,name:"-webkit-text-fill-color"},{inherited:!0,name:"-webkit-text-orientation"},{inherited:!0,keywords:["none","disc","circle","square"],name:"-webkit-text-security"},{inherited:!0,longhands:["-webkit-text-stroke-width","-webkit-text-stroke-color"],name:"-webkit-text-stroke"},{inherited:!0,name:"-webkit-text-stroke-color"},{inherited:!0,name:"-webkit-text-stroke-width"},{name:"-webkit-transform-origin-x"},{name:"-webkit-transform-origin-y"},{name:"-webkit-transform-origin-z"},{keywords:["auto","none","element"],name:"-webkit-user-drag"},{inherited:!0,keywords:["read-only","read-write","read-write-plaintext-only"],name:"-webkit-user-modify"},{inherited:!0,name:"-webkit-writing-mode"},{inherited:!0,keywords:["auto","currentcolor"],name:"accent-color"},{name:"additive-symbols"},{name:"align-content"},{name:"align-items"},{name:"align-self"},{keywords:["auto","baseline","alphabetic","ideographic","middle","central","mathematical","before-edge","text-before-edge","after-edge","text-after-edge","hanging"],name:"alignment-baseline"},{name:"all"},{keywords:["none"],name:"anchor-name"},{keywords:["none","all"],name:"anchor-scope"},{longhands:["animation-duration","animation-timing-function","animation-delay","animation-iteration-count","animation-direction","animation-fill-mode","animation-play-state","animation-name"],name:"animation"},{keywords:["replace","add","accumulate"],name:"animation-composition"},{name:"animation-delay"},{keywords:["normal","reverse","alternate","alternate-reverse"],name:"animation-direction"},{name:"animation-duration"},{keywords:["none","forwards","backwards","both"],name:"animation-fill-mode"},{keywords:["infinite"],name:"animation-iteration-count"},{keywords:["none"],name:"animation-name"},{keywords:["running","paused"],name:"animation-play-state"},{longhands:["animation-range-start","animation-range-end"],name:"animation-range"},{name:"animation-range-end"},{name:"animation-range-start"},{keywords:["none","auto"],name:"animation-timeline"},{keywords:["linear","ease","ease-in","ease-out","ease-in-out","jump-both","jump-end","jump-none","jump-start","step-start","step-end"],name:"animation-timing-function"},{keywords:["none","drag","no-drag"],name:"app-region"},{name:"appearance"},{name:"ascent-override"},{keywords:["auto"],name:"aspect-ratio"},{keywords:["none"],name:"backdrop-filter"},{keywords:["visible","hidden"],name:"backface-visibility"},{longhands:["background-image","background-position-x","background-position-y","background-size","background-repeat","background-attachment","background-origin","background-clip","background-color"],name:"background"},{keywords:["scroll","fixed","local"],name:"background-attachment"},{keywords:["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],name:"background-blend-mode"},{keywords:["border-box","padding-box","content-box","text"],name:"background-clip"},{keywords:["currentcolor"],name:"background-color"},{keywords:["auto","none"],name:"background-image"},{keywords:["border-box","padding-box","content-box"],name:"background-origin"},{longhands:["background-position-x","background-position-y"],name:"background-position"},{name:"background-position-x"},{name:"background-position-y"},{name:"background-repeat"},{keywords:["auto","cover","contain"],name:"background-size"},{name:"base-palette"},{keywords:["baseline","sub","super"],name:"baseline-shift"},{keywords:["auto","first","last"],name:"baseline-source"},{keywords:["auto"],name:"block-size"},{longhands:["border-top-color","border-top-style","border-top-width","border-right-color","border-right-style","border-right-width","border-bottom-color","border-bottom-style","border-bottom-width","border-left-color","border-left-style","border-left-width","border-image-source","border-image-slice","border-image-width","border-image-outset","border-image-repeat"],name:"border"},{longhands:["border-block-start-color","border-block-start-style","border-block-start-width","border-block-end-color","border-block-end-style","border-block-end-width"],name:"border-block"},{longhands:["border-block-start-color","border-block-end-color"],name:"border-block-color"},{longhands:["border-block-end-width","border-block-end-style","border-block-end-color"],name:"border-block-end"},{name:"border-block-end-color"},{name:"border-block-end-style"},{name:"border-block-end-width"},{longhands:["border-block-start-width","border-block-start-style","border-block-start-color"],name:"border-block-start"},{name:"border-block-start-color"},{name:"border-block-start-style"},{name:"border-block-start-width"},{longhands:["border-block-start-style","border-block-end-style"],name:"border-block-style"},{longhands:["border-block-start-width","border-block-end-width"],name:"border-block-width"},{longhands:["border-bottom-width","border-bottom-style","border-bottom-color"],name:"border-bottom"},{keywords:["currentcolor"],name:"border-bottom-color"},{name:"border-bottom-left-radius"},{name:"border-bottom-right-radius"},{keywords:["none","hidden","inset","groove","outset","ridge","dotted","dashed","solid","double"],name:"border-bottom-style"},{keywords:["thin","medium","thick"],name:"border-bottom-width"},{inherited:!0,keywords:["separate","collapse"],name:"border-collapse"},{longhands:["border-top-color","border-right-color","border-bottom-color","border-left-color"],name:"border-color"},{name:"border-end-end-radius"},{name:"border-end-start-radius"},{longhands:["border-image-source","border-image-slice","border-image-width","border-image-outset","border-image-repeat"],name:"border-image"},{name:"border-image-outset"},{keywords:["stretch","repeat","round","space"],name:"border-image-repeat"},{name:"border-image-slice"},{keywords:["none"],name:"border-image-source"},{keywords:["auto"],name:"border-image-width"},{longhands:["border-inline-start-color","border-inline-start-style","border-inline-start-width","border-inline-end-color","border-inline-end-style","border-inline-end-width"],name:"border-inline"},{longhands:["border-inline-start-color","border-inline-end-color"],name:"border-inline-color"},{longhands:["border-inline-end-width","border-inline-end-style","border-inline-end-color"],name:"border-inline-end"},{name:"border-inline-end-color"},{name:"border-inline-end-style"},{name:"border-inline-end-width"},{longhands:["border-inline-start-width","border-inline-start-style","border-inline-start-color"],name:"border-inline-start"},{name:"border-inline-start-color"},{name:"border-inline-start-style"},{name:"border-inline-start-width"},{longhands:["border-inline-start-style","border-inline-end-style"],name:"border-inline-style"},{longhands:["border-inline-start-width","border-inline-end-width"],name:"border-inline-width"},{longhands:["border-left-width","border-left-style","border-left-color"],name:"border-left"},{keywords:["currentcolor"],name:"border-left-color"},{keywords:["none","hidden","inset","groove","outset","ridge","dotted","dashed","solid","double"],name:"border-left-style"},{keywords:["thin","medium","thick"],name:"border-left-width"},{longhands:["border-top-left-radius","border-top-right-radius","border-bottom-right-radius","border-bottom-left-radius"],name:"border-radius"},{longhands:["border-right-width","border-right-style","border-right-color"],name:"border-right"},{keywords:["currentcolor"],name:"border-right-color"},{keywords:["none","hidden","inset","groove","outset","ridge","dotted","dashed","solid","double"],name:"border-right-style"},{keywords:["thin","medium","thick"],name:"border-right-width"},{inherited:!0,longhands:["-webkit-border-horizontal-spacing","-webkit-border-vertical-spacing"],name:"border-spacing"},{name:"border-start-end-radius"},{name:"border-start-start-radius"},{keywords:["none"],longhands:["border-top-style","border-right-style","border-bottom-style","border-left-style"],name:"border-style"},{longhands:["border-top-width","border-top-style","border-top-color"],name:"border-top"},{keywords:["currentcolor"],name:"border-top-color"},{name:"border-top-left-radius"},{name:"border-top-right-radius"},{keywords:["none","hidden","inset","groove","outset","ridge","dotted","dashed","solid","double"],name:"border-top-style"},{keywords:["thin","medium","thick"],name:"border-top-width"},{longhands:["border-top-width","border-right-width","border-bottom-width","border-left-width"],name:"border-width"},{keywords:["auto"],name:"bottom"},{keywords:["slice","clone"],name:"box-decoration-break"},{keywords:["none"],name:"box-shadow"},{keywords:["content-box","border-box"],name:"box-sizing"},{keywords:["auto","avoid","avoid-column","avoid-page","column","left","page","recto","right","verso"],name:"break-after"},{keywords:["auto","avoid","avoid-column","avoid-page","column","left","page","recto","right","verso"],name:"break-before"},{keywords:["auto","avoid","avoid-column","avoid-page"],name:"break-inside"},{keywords:["auto","dynamic","static"],name:"buffered-rendering"},{inherited:!0,keywords:["top","bottom"],name:"caption-side"},{inherited:!0,keywords:["auto","currentcolor"],name:"caret-color"},{keywords:["none","left","right","both","inline-start","inline-end"],name:"clear"},{keywords:["auto"],name:"clip"},{keywords:["border-box","padding-box","content-box","margin-box","fill-box","stroke-box","view-box","none"],name:"clip-path"},{inherited:!0,keywords:["nonzero","evenodd"],name:"clip-rule"},{inherited:!0,keywords:["currentcolor"],name:"color"},{inherited:!0,keywords:["auto","srgb","linearrgb"],name:"color-interpolation"},{inherited:!0,keywords:["auto","srgb","linearrgb"],name:"color-interpolation-filters"},{inherited:!0,keywords:["auto","optimizespeed","optimizequality"],name:"color-rendering"},{inherited:!0,name:"color-scheme"},{keywords:["auto"],name:"column-count"},{keywords:["balance","auto"],name:"column-fill"},{keywords:["normal"],name:"column-gap"},{longhands:["column-rule-width","column-rule-style","column-rule-color"],name:"column-rule"},{keywords:["currentcolor"],name:"column-rule-color"},{keywords:["none","hidden","inset","groove","outset","ridge","dotted","dashed","solid","double"],name:"column-rule-style"},{keywords:["thin","medium","thick"],name:"column-rule-width"},{keywords:["none","all"],name:"column-span"},{keywords:["auto"],name:"column-width"},{longhands:["column-width","column-count"],name:"columns"},{keywords:["none","strict","content","size","layout","style","paint","inline-size","block-size"],name:"contain"},{name:"contain-intrinsic-block-size"},{keywords:["none"],name:"contain-intrinsic-height"},{name:"contain-intrinsic-inline-size"},{longhands:["contain-intrinsic-width","contain-intrinsic-height"],name:"contain-intrinsic-size"},{keywords:["none"],name:"contain-intrinsic-width"},{longhands:["container-name","container-type"],name:"container"},{keywords:["none"],name:"container-name"},{keywords:["normal","inline-size","size","scroll-state"],name:"container-type"},{name:"content"},{keywords:["visible","auto","hidden"],name:"content-visibility"},{keywords:["none"],name:"counter-increment"},{keywords:["none"],name:"counter-reset"},{keywords:["none"],name:"counter-set"},{inherited:!0,keywords:["auto","default","none","context-menu","help","pointer","progress","wait","cell","crosshair","text","vertical-text","alias","copy","move","no-drop","not-allowed","e-resize","n-resize","ne-resize","nw-resize","s-resize","se-resize","sw-resize","w-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","col-resize","row-resize","all-scroll","zoom-in","zoom-out","grab","grabbing"],name:"cursor"},{name:"cx"},{name:"cy"},{keywords:["none"],name:"d"},{name:"descent-override"},{inherited:!0,keywords:["ltr","rtl"],name:"direction"},{keywords:["inline","block","list-item","inline-block","table","inline-table","table-row-group","table-header-group","table-footer-group","table-row","table-column-group","table-column","table-cell","table-caption","-webkit-box","-webkit-inline-box","flex","inline-flex","grid","inline-grid","contents","flow-root","none","flow","math","ruby","ruby-text"],name:"display"},{inherited:!0,keywords:["auto","alphabetic","ideographic","middle","central","mathematical","hanging","use-script","no-change","reset-size","text-after-edge","text-before-edge"],name:"dominant-baseline"},{inherited:!0,keywords:["standard","high","constrained-high"],name:"dynamic-range-limit"},{inherited:!0,keywords:["show","hide"],name:"empty-cells"},{name:"fallback"},{keywords:["fixed","content"],name:"field-sizing"},{inherited:!0,name:"fill"},{inherited:!0,name:"fill-opacity"},{inherited:!0,keywords:["nonzero","evenodd"],name:"fill-rule"},{keywords:["none"],name:"filter"},{longhands:["flex-grow","flex-shrink","flex-basis"],name:"flex"},{keywords:["auto","fit-content","min-content","max-content","content"],name:"flex-basis"},{keywords:["row","row-reverse","column","column-reverse"],name:"flex-direction"},{longhands:["flex-direction","flex-wrap"],name:"flex-flow"},{name:"flex-grow"},{name:"flex-shrink"},{keywords:["nowrap","wrap","wrap-reverse"],name:"flex-wrap"},{keywords:["none","left","right","inline-start","inline-end"],name:"float"},{keywords:["currentcolor"],name:"flood-color"},{name:"flood-opacity"},{inherited:!0,longhands:["font-style","font-variant-ligatures","font-variant-caps","font-variant-numeric","font-variant-east-asian","font-variant-alternates","font-variant-position","font-variant-emoji","font-weight","font-stretch","font-size","line-height","font-family","font-optical-sizing","font-size-adjust","font-kerning","font-feature-settings","font-variation-settings"],name:"font"},{name:"font-display"},{inherited:!0,name:"font-family"},{inherited:!0,keywords:["normal"],name:"font-feature-settings"},{inherited:!0,keywords:["auto","normal","none"],name:"font-kerning"},{inherited:!0,keywords:["auto","none"],name:"font-optical-sizing"},{inherited:!0,keywords:["normal","light","dark"],name:"font-palette"},{inherited:!0,keywords:["xx-small","x-small","small","medium","large","x-large","xx-large","xxx-large","larger","smaller","-webkit-xxx-large"],name:"font-size"},{inherited:!0,keywords:["none","ex-height","cap-height","ch-width","ic-width","ic-height","from-font"],name:"font-size-adjust"},{inherited:!0,keywords:["normal","ultra-condensed","extra-condensed","condensed","semi-condensed","semi-expanded","expanded","extra-expanded","ultra-expanded"],name:"font-stretch"},{inherited:!0,keywords:["normal","italic","oblique"],name:"font-style"},{inherited:!0,longhands:["font-synthesis-weight","font-synthesis-style","font-synthesis-small-caps"],name:"font-synthesis"},{inherited:!0,keywords:["auto","none"],name:"font-synthesis-small-caps"},{inherited:!0,keywords:["auto","none"],name:"font-synthesis-style"},{inherited:!0,keywords:["auto","none"],name:"font-synthesis-weight"},{inherited:!0,longhands:["font-variant-ligatures","font-variant-caps","font-variant-alternates","font-variant-numeric","font-variant-east-asian","font-variant-position","font-variant-emoji"],name:"font-variant"},{inherited:!0,keywords:["normal"],name:"font-variant-alternates"},{inherited:!0,keywords:["normal","small-caps","all-small-caps","petite-caps","all-petite-caps","unicase","titling-caps"],name:"font-variant-caps"},{inherited:!0,keywords:["normal","jis78","jis83","jis90","jis04","simplified","traditional","full-width","proportional-width","ruby"],name:"font-variant-east-asian"},{inherited:!0,keywords:["normal","text","emoji","unicode"],name:"font-variant-emoji"},{inherited:!0,keywords:["normal","none","common-ligatures","no-common-ligatures","discretionary-ligatures","no-discretionary-ligatures","historical-ligatures","no-historical-ligatures","contextual","no-contextual"],name:"font-variant-ligatures"},{inherited:!0,keywords:["normal","lining-nums","oldstyle-nums","proportional-nums","tabular-nums","diagonal-fractions","stacked-fractions","ordinal","slashed-zero"],name:"font-variant-numeric"},{inherited:!0,keywords:["normal","sub","super"],name:"font-variant-position"},{inherited:!0,keywords:["normal"],name:"font-variation-settings"},{inherited:!0,keywords:["normal","bold","bolder","lighter"],name:"font-weight"},{inherited:!0,keywords:["auto","none","preserve-parent-color"],name:"forced-color-adjust"},{longhands:["row-gap","column-gap"],name:"gap"},{longhands:["grid-template-rows","grid-template-columns","grid-template-areas","grid-auto-flow","grid-auto-rows","grid-auto-columns"],name:"grid"},{longhands:["grid-row-start","grid-column-start","grid-row-end","grid-column-end"],name:"grid-area"},{keywords:["auto","min-content","max-content"],name:"grid-auto-columns"},{keywords:["row","column"],name:"grid-auto-flow"},{keywords:["auto","min-content","max-content"],name:"grid-auto-rows"},{longhands:["grid-column-start","grid-column-end"],name:"grid-column"},{keywords:["auto"],name:"grid-column-end"},{keywords:["auto"],name:"grid-column-start"},{longhands:["grid-row-start","grid-row-end"],name:"grid-row"},{keywords:["auto"],name:"grid-row-end"},{keywords:["auto"],name:"grid-row-start"},{longhands:["grid-template-rows","grid-template-columns","grid-template-areas"],name:"grid-template"},{keywords:["none"],name:"grid-template-areas"},{keywords:["none"],name:"grid-template-columns"},{keywords:["none"],name:"grid-template-rows"},{keywords:["auto","fit-content","min-content","max-content"],name:"height"},{inherited:!0,name:"hyphenate-character"},{inherited:!0,keywords:["auto"],name:"hyphenate-limit-chars"},{inherited:!0,keywords:["none","manual","auto"],name:"hyphens"},{inherited:!0,name:"image-orientation"},{inherited:!0,keywords:["auto","optimizespeed","optimizequality","-webkit-optimize-contrast","pixelated"],name:"image-rendering"},{name:"inherits"},{inherited:!1,keywords:["drop","normal","raise"],name:"initial-letter"},{name:"initial-value"},{keywords:["auto"],name:"inline-size"},{longhands:["top","right","bottom","left"],name:"inset"},{keywords:["none","top","bottom","center","left","right","x-start","x-end","y-start","y-end","start","end","self-start","self-end","all"],name:"inset-area"},{longhands:["inset-block-start","inset-block-end"],name:"inset-block"},{name:"inset-block-end"},{name:"inset-block-start"},{longhands:["inset-inline-start","inset-inline-end"],name:"inset-inline"},{name:"inset-inline-end"},{name:"inset-inline-start"},{inherited:!0,keywords:["numeric-only","allow-keywords"],name:"interpolate-size"},{keywords:["auto","isolate"],name:"isolation"},{name:"justify-content"},{name:"justify-items"},{name:"justify-self"},{keywords:["auto"],name:"left"},{inherited:!0,keywords:["normal"],name:"letter-spacing"},{keywords:["currentcolor"],name:"lighting-color"},{inherited:!0,keywords:["auto","loose","normal","strict","anywhere"],name:"line-break"},{keywords:["none","auto"],name:"line-clamp"},{name:"line-gap-override"},{inherited:!0,keywords:["normal"],name:"line-height"},{inherited:!0,longhands:["list-style-position","list-style-image","list-style-type"],name:"list-style"},{inherited:!0,keywords:["none"],name:"list-style-image"},{inherited:!0,keywords:["outside","inside"],name:"list-style-position"},{inherited:!0,keywords:["disc","circle","square","disclosure-open","disclosure-closed","decimal","none"],name:"list-style-type"},{longhands:["margin-top","margin-right","margin-bottom","margin-left"],name:"margin"},{longhands:["margin-block-start","margin-block-end"],name:"margin-block"},{keywords:["auto"],name:"margin-block-end"},{keywords:["auto"],name:"margin-block-start"},{keywords:["auto"],name:"margin-bottom"},{longhands:["margin-inline-start","margin-inline-end"],name:"margin-inline"},{keywords:["auto"],name:"margin-inline-end"},{keywords:["auto"],name:"margin-inline-start"},{keywords:["auto"],name:"margin-left"},{keywords:["auto"],name:"margin-right"},{keywords:["auto"],name:"margin-top"},{inherited:!0,longhands:["marker-start","marker-mid","marker-end"],name:"marker"},{inherited:!0,keywords:["none"],name:"marker-end"},{inherited:!0,keywords:["none"],name:"marker-mid"},{inherited:!0,keywords:["none"],name:"marker-start"},{longhands:["mask-image","-webkit-mask-position-x","-webkit-mask-position-y","mask-size","mask-repeat","mask-origin","mask-clip","mask-composite","mask-mode"],name:"mask"},{name:"mask-clip"},{name:"mask-composite"},{name:"mask-image"},{name:"mask-mode"},{name:"mask-origin"},{longhands:["-webkit-mask-position-x","-webkit-mask-position-y"],name:"mask-position"},{name:"mask-repeat"},{name:"mask-size"},{keywords:["luminance","alpha"],name:"mask-type"},{inherited:!0,name:"math-depth"},{inherited:!0,keywords:["normal","compact"],name:"math-shift"},{inherited:!0,keywords:["normal","compact"],name:"math-style"},{keywords:["none"],name:"max-block-size"},{keywords:["none"],name:"max-height"},{keywords:["none"],name:"max-inline-size"},{keywords:["none"],name:"max-width"},{name:"min-block-size"},{name:"min-height"},{name:"min-inline-size"},{name:"min-width"},{keywords:["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity","plus-lighter"],name:"mix-blend-mode"},{name:"navigation"},{name:"negative"},{keywords:["fill","contain","cover","none","scale-down"],name:"object-fit"},{name:"object-position"},{keywords:["none"],name:"object-view-box"},{longhands:["offset-position","offset-path","offset-distance","offset-rotate","offset-anchor"],name:"offset"},{keywords:["auto"],name:"offset-anchor"},{name:"offset-distance"},{keywords:["none"],name:"offset-path"},{keywords:["auto","normal"],name:"offset-position"},{keywords:["auto","reverse"],name:"offset-rotate"},{name:"opacity"},{name:"order"},{keywords:["normal","none"],name:"origin-trial-test-property"},{inherited:!0,name:"orphans"},{longhands:["outline-color","outline-style","outline-width"],name:"outline"},{keywords:["currentcolor"],name:"outline-color"},{name:"outline-offset"},{keywords:["none","hidden","inset","groove","outset","ridge","dotted","dashed","solid","double"],name:"outline-style"},{keywords:["thin","medium","thick"],name:"outline-width"},{longhands:["overflow-x","overflow-y"],name:"overflow"},{inherited:!1,keywords:["visible","none","auto"],name:"overflow-anchor"},{name:"overflow-block"},{keywords:["border-box","content-box","padding-box"],name:"overflow-clip-margin"},{name:"overflow-inline"},{inherited:!0,keywords:["normal","break-word","anywhere"],name:"overflow-wrap"},{keywords:["visible","hidden","scroll","auto","overlay","clip"],name:"overflow-x"},{keywords:["visible","hidden","scroll","auto","overlay","clip"],name:"overflow-y"},{keywords:["none","auto"],name:"overlay"},{name:"override-colors"},{longhands:["overscroll-behavior-x","overscroll-behavior-y"],name:"overscroll-behavior"},{name:"overscroll-behavior-block"},{name:"overscroll-behavior-inline"},{keywords:["auto","contain","none"],name:"overscroll-behavior-x"},{keywords:["auto","contain","none"],name:"overscroll-behavior-y"},{name:"pad"},{longhands:["padding-top","padding-right","padding-bottom","padding-left"],name:"padding"},{longhands:["padding-block-start","padding-block-end"],name:"padding-block"},{name:"padding-block-end"},{name:"padding-block-start"},{name:"padding-bottom"},{longhands:["padding-inline-start","padding-inline-end"],name:"padding-inline"},{name:"padding-inline-end"},{name:"padding-inline-start"},{name:"padding-left"},{name:"padding-right"},{name:"padding-top"},{keywords:["auto"],name:"page"},{longhands:["break-after"],name:"page-break-after"},{longhands:["break-before"],name:"page-break-before"},{longhands:["break-inside"],name:"page-break-inside"},{name:"page-orientation"},{inherited:!0,keywords:["normal","fill","stroke","markers"],name:"paint-order"},{keywords:["none"],name:"perspective"},{name:"perspective-origin"},{longhands:["align-content","justify-content"],name:"place-content"},{longhands:["align-items","justify-items"],name:"place-items"},{longhands:["align-self","justify-self"],name:"place-self"},{inherited:!0,keywords:["none","auto","stroke","fill","painted","visible","visiblestroke","visiblefill","visiblepainted","bounding-box","all"],name:"pointer-events"},{name:"popover-hide-delay"},{name:"popover-show-delay"},{keywords:["static","relative","absolute","fixed","sticky"],name:"position"},{keywords:["auto"],name:"position-anchor"},{longhands:["position-try-order","position-try-options"],name:"position-try"},{keywords:["none","flip-block","flip-inline","flip-start"],name:"position-try-fallbacks"},{name:"position-try-options"},{keywords:["normal","most-width","most-height","most-block-size","most-inline-size"],name:"position-try-order"},{keywords:["always","anchors-visible","no-overflow"],name:"position-visibility"},{name:"prefix"},{inherited:!0,keywords:["auto","none"],name:"quotes"},{name:"r"},{name:"range"},{keywords:["normal","flex-visual","flex-flow","grid-rows","grid-columns","grid-order"],name:"reading-flow"},{keywords:["none","both","horizontal","vertical","block","inline"],name:"resize"},{keywords:["auto"],name:"right"},{name:"rotate"},{keywords:["normal"],name:"row-gap"},{inherited:!0,keywords:["space-around","start","center","space-between"],name:"ruby-align"},{inherited:!0,keywords:["over","under"],name:"ruby-position"},{keywords:["auto"],name:"rx"},{keywords:["auto"],name:"ry"},{name:"scale"},{keywords:["auto","smooth"],name:"scroll-behavior"},{longhands:["scroll-margin-top","scroll-margin-right","scroll-margin-bottom","scroll-margin-left"],name:"scroll-margin"},{longhands:["scroll-margin-block-start","scroll-margin-block-end"],name:"scroll-margin-block"},{name:"scroll-margin-block-end"},{name:"scroll-margin-block-start"},{name:"scroll-margin-bottom"},{longhands:["scroll-margin-inline-start","scroll-margin-inline-end"],name:"scroll-margin-inline"},{name:"scroll-margin-inline-end"},{name:"scroll-margin-inline-start"},{name:"scroll-margin-left"},{name:"scroll-margin-right"},{name:"scroll-margin-top"},{keywords:["none","after","before"],name:"scroll-marker-group"},{longhands:["scroll-padding-top","scroll-padding-right","scroll-padding-bottom","scroll-padding-left"],name:"scroll-padding"},{longhands:["scroll-padding-block-start","scroll-padding-block-end"],name:"scroll-padding-block"},{keywords:["auto"],name:"scroll-padding-block-end"},{keywords:["auto"],name:"scroll-padding-block-start"},{keywords:["auto"],name:"scroll-padding-bottom"},{longhands:["scroll-padding-inline-start","scroll-padding-inline-end"],name:"scroll-padding-inline"},{keywords:["auto"],name:"scroll-padding-inline-end"},{keywords:["auto"],name:"scroll-padding-inline-start"},{keywords:["auto"],name:"scroll-padding-left"},{keywords:["auto"],name:"scroll-padding-right"},{keywords:["auto"],name:"scroll-padding-top"},{keywords:["none","start","end","center"],name:"scroll-snap-align"},{keywords:["normal","always"],name:"scroll-snap-stop"},{keywords:["none","x","y","block","inline","both","mandatory","proximity"],name:"scroll-snap-type"},{longhands:["scroll-start-block","scroll-start-inline"],name:"scroll-start"},{name:"scroll-start-block"},{name:"scroll-start-inline"},{longhands:["scroll-start-target-block","scroll-start-target-inline"],name:"scroll-start-target"},{name:"scroll-start-target-block"},{name:"scroll-start-target-inline"},{keywords:["none","auto"],name:"scroll-start-target-x"},{keywords:["none","auto"],name:"scroll-start-target-y"},{name:"scroll-start-x"},{name:"scroll-start-y"},{longhands:["scroll-timeline-name","scroll-timeline-axis"],name:"scroll-timeline"},{name:"scroll-timeline-axis"},{name:"scroll-timeline-name"},{inherited:!0,keywords:["auto"],name:"scrollbar-color"},{inherited:!1,keywords:["auto","stable","both-edges"],name:"scrollbar-gutter"},{inherited:!1,keywords:["auto","thin","none"],name:"scrollbar-width"},{name:"shape-image-threshold"},{keywords:["none"],name:"shape-margin"},{keywords:["none"],name:"shape-outside"},{inherited:!0,keywords:["auto","optimizespeed","crispedges","geometricprecision"],name:"shape-rendering"},{name:"size"},{name:"size-adjust"},{inherited:!0,keywords:["none","normal","spell-out","digits","literal-punctuation","no-punctuation"],name:"speak"},{name:"speak-as"},{name:"src"},{keywords:["currentcolor"],name:"stop-color"},{name:"stop-opacity"},{inherited:!0,name:"stroke"},{inherited:!0,keywords:["none"],name:"stroke-dasharray"},{inherited:!0,name:"stroke-dashoffset"},{inherited:!0,keywords:["butt","round","square"],name:"stroke-linecap"},{inherited:!0,keywords:["miter","bevel","round"],name:"stroke-linejoin"},{inherited:!0,name:"stroke-miterlimit"},{inherited:!0,name:"stroke-opacity"},{inherited:!0,name:"stroke-width"},{name:"suffix"},{name:"symbols"},{name:"syntax"},{name:"system"},{inherited:!0,name:"tab-size"},{keywords:["auto","fixed"],name:"table-layout"},{inherited:!0,keywords:["left","right","center","justify","-webkit-left","-webkit-right","-webkit-center","start","end"],name:"text-align"},{inherited:!0,keywords:["auto","start","end","left","right","center","justify"],name:"text-align-last"},{inherited:!0,keywords:["start","middle","end"],name:"text-anchor"},{inherited:!0,keywords:["normal","no-autospace"],name:"text-autospace"},{inherited:!0,name:"text-box-edge"},{keywords:["none","start","end","both"],name:"text-box-trim"},{inherited:!0,keywords:["none","all"],name:"text-combine-upright"},{longhands:["text-decoration-line","text-decoration-thickness","text-decoration-style","text-decoration-color"],name:"text-decoration"},{keywords:["currentcolor"],name:"text-decoration-color"},{keywords:["none","underline","overline","line-through","blink","spelling-error","grammar-error"],name:"text-decoration-line"},{inherited:!0,keywords:["none","auto"],name:"text-decoration-skip-ink"},{keywords:["solid","double","dotted","dashed","wavy"],name:"text-decoration-style"},{inherited:!1,keywords:["auto","from-font"],name:"text-decoration-thickness"},{inherited:!0,longhands:["text-emphasis-style","text-emphasis-color"],name:"text-emphasis"},{inherited:!0,keywords:["currentcolor"],name:"text-emphasis-color"},{inherited:!0,name:"text-emphasis-position"},{inherited:!0,name:"text-emphasis-style"},{inherited:!0,name:"text-indent"},{inherited:!0,keywords:["sideways","mixed","upright"],name:"text-orientation"},{keywords:["clip","ellipsis"],name:"text-overflow"},{inherited:!0,keywords:["auto","optimizespeed","optimizelegibility","geometricprecision"],name:"text-rendering"},{inherited:!0,keywords:["none"],name:"text-shadow"},{inherited:!0,keywords:["none","auto"],name:"text-size-adjust"},{inherited:!0,longhands:["text-autospace","text-spacing-trim"],name:"text-spacing"},{inherited:!0,keywords:["normal","space-all","space-first","trim-start"],name:"text-spacing-trim"},{inherited:!0,keywords:["capitalize","uppercase","lowercase","none","math-auto"],name:"text-transform"},{inherited:!0,keywords:["auto"],name:"text-underline-offset"},{inherited:!0,keywords:["auto","from-font","under","left","right"],name:"text-underline-position"},{inherited:!0,keywords:["wrap","nowrap","balance","pretty"],name:"text-wrap"},{name:"timeline-scope"},{keywords:["auto"],name:"top"},{keywords:["auto","none","pan-x","pan-left","pan-right","pan-y","pan-up","pan-down","pinch-zoom","manipulation"],name:"touch-action"},{keywords:["none"],name:"transform"},{keywords:["content-box","border-box","fill-box","stroke-box","view-box"],name:"transform-box"},{name:"transform-origin"},{keywords:["flat","preserve-3d"],name:"transform-style"},{longhands:["transition-property","transition-duration","transition-timing-function","transition-delay","transition-behavior"],name:"transition"},{keywords:["normal","allow-discrete"],name:"transition-behavior"},{name:"transition-delay"},{name:"transition-duration"},{keywords:["none"],name:"transition-property"},{keywords:["linear","ease","ease-in","ease-out","ease-in-out","jump-both","jump-end","jump-none","jump-start","step-start","step-end"],name:"transition-timing-function"},{name:"translate"},{name:"types"},{keywords:["normal","embed","bidi-override","isolate","plaintext","isolate-override"],name:"unicode-bidi"},{name:"unicode-range"},{inherited:!0,keywords:["auto","none","text","all","contain"],name:"user-select"},{keywords:["none","non-scaling-stroke"],name:"vector-effect"},{keywords:["baseline","sub","super","text-top","text-bottom","middle"],name:"vertical-align"},{longhands:["view-timeline-name","view-timeline-axis","view-timeline-inset"],name:"view-timeline"},{name:"view-timeline-axis"},{name:"view-timeline-inset"},{name:"view-timeline-name"},{keywords:["none"],name:"view-transition-class"},{keywords:["none"],name:"view-transition-name"},{inherited:!0,keywords:["visible","hidden","collapse"],name:"visibility"},{inherited:!0,longhands:["white-space-collapse","text-wrap"],name:"white-space"},{inherited:!0,keywords:["collapse","preserve","preserve-breaks","break-spaces"],name:"white-space-collapse"},{inherited:!0,name:"widows"},{keywords:["auto","fit-content","min-content","max-content"],name:"width"},{keywords:["auto"],name:"will-change"},{inherited:!0,keywords:["normal","break-all","keep-all","break-word","auto-phrase"],name:"word-break"},{inherited:!0,keywords:["normal"],name:"word-spacing"},{inherited:!0,keywords:["horizontal-tb","vertical-rl","vertical-lr","sideways-rl","sideways-lr"],name:"writing-mode"},{name:"x"},{name:"y"},{keywords:["auto"],name:"z-index"},{name:"zoom"}],p={"-webkit-box-align":{values:["stretch","start","center","end","baseline"]},"-webkit-box-decoration-break":{values:["slice","clone"]},"-webkit-box-direction":{values:["normal","reverse"]},"-webkit-box-orient":{values:["horizontal","vertical"]},"-webkit-box-pack":{values:["start","center","end","justify"]},"-webkit-line-break":{values:["auto","loose","normal","strict","after-white-space","anywhere"]},"-webkit-print-color-adjust":{values:["economy","exact"]},"-webkit-rtl-ordering":{values:["logical","visual"]},"-webkit-text-security":{values:["none","disc","circle","square"]},"-webkit-user-drag":{values:["auto","none","element"]},"-webkit-user-modify":{values:["read-only","read-write","read-write-plaintext-only"]},"accent-color":{values:["auto","currentcolor"]},"alignment-baseline":{values:["auto","baseline","alphabetic","ideographic","middle","central","mathematical","before-edge","text-before-edge","after-edge","text-after-edge","hanging"]},"anchor-name":{values:["none"]},"anchor-scope":{values:["none","all"]},"animation-composition":{values:["replace","add","accumulate"]},"animation-direction":{values:["normal","reverse","alternate","alternate-reverse"]},"animation-fill-mode":{values:["none","forwards","backwards","both"]},"animation-iteration-count":{values:["infinite"]},"animation-name":{values:["none"]},"animation-play-state":{values:["running","paused"]},"animation-timeline":{values:["none","auto"]},"animation-timing-function":{values:["linear","ease","ease-in","ease-out","ease-in-out","jump-both","jump-end","jump-none","jump-start","step-start","step-end"]},"app-region":{values:["none","drag","no-drag"]},"aspect-ratio":{values:["auto"]},"backdrop-filter":{values:["none"]},"backface-visibility":{values:["visible","hidden"]},"background-attachment":{values:["scroll","fixed","local"]},"background-blend-mode":{values:["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"]},"background-clip":{values:["border-box","padding-box","content-box","text"]},"background-color":{values:["currentcolor"]},"background-image":{values:["auto","none"]},"background-origin":{values:["border-box","padding-box","content-box"]},"background-size":{values:["auto","cover","contain"]},"baseline-shift":{values:["baseline","sub","super"]},"baseline-source":{values:["auto","first","last"]},"block-size":{values:["auto"]},"border-bottom-color":{values:["currentcolor"]},"border-bottom-style":{values:["none","hidden","inset","groove","outset","ridge","dotted","dashed","solid","double"]},"border-bottom-width":{values:["thin","medium","thick"]},"border-collapse":{values:["separate","collapse"]},"border-image-repeat":{values:["stretch","repeat","round","space"]},"border-image-source":{values:["none"]},"border-image-width":{values:["auto"]},"border-left-color":{values:["currentcolor"]},"border-left-style":{values:["none","hidden","inset","groove","outset","ridge","dotted","dashed","solid","double"]},"border-left-width":{values:["thin","medium","thick"]},"border-right-color":{values:["currentcolor"]},"border-right-style":{values:["none","hidden","inset","groove","outset","ridge","dotted","dashed","solid","double"]},"border-right-width":{values:["thin","medium","thick"]},"border-style":{values:["none"]},"border-top-color":{values:["currentcolor"]},"border-top-style":{values:["none","hidden","inset","groove","outset","ridge","dotted","dashed","solid","double"]},"border-top-width":{values:["thin","medium","thick"]},bottom:{values:["auto"]},"box-decoration-break":{values:["slice","clone"]},"box-shadow":{values:["none"]},"box-sizing":{values:["content-box","border-box"]},"break-after":{values:["auto","avoid","avoid-column","avoid-page","column","left","page","recto","right","verso"]},"break-before":{values:["auto","avoid","avoid-column","avoid-page","column","left","page","recto","right","verso"]},"break-inside":{values:["auto","avoid","avoid-column","avoid-page"]},"buffered-rendering":{values:["auto","dynamic","static"]},"caption-side":{values:["top","bottom"]},"caret-color":{values:["auto","currentcolor"]},clear:{values:["none","left","right","both","inline-start","inline-end"]},clip:{values:["auto"]},"clip-path":{values:["border-box","padding-box","content-box","margin-box","fill-box","stroke-box","view-box","none"]},"clip-rule":{values:["nonzero","evenodd"]},color:{values:["currentcolor"]},"color-interpolation":{values:["auto","srgb","linearrgb"]},"color-interpolation-filters":{values:["auto","srgb","linearrgb"]},"color-rendering":{values:["auto","optimizespeed","optimizequality"]},"column-count":{values:["auto"]},"column-fill":{values:["balance","auto"]},"column-gap":{values:["normal"]},"column-rule-color":{values:["currentcolor"]},"column-rule-style":{values:["none","hidden","inset","groove","outset","ridge","dotted","dashed","solid","double"]},"column-rule-width":{values:["thin","medium","thick"]},"column-span":{values:["none","all"]},"column-width":{values:["auto"]},contain:{values:["none","strict","content","size","layout","style","paint","inline-size","block-size"]},"contain-intrinsic-height":{values:["none"]},"contain-intrinsic-width":{values:["none"]},"container-name":{values:["none"]},"container-type":{values:["normal","inline-size","size","scroll-state"]},"content-visibility":{values:["visible","auto","hidden"]},"counter-increment":{values:["none"]},"counter-reset":{values:["none"]},"counter-set":{values:["none"]},cursor:{values:["auto","default","none","context-menu","help","pointer","progress","wait","cell","crosshair","text","vertical-text","alias","copy","move","no-drop","not-allowed","e-resize","n-resize","ne-resize","nw-resize","s-resize","se-resize","sw-resize","w-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","col-resize","row-resize","all-scroll","zoom-in","zoom-out","grab","grabbing"]},d:{values:["none"]},direction:{values:["ltr","rtl"]},display:{values:["inline","block","list-item","inline-block","table","inline-table","table-row-group","table-header-group","table-footer-group","table-row","table-column-group","table-column","table-cell","table-caption","-webkit-box","-webkit-inline-box","flex","inline-flex","grid","inline-grid","contents","flow-root","none","flow","math","ruby","ruby-text"]},"dominant-baseline":{values:["auto","alphabetic","ideographic","middle","central","mathematical","hanging","use-script","no-change","reset-size","text-after-edge","text-before-edge"]},"dynamic-range-limit":{values:["standard","high","constrained-high"]},"empty-cells":{values:["show","hide"]},"field-sizing":{values:["fixed","content"]},"fill-rule":{values:["nonzero","evenodd"]},filter:{values:["none"]},"flex-basis":{values:["auto","fit-content","min-content","max-content","content"]},"flex-direction":{values:["row","row-reverse","column","column-reverse"]},"flex-wrap":{values:["nowrap","wrap","wrap-reverse"]},float:{values:["none","left","right","inline-start","inline-end"]},"flood-color":{values:["currentcolor"]},"font-feature-settings":{values:["normal"]},"font-kerning":{values:["auto","normal","none"]},"font-optical-sizing":{values:["auto","none"]},"font-palette":{values:["normal","light","dark"]},"font-size":{values:["xx-small","x-small","small","medium","large","x-large","xx-large","xxx-large","larger","smaller","-webkit-xxx-large"]},"font-size-adjust":{values:["none","ex-height","cap-height","ch-width","ic-width","ic-height","from-font"]},"font-stretch":{values:["normal","ultra-condensed","extra-condensed","condensed","semi-condensed","semi-expanded","expanded","extra-expanded","ultra-expanded"]},"font-style":{values:["normal","italic","oblique"]},"font-synthesis-small-caps":{values:["auto","none"]},"font-synthesis-style":{values:["auto","none"]},"font-synthesis-weight":{values:["auto","none"]},"font-variant-alternates":{values:["normal"]},"font-variant-caps":{values:["normal","small-caps","all-small-caps","petite-caps","all-petite-caps","unicase","titling-caps"]},"font-variant-east-asian":{values:["normal","jis78","jis83","jis90","jis04","simplified","traditional","full-width","proportional-width","ruby"]},"font-variant-emoji":{values:["normal","text","emoji","unicode"]},"font-variant-ligatures":{values:["normal","none","common-ligatures","no-common-ligatures","discretionary-ligatures","no-discretionary-ligatures","historical-ligatures","no-historical-ligatures","contextual","no-contextual"]},"font-variant-numeric":{values:["normal","lining-nums","oldstyle-nums","proportional-nums","tabular-nums","diagonal-fractions","stacked-fractions","ordinal","slashed-zero"]},"font-variant-position":{values:["normal","sub","super"]},"font-variation-settings":{values:["normal"]},"font-weight":{values:["normal","bold","bolder","lighter"]},"forced-color-adjust":{values:["auto","none","preserve-parent-color"]},"grid-auto-columns":{values:["auto","min-content","max-content"]},"grid-auto-flow":{values:["row","column"]},"grid-auto-rows":{values:["auto","min-content","max-content"]},"grid-column-end":{values:["auto"]},"grid-column-start":{values:["auto"]},"grid-row-end":{values:["auto"]},"grid-row-start":{values:["auto"]},"grid-template-areas":{values:["none"]},"grid-template-columns":{values:["none"]},"grid-template-rows":{values:["none"]},height:{values:["auto","fit-content","min-content","max-content"]},"hyphenate-limit-chars":{values:["auto"]},hyphens:{values:["none","manual","auto"]},"image-rendering":{values:["auto","optimizespeed","optimizequality","-webkit-optimize-contrast","pixelated"]},"initial-letter":{values:["drop","normal","raise"]},"inline-size":{values:["auto"]},"inset-area":{values:["none","top","bottom","center","left","right","x-start","x-end","y-start","y-end","start","end","self-start","self-end","all"]},"interpolate-size":{values:["numeric-only","allow-keywords"]},isolation:{values:["auto","isolate"]},left:{values:["auto"]},"letter-spacing":{values:["normal"]},"lighting-color":{values:["currentcolor"]},"line-break":{values:["auto","loose","normal","strict","anywhere"]},"line-clamp":{values:["none","auto"]},"line-height":{values:["normal"]},"list-style-image":{values:["none"]},"list-style-position":{values:["outside","inside"]},"list-style-type":{values:["disc","circle","square","disclosure-open","disclosure-closed","decimal","none"]},"margin-block-end":{values:["auto"]},"margin-block-start":{values:["auto"]},"margin-bottom":{values:["auto"]},"margin-inline-end":{values:["auto"]},"margin-inline-start":{values:["auto"]},"margin-left":{values:["auto"]},"margin-right":{values:["auto"]},"margin-top":{values:["auto"]},"marker-end":{values:["none"]},"marker-mid":{values:["none"]},"marker-start":{values:["none"]},"mask-type":{values:["luminance","alpha"]},"math-shift":{values:["normal","compact"]},"math-style":{values:["normal","compact"]},"max-block-size":{values:["none"]},"max-height":{values:["none"]},"max-inline-size":{values:["none"]},"max-width":{values:["none"]},"mix-blend-mode":{values:["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity","plus-lighter"]},"object-fit":{values:["fill","contain","cover","none","scale-down"]},"object-view-box":{values:["none"]},"offset-anchor":{values:["auto"]},"offset-path":{values:["none"]},"offset-position":{values:["auto","normal"]},"offset-rotate":{values:["auto","reverse"]},"origin-trial-test-property":{values:["normal","none"]},"outline-color":{values:["currentcolor"]},"outline-style":{values:["none","hidden","inset","groove","outset","ridge","dotted","dashed","solid","double"]},"outline-width":{values:["thin","medium","thick"]},"overflow-anchor":{values:["visible","none","auto"]},"overflow-clip-margin":{values:["border-box","content-box","padding-box"]},"overflow-wrap":{values:["normal","break-word","anywhere"]},"overflow-x":{values:["visible","hidden","scroll","auto","overlay","clip"]},"overflow-y":{values:["visible","hidden","scroll","auto","overlay","clip"]},overlay:{values:["none","auto"]},"overscroll-behavior-x":{values:["auto","contain","none"]},"overscroll-behavior-y":{values:["auto","contain","none"]},page:{values:["auto"]},"paint-order":{values:["normal","fill","stroke","markers"]},perspective:{values:["none"]},"pointer-events":{values:["none","auto","stroke","fill","painted","visible","visiblestroke","visiblefill","visiblepainted","bounding-box","all"]},position:{values:["static","relative","absolute","fixed","sticky"]},"position-anchor":{values:["auto"]},"position-try-fallbacks":{values:["none","flip-block","flip-inline","flip-start"]},"position-try-order":{values:["normal","most-width","most-height","most-block-size","most-inline-size"]},"position-visibility":{values:["always","anchors-visible","no-overflow"]},quotes:{values:["auto","none"]},"reading-flow":{values:["normal","flex-visual","flex-flow","grid-rows","grid-columns","grid-order"]},resize:{values:["none","both","horizontal","vertical","block","inline"]},right:{values:["auto"]},"row-gap":{values:["normal"]},"ruby-align":{values:["space-around","start","center","space-between"]},"ruby-position":{values:["over","under"]},rx:{values:["auto"]},ry:{values:["auto"]},"scroll-behavior":{values:["auto","smooth"]},"scroll-marker-group":{values:["none","after","before"]},"scroll-padding-block-end":{values:["auto"]},"scroll-padding-block-start":{values:["auto"]},"scroll-padding-bottom":{values:["auto"]},"scroll-padding-inline-end":{values:["auto"]},"scroll-padding-inline-start":{values:["auto"]},"scroll-padding-left":{values:["auto"]},"scroll-padding-right":{values:["auto"]},"scroll-padding-top":{values:["auto"]},"scroll-snap-align":{values:["none","start","end","center"]},"scroll-snap-stop":{values:["normal","always"]},"scroll-snap-type":{values:["none","x","y","block","inline","both","mandatory","proximity"]},"scroll-start-target-x":{values:["none","auto"]},"scroll-start-target-y":{values:["none","auto"]},"scrollbar-color":{values:["auto"]},"scrollbar-gutter":{values:["auto","stable","both-edges"]},"scrollbar-width":{values:["auto","thin","none"]},"shape-margin":{values:["none"]},"shape-outside":{values:["none"]},"shape-rendering":{values:["auto","optimizespeed","crispedges","geometricprecision"]},speak:{values:["none","normal","spell-out","digits","literal-punctuation","no-punctuation"]},"stop-color":{values:["currentcolor"]},"stroke-dasharray":{values:["none"]},"stroke-linecap":{values:["butt","round","square"]},"stroke-linejoin":{values:["miter","bevel","round"]},"table-layout":{values:["auto","fixed"]},"text-align":{values:["left","right","center","justify","-webkit-left","-webkit-right","-webkit-center","start","end"]},"text-align-last":{values:["auto","start","end","left","right","center","justify"]},"text-anchor":{values:["start","middle","end"]},"text-autospace":{values:["normal","no-autospace"]},"text-box-trim":{values:["none","start","end","both"]},"text-combine-upright":{values:["none","all"]},"text-decoration-color":{values:["currentcolor"]},"text-decoration-line":{values:["none","underline","overline","line-through","blink","spelling-error","grammar-error"]},"text-decoration-skip-ink":{values:["none","auto"]},"text-decoration-style":{values:["solid","double","dotted","dashed","wavy"]},"text-decoration-thickness":{values:["auto","from-font"]},"text-emphasis-color":{values:["currentcolor"]},"text-orientation":{values:["sideways","mixed","upright"]},"text-overflow":{values:["clip","ellipsis"]},"text-rendering":{values:["auto","optimizespeed","optimizelegibility","geometricprecision"]},"text-shadow":{values:["none"]},"text-size-adjust":{values:["none","auto"]},"text-spacing-trim":{values:["normal","space-all","space-first","trim-start"]},"text-transform":{values:["capitalize","uppercase","lowercase","none","math-auto"]},"text-underline-offset":{values:["auto"]},"text-underline-position":{values:["auto","from-font","under","left","right"]},"text-wrap":{values:["wrap","nowrap","balance","pretty"]},top:{values:["auto"]},"touch-action":{values:["auto","none","pan-x","pan-left","pan-right","pan-y","pan-up","pan-down","pinch-zoom","manipulation"]},transform:{values:["none"]},"transform-box":{values:["content-box","border-box","fill-box","stroke-box","view-box"]},"transform-style":{values:["flat","preserve-3d"]},"transition-behavior":{values:["normal","allow-discrete"]},"transition-property":{values:["none"]},"transition-timing-function":{values:["linear","ease","ease-in","ease-out","ease-in-out","jump-both","jump-end","jump-none","jump-start","step-start","step-end"]},"unicode-bidi":{values:["normal","embed","bidi-override","isolate","plaintext","isolate-override"]},"user-select":{values:["auto","none","text","all","contain"]},"vector-effect":{values:["none","non-scaling-stroke"]},"vertical-align":{values:["baseline","sub","super","text-top","text-bottom","middle"]},"view-transition-class":{values:["none"]},"view-transition-name":{values:["none"]},visibility:{values:["visible","hidden","collapse"]},"white-space-collapse":{values:["collapse","preserve","preserve-breaks","break-spaces"]},width:{values:["auto","fit-content","min-content","max-content"]},"will-change":{values:["auto"]},"word-break":{values:["normal","break-all","keep-all","break-word","auto-phrase"]},"word-spacing":{values:["normal"]},"writing-mode":{values:["horizontal-tb","vertical-rl","vertical-lr","sideways-rl","sideways-lr"]},"z-index":{values:["auto"]}},m=new Map([["-epub-caption-side","caption-side"],["-epub-text-combine","-webkit-text-combine"],["-epub-text-emphasis","text-emphasis"],["-epub-text-emphasis-color","text-emphasis-color"],["-epub-text-emphasis-style","text-emphasis-style"],["-epub-text-orientation","-webkit-text-orientation"],["-epub-text-transform","text-transform"],["-epub-word-break","word-break"],["-epub-writing-mode","-webkit-writing-mode"],["-webkit-align-content","align-content"],["-webkit-align-items","align-items"],["-webkit-align-self","align-self"],["-webkit-alternative-animation-with-timeline","-alternative-animation-with-timeline"],["-webkit-animation","animation"],["-webkit-animation-delay","animation-delay"],["-webkit-animation-direction","animation-direction"],["-webkit-animation-duration","animation-duration"],["-webkit-animation-fill-mode","animation-fill-mode"],["-webkit-animation-iteration-count","animation-iteration-count"],["-webkit-animation-name","animation-name"],["-webkit-animation-play-state","animation-play-state"],["-webkit-animation-timing-function","animation-timing-function"],["-webkit-app-region","app-region"],["-webkit-appearance","appearance"],["-webkit-backface-visibility","backface-visibility"],["-webkit-background-clip","background-clip"],["-webkit-background-origin","background-origin"],["-webkit-background-size","background-size"],["-webkit-border-after","border-block-end"],["-webkit-border-after-color","border-block-end-color"],["-webkit-border-after-style","border-block-end-style"],["-webkit-border-after-width","border-block-end-width"],["-webkit-border-before","border-block-start"],["-webkit-border-before-color","border-block-start-color"],["-webkit-border-before-style","border-block-start-style"],["-webkit-border-before-width","border-block-start-width"],["-webkit-border-bottom-left-radius","border-bottom-left-radius"],["-webkit-border-bottom-right-radius","border-bottom-right-radius"],["-webkit-border-end","border-inline-end"],["-webkit-border-end-color","border-inline-end-color"],["-webkit-border-end-style","border-inline-end-style"],["-webkit-border-end-width","border-inline-end-width"],["-webkit-border-radius","border-radius"],["-webkit-border-start","border-inline-start"],["-webkit-border-start-color","border-inline-start-color"],["-webkit-border-start-style","border-inline-start-style"],["-webkit-border-start-width","border-inline-start-width"],["-webkit-border-top-left-radius","border-top-left-radius"],["-webkit-border-top-right-radius","border-top-right-radius"],["-webkit-box-shadow","box-shadow"],["-webkit-box-sizing","box-sizing"],["-webkit-clip-path","clip-path"],["-webkit-column-count","column-count"],["-webkit-column-gap","column-gap"],["-webkit-column-rule","column-rule"],["-webkit-column-rule-color","column-rule-color"],["-webkit-column-rule-style","column-rule-style"],["-webkit-column-rule-width","column-rule-width"],["-webkit-column-span","column-span"],["-webkit-column-width","column-width"],["-webkit-columns","columns"],["-webkit-filter","filter"],["-webkit-flex","flex"],["-webkit-flex-basis","flex-basis"],["-webkit-flex-direction","flex-direction"],["-webkit-flex-flow","flex-flow"],["-webkit-flex-grow","flex-grow"],["-webkit-flex-shrink","flex-shrink"],["-webkit-flex-wrap","flex-wrap"],["-webkit-font-feature-settings","font-feature-settings"],["-webkit-hyphenate-character","hyphenate-character"],["-webkit-justify-content","justify-content"],["-webkit-logical-height","block-size"],["-webkit-logical-width","inline-size"],["-webkit-margin-after","margin-block-end"],["-webkit-margin-before","margin-block-start"],["-webkit-margin-end","margin-inline-end"],["-webkit-margin-start","margin-inline-start"],["-webkit-mask","mask"],["-webkit-mask-clip","mask-clip"],["-webkit-mask-composite","mask-composite"],["-webkit-mask-image","mask-image"],["-webkit-mask-origin","mask-origin"],["-webkit-mask-position","mask-position"],["-webkit-mask-repeat","mask-repeat"],["-webkit-mask-size","mask-size"],["-webkit-max-logical-height","max-block-size"],["-webkit-max-logical-width","max-inline-size"],["-webkit-min-logical-height","min-block-size"],["-webkit-min-logical-width","min-inline-size"],["-webkit-opacity","opacity"],["-webkit-order","order"],["-webkit-padding-after","padding-block-end"],["-webkit-padding-before","padding-block-start"],["-webkit-padding-end","padding-inline-end"],["-webkit-padding-start","padding-inline-start"],["-webkit-perspective","perspective"],["-webkit-perspective-origin","perspective-origin"],["-webkit-shape-image-threshold","shape-image-threshold"],["-webkit-shape-margin","shape-margin"],["-webkit-shape-outside","shape-outside"],["-webkit-text-emphasis","text-emphasis"],["-webkit-text-emphasis-color","text-emphasis-color"],["-webkit-text-emphasis-position","text-emphasis-position"],["-webkit-text-emphasis-style","text-emphasis-style"],["-webkit-text-size-adjust","text-size-adjust"],["-webkit-transform","transform"],["-webkit-transform-origin","transform-origin"],["-webkit-transform-style","transform-style"],["-webkit-transition","transition"],["-webkit-transition-delay","transition-delay"],["-webkit-transition-duration","transition-duration"],["-webkit-transition-property","transition-property"],["-webkit-transition-timing-function","transition-timing-function"],["-webkit-user-select","user-select"],["grid-column-gap","column-gap"],["grid-gap","gap"],["grid-row-gap","row-gap"],["word-wrap","overflow-wrap"]]);class f{#t;#n;#r;#s;#i;#o;#a;#l;#d;#c;constructor(e,t){this.#t=[],this.#n=new Map,this.#r=new Map,this.#s=new Set,this.#i=new Set,this.#o=new Map,this.#a=t;for(let t=0;t<e.length;++t){const n=e[t],r=n.name;if(!CSS.supports(r,"initial"))continue;this.#t.push(r),n.inherited&&this.#s.add(r),n.svg&&this.#i.add(r);const s=e[t].longhands;if(s){this.#n.set(r,s);for(let e=0;e<s.length;++e){const t=s[e];let n=this.#r.get(t);n||(n=[],this.#r.set(t,n)),n.push(r)}}}this.#t.sort(f.sortPrefixesAndCSSWideKeywordsToEnd),this.#l=new Set(this.#t);const n=new Map;for(const[e,t]of Object.entries(p))n.set(e,new Set(t.values));for(const[e,t]of P){const r=n.get(e);r?n.set(e,r.union(t)):n.set(e,t)}for(const[e,t]of n){for(const n of E)!t.has(n)&&CSS.supports(e,n)&&t.add(n);this.#o.set(e,[...t])}this.#d=[],this.#c=[];for(const e of this.#l){const t=this.specificPropertyValues(e).filter((t=>CSS.supports(e,t))).sort(f.sortPrefixesAndCSSWideKeywordsToEnd).map((t=>`${e}: ${t}`));this.isSVGProperty(e)||this.#d.push(...t),this.#c.push(...t)}}static sortPrefixesAndCSSWideKeywordsToEnd(e,t){const n=b.includes(e),r=b.includes(t);if(n&&!r)return 1;if(!n&&r)return-1;const s=e.startsWith("-webkit-"),i=t.startsWith("-webkit-");return s&&!i?1:!s&&i||e<t?-1:e>t?1:0}allProperties(){return this.#t}aliasesFor(){return this.#a}nameValuePresets(e){return e?this.#c:this.#d}isSVGProperty(e){return e=e.toLowerCase(),this.#i.has(e)}getLonghands(e){return this.#n.get(e)||null}getShorthands(e){return this.#r.get(e)||null}isColorAwareProperty(e){return T.has(e.toLowerCase())||this.isCustomProperty(e.toLowerCase())}isFontFamilyProperty(e){return"font-family"===e.toLowerCase()}isAngleAwareProperty(e){const t=e.toLowerCase();return T.has(t)||M.has(t)}isGridAreaDefiningProperty(e){return"grid"===(e=e.toLowerCase())||"grid-template"===e||"grid-template-areas"===e}isLengthProperty(e){return"line-height"!==(e=e.toLowerCase())&&(C.has(e)||e.startsWith("margin")||e.startsWith("padding")||-1!==e.indexOf("width")||-1!==e.indexOf("height"))}isBezierAwareProperty(e){return e=e.toLowerCase(),R.has(e)||this.isCustomProperty(e)}isFontAwareProperty(e){return e=e.toLowerCase(),x.has(e)||this.isCustomProperty(e)}isCustomProperty(e){return e.startsWith("--")}isShadowProperty(e){return"box-shadow"===(e=e.toLowerCase())||"text-shadow"===e||"-webkit-box-shadow"===e}isStringProperty(e){return"content"===(e=e.toLowerCase())}canonicalPropertyName(e){if(this.isCustomProperty(e))return e;e=e.toLowerCase();const t=this.#a.get(e);if(t)return t;if(!e||e.length<9||"-"!==e.charAt(0))return e;const n=e.match(/(?:-webkit-)(.+)/);return n&&this.#l.has(n[1])?n[1]:e}isCSSPropertyName(e){return!!((e=e.toLowerCase()).startsWith("--")&&e.length>2||e.startsWith("-moz-")||e.startsWith("-ms-")||e.startsWith("-o-")||e.startsWith("-webkit-"))||this.#l.has(e)}isPropertyInherited(e){return(e=e.toLowerCase()).startsWith("--")||this.#s.has(this.canonicalPropertyName(e))||this.#s.has(e)}specificPropertyValues(e){const t=e.replace(/^-webkit-/,""),n=this.#o;let r=n.get(e)||n.get(t);if(!r){r=[];for(const t of E)CSS.supports(e,t)&&r.push(t);n.set(e,r)}return r}getPropertyValues(t){t=t.toLowerCase();const n=[...this.specificPropertyValues(t),...b];if(this.isColorAwareProperty(t)){n.push("currentColor");for(const t of e.Color.Nicknames.keys())n.push(t)}return n.sort(f.sortPrefixesAndCSSWideKeywordsToEnd)}propertyUsageWeight(e){return L.get(e)||L.get(this.canonicalPropertyName(e))||0}getValuePreset(e,t){const n=w.get(e);let r=n?n.get(t):null;if(!r)return null;let s=r.length,i=r.length;return r&&(s=r.indexOf("|"),i=r.lastIndexOf("|"),i=s===i?i:i-1,r=r.replace(/\|/g,"")),{text:r,startColumn:s,endColumn:i}}isHighlightPseudoType(e){return"highlight"===e||"selection"===e||"target-text"===e||"grammar-error"===e||"spelling-error"===e}}const b=["inherit","initial","revert","revert-layer","unset"],y=/((?:\[[\w\- ]+\]\s*)*(?:"[^"]+"|'[^']+'))[^'"\[]*\[?[^'"\[]*/;let I=null;function v(){if(!I){I=new f(g,m)}return I}const k=new Map([["linear-gradient","linear-gradient(|45deg, black, transparent|)"],["radial-gradient","radial-gradient(|black, transparent|)"],["repeating-linear-gradient","repeating-linear-gradient(|45deg, black, transparent 100px|)"],["repeating-radial-gradient","repeating-radial-gradient(|black, transparent 100px|)"],["url","url(||)"]]),S=new Map([["blur","blur(|1px|)"],["brightness","brightness(|0.5|)"],["contrast","contrast(|0.5|)"],["drop-shadow","drop-shadow(|2px 4px 6px black|)"],["grayscale","grayscale(|1|)"],["hue-rotate","hue-rotate(|45deg|)"],["invert","invert(|1|)"],["opacity","opacity(|0.5|)"],["saturate","saturate(|0.5|)"],["sepia","sepia(|1|)"],["url","url(||)"]]),w=new Map([["filter",S],["backdrop-filter",S],["background",k],["background-image",k],["-webkit-mask-image",k],["transform",new Map([["scale","scale(|1.5|)"],["scaleX","scaleX(|1.5|)"],["scaleY","scaleY(|1.5|)"],["scale3d","scale3d(|1.5, 1.5, 1.5|)"],["rotate","rotate(|45deg|)"],["rotateX","rotateX(|45deg|)"],["rotateY","rotateY(|45deg|)"],["rotateZ","rotateZ(|45deg|)"],["rotate3d","rotate3d(|1, 1, 1, 45deg|)"],["skew","skew(|10deg, 10deg|)"],["skewX","skewX(|10deg|)"],["skewY","skewY(|10deg|)"],["translate","translate(|10px, 10px|)"],["translateX","translateX(|10px|)"],["translateY","translateY(|10px|)"],["translateZ","translateZ(|10px|)"],["translate3d","translate3d(|10px, 10px, 10px|)"],["matrix","matrix(|1, 0, 0, 1, 0, 0|)"],["matrix3d","matrix3d(|1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1|)"],["perspective","perspective(|10px|)"]])]]),C=new Set(["background-position","border-spacing","bottom","font-size","height","left","letter-spacing","max-height","max-width","min-height","min-width","right","text-indent","top","width","word-spacing","grid-row-gap","grid-column-gap","row-gap"]),R=new Set(["animation","animation-timing-function","transition","transition-timing-function","-webkit-animation","-webkit-animation-timing-function","-webkit-transition","-webkit-transition-timing-function"]),x=new Set(["font-size","line-height","font-weight","font-family","letter-spacing"]),T=new Set(["accent-color","background","background-color","background-image","border","border-color","border-image","border-image-source","border-bottom","border-bottom-color","border-left","border-left-color","border-right","border-right-color","border-top","border-top-color","border-block-end","border-block-end-color","border-block-start","border-block-start-color","border-inline-end","border-inline-end-color","border-inline-start","border-inline-start-color","box-shadow","caret-color","color","column-rule","column-rule-color","content","fill","list-style-image","mask","mask-image","mask-border","mask-border-source","outline","outline-color","scrollbar-color","stop-color","stroke","text-decoration-color","text-shadow","-webkit-border-after","-webkit-border-after-color","-webkit-border-before","-webkit-border-before-color","-webkit-border-end","-webkit-border-end-color","-webkit-border-start","-webkit-border-start-color","-webkit-box-reflect","-webkit-box-shadow","-webkit-column-rule-color","-webkit-mask","-webkit-mask-box-image","-webkit-mask-box-image-source","-webkit-mask-image","-webkit-tap-highlight-color","-webkit-text-emphasis","-webkit-text-emphasis-color","-webkit-text-fill-color","-webkit-text-stroke","-webkit-text-stroke-color"]),M=new Set(["-webkit-border-image","transform","-webkit-transform","rotate","filter","-webkit-filter","backdrop-filter","offset","offset-rotate","font-style"]),P=new Map([["background-repeat",new Set(["repeat","repeat-x","repeat-y","no-repeat","space","round"])],["content",new Set(["normal","close-quote","no-close-quote","no-open-quote","open-quote"])],["baseline-shift",new Set(["baseline"])],["max-height",new Set(["min-content","max-content","-webkit-fill-available","fit-content"])],["color",new Set(["black"])],["background-color",new Set(["white"])],["box-shadow",new Set(["inset"])],["text-shadow",new Set(["0 0 black"])],["-webkit-writing-mode",new Set(["horizontal-tb","vertical-rl","vertical-lr"])],["writing-mode",new Set(["lr","rl","tb","lr-tb","rl-tb","tb-rl"])],["page-break-inside",new Set(["avoid"])],["cursor",new Set(["-webkit-zoom-in","-webkit-zoom-out","-webkit-grab","-webkit-grabbing"])],["border-width",new Set(["medium","thick","thin"])],["border-style",new Set(["hidden","inset","groove","ridge","outset","dotted","dashed","solid","double"])],["size",new Set(["a3","a4","a5","b4","b5","landscape","ledger","legal","letter","portrait"])],["overflow",new Set(["hidden","visible","overlay","scroll"])],["overscroll-behavior",new Set(["contain"])],["text-rendering",new Set(["optimizeSpeed","optimizeLegibility","geometricPrecision"])],["text-align",new Set(["-webkit-auto","-webkit-match-parent"])],["clip-path",new Set(["circle","ellipse","inset","polygon","url"])],["color-interpolation",new Set(["sRGB","linearRGB"])],["word-wrap",new Set(["normal","break-word"])],["font-weight",new Set(["100","200","300","400","500","600","700","800","900"])],["-webkit-text-emphasis",new Set(["circle","filled","open","dot","double-circle","triangle","sesame"])],["color-rendering",new Set(["optimizeSpeed","optimizeQuality"])],["-webkit-text-combine",new Set(["horizontal"])],["text-orientation",new Set(["sideways-right"])],["outline",new Set(["inset","groove","ridge","outset","dotted","dashed","solid","double","medium","thick","thin"])],["font",new Set(["caption","icon","menu","message-box","small-caption","-webkit-mini-control","-webkit-small-control","-webkit-control","status-bar"])],["dominant-baseline",new Set(["text-before-edge","text-after-edge","use-script","no-change","reset-size"])],["-webkit-text-emphasis-position",new Set(["over","under"])],["alignment-baseline",new Set(["before-edge","after-edge","text-before-edge","text-after-edge","hanging"])],["page-break-before",new Set(["left","right","always","avoid"])],["border-image",new Set(["repeat","stretch","space","round"])],["text-decoration",new Set(["blink","line-through","overline","underline","wavy","double","solid","dashed","dotted"])],["font-family",new Set(["serif","sans-serif","cursive","fantasy","monospace","system-ui","emoji","math","fangsong","ui-serif","ui-sans-serif","ui-monospace","ui-rounded","-webkit-body"])],["zoom",new Set(["normal"])],["max-width",new Set(["min-content","max-content","-webkit-fill-available","fit-content"])],["-webkit-font-smoothing",new Set(["antialiased","subpixel-antialiased"])],["border",new Set(["hidden","inset","groove","ridge","outset","dotted","dashed","solid","double","medium","thick","thin"])],["font-variant",new Set(["small-caps","normal","common-ligatures","no-common-ligatures","discretionary-ligatures","no-discretionary-ligatures","historical-ligatures","no-historical-ligatures","contextual","no-contextual","all-small-caps","petite-caps","all-petite-caps","unicase","titling-caps","lining-nums","oldstyle-nums","proportional-nums","tabular-nums","diagonal-fractions","stacked-fractions","ordinal","slashed-zero","jis78","jis83","jis90","jis04","simplified","traditional","full-width","proportional-width","ruby"])],["vertical-align",new Set(["top","bottom","-webkit-baseline-middle"])],["page-break-after",new Set(["left","right","always","avoid"])],["-webkit-text-emphasis-style",new Set(["circle","filled","open","dot","double-circle","triangle","sesame"])],["transform",new Set(["scale","scaleX","scaleY","scale3d","rotate","rotateX","rotateY","rotateZ","rotate3d","skew","skewX","skewY","translate","translateX","translateY","translateZ","translate3d","matrix","matrix3d","perspective"])],["align-content",new Set(["normal","baseline","space-between","space-around","space-evenly","stretch","center","start","end","flex-start","flex-end"])],["justify-content",new Set(["normal","space-between","space-around","space-evenly","stretch","center","start","end","flex-start","flex-end","left","right"])],["place-content",new Set(["normal","space-between","space-around","space-evenly","stretch","center","start","end","flex-start","flex-end","baseline"])],["align-items",new Set(["normal","stretch","baseline","center","start","end","self-start","self-end","flex-start","flex-end"])],["justify-items",new Set(["normal","stretch","baseline","center","start","end","self-start","self-end","flex-start","flex-end","left","right","legacy"])],["place-items",new Set(["normal","stretch","baseline","center","start","end","self-start","self-end","flex-start","flex-end"])],["align-self",new Set(["normal","stretch","baseline","center","start","end","self-start","self-end","flex-start","flex-end"])],["justify-self",new Set(["normal","stretch","baseline","center","start","end","self-start","self-end","flex-start","flex-end","left","right"])],["place-self",new Set(["normal","stretch","baseline","center","start","end","self-start","self-end","flex-start","flex-end"])],["perspective-origin",new Set(["left","center","right","top","bottom"])],["transform-origin",new Set(["left","center","right","top","bottom"])],["transition-timing-function",new Set(["cubic-bezier","steps"])],["animation-timing-function",new Set(["cubic-bezier","steps"])],["-webkit-backface-visibility",new Set(["visible","hidden"])],["-webkit-column-break-after",new Set(["always","avoid"])],["-webkit-column-break-before",new Set(["always","avoid"])],["-webkit-column-break-inside",new Set(["avoid"])],["-webkit-column-span",new Set(["all"])],["-webkit-column-gap",new Set(["normal"])],["filter",new Set(["url","blur","brightness","contrast","drop-shadow","grayscale","hue-rotate","invert","opacity","saturate","sepia"])],["backdrop-filter",new Set(["url","blur","brightness","contrast","drop-shadow","grayscale","hue-rotate","invert","opacity","saturate","sepia"])],["grid-template-columns",new Set(["min-content","max-content"])],["grid-template-rows",new Set(["min-content","max-content"])],["grid-auto-flow",new Set(["dense"])],["background",new Set(["repeat","repeat-x","repeat-y","no-repeat","top","bottom","left","right","center","fixed","local","scroll","space","round","border-box","content-box","padding-box","linear-gradient","radial-gradient","repeating-linear-gradient","repeating-radial-gradient","url"])],["background-image",new Set(["linear-gradient","radial-gradient","repeating-linear-gradient","repeating-radial-gradient","url"])],["background-position",new Set(["top","bottom","left","right","center"])],["background-position-x",new Set(["left","right","center"])],["background-position-y",new Set(["top","bottom","center"])],["background-repeat-x",new Set(["repeat","no-repeat"])],["background-repeat-y",new Set(["repeat","no-repeat"])],["border-bottom",new Set(["hidden","inset","groove","outset","ridge","dotted","dashed","solid","double","medium","thick","thin"])],["border-left",new Set(["hidden","inset","groove","outset","ridge","dotted","dashed","solid","double","medium","thick","thin"])],["border-right",new Set(["hidden","inset","groove","outset","ridge","dotted","dashed","solid","double","medium","thick","thin"])],["border-top",new Set(["hidden","inset","groove","outset","ridge","dotted","dashed","solid","double","medium","thick","thin"])],["buffered-rendering",new Set(["static","dynamic"])],["color-interpolation-filters",new Set(["srgb","linearrgb"])],["column-rule",new Set(["hidden","inset","groove","outset","ridge","dotted","dashed","solid","double","medium","thick","thin"])],["flex-flow",new Set(["nowrap","row","row-reverse","column","column-reverse","wrap","wrap-reverse"])],["height",new Set(["-webkit-fill-available"])],["inline-size",new Set(["-webkit-fill-available","min-content","max-content","fit-content"])],["list-style",new Set(["outside","inside","disc","circle","square","decimal","decimal-leading-zero","arabic-indic","bengali","cambodian","khmer","devanagari","gujarati","gurmukhi","kannada","lao","malayalam","mongolian","myanmar","oriya","persian","urdu","telugu","tibetan","thai","lower-roman","upper-roman","lower-greek","lower-alpha","lower-latin","upper-alpha","upper-latin","cjk-earthly-branch","cjk-heavenly-stem","ethiopic-halehame","ethiopic-halehame-am","ethiopic-halehame-ti-er","ethiopic-halehame-ti-et","hangul","hangul-consonant","korean-hangul-formal","korean-hanja-formal","korean-hanja-informal","hebrew","armenian","lower-armenian","upper-armenian","georgian","cjk-ideographic","simp-chinese-formal","simp-chinese-informal","trad-chinese-formal","trad-chinese-informal","hiragana","katakana","hiragana-iroha","katakana-iroha"])],["max-block-size",new Set(["-webkit-fill-available","min-content","max-content","fit-content"])],["max-inline-size",new Set(["-webkit-fill-available","min-content","max-content","fit-content"])],["min-block-size",new Set(["-webkit-fill-available","min-content","max-content","fit-content"])],["min-height",new Set(["-webkit-fill-available","min-content","max-content","fit-content"])],["min-inline-size",new Set(["-webkit-fill-available","min-content","max-content","fit-content"])],["min-width",new Set(["-webkit-fill-available","min-content","max-content","fit-content"])],["object-position",new Set(["top","bottom","left","right","center"])],["shape-outside",new Set(["border-box","content-box","padding-box","margin-box"])],["-webkit-appearance",new Set(["checkbox","radio","push-button","square-button","button","inner-spin-button","listbox","media-slider","media-sliderthumb","media-volume-slider","media-volume-sliderthumb","menulist","menulist-button","meter","progress-bar","slider-horizontal","slider-vertical","sliderthumb-horizontal","sliderthumb-vertical","searchfield","searchfield-cancel-button","textfield","textarea"])],["-webkit-border-after",new Set(["hidden","inset","groove","outset","ridge","dotted","dashed","solid","double","medium","thick","thin"])],["-webkit-border-after-style",new Set(["hidden","inset","groove","outset","ridge","dotted","dashed","solid","double"])],["-webkit-border-after-width",new Set(["medium","thick","thin"])],["-webkit-border-before",new Set(["hidden","inset","groove","outset","ridge","dotted","dashed","solid","double","medium","thick","thin"])],["-webkit-border-before-style",new Set(["hidden","inset","groove","outset","ridge","dotted","dashed","solid","double"])],["-webkit-border-before-width",new Set(["medium","thick","thin"])],["-webkit-border-end",new Set(["hidden","inset","groove","outset","ridge","dotted","dashed","solid","double","medium","thick","thin"])],["-webkit-border-end-style",new Set(["hidden","inset","groove","outset","ridge","dotted","dashed","solid","double"])],["-webkit-border-end-width",new Set(["medium","thick","thin"])],["-webkit-border-start",new Set(["hidden","inset","groove","outset","ridge","dotted","dashed","solid","double","medium","thick","thin"])],["-webkit-border-start-style",new Set(["hidden","inset","groove","outset","ridge","dotted","dashed","solid","double"])],["-webkit-border-start-width",new Set(["medium","thick","thin"])],["-webkit-logical-height",new Set(["-webkit-fill-available","min-content","max-content","fit-content"])],["-webkit-logical-width",new Set(["-webkit-fill-available","min-content","max-content","fit-content"])],["-webkit-mask-box-image",new Set(["repeat","stretch","space","round"])],["-webkit-mask-box-image-repeat",new Set(["repeat","stretch","space","round"])],["-webkit-mask-clip",new Set(["text","border","border-box","content","content-box","padding","padding-box"])],["-webkit-mask-composite",new Set(["clear","copy","source-over","source-in","source-out","source-atop","destination-over","destination-in","destination-out","destination-atop","xor","plus-lighter"])],["-webkit-mask-image",new Set(["linear-gradient","radial-gradient","repeating-linear-gradient","repeating-radial-gradient","url"])],["-webkit-mask-origin",new Set(["border","border-box","content","content-box","padding","padding-box"])],["-webkit-mask-position",new Set(["top","bottom","left","right","center"])],["-webkit-mask-position-x",new Set(["left","right","center"])],["-webkit-mask-position-y",new Set(["top","bottom","center"])],["-webkit-mask-repeat",new Set(["repeat","repeat-x","repeat-y","no-repeat","space","round"])],["-webkit-mask-size",new Set(["contain","cover"])],["-webkit-max-logical-height",new Set(["-webkit-fill-available","min-content","max-content","fit-content"])],["-webkit-max-logical-width",new Set(["-webkit-fill-available","min-content","max-content","fit-content"])],["-webkit-min-logical-height",new Set(["-webkit-fill-available","min-content","max-content","fit-content"])],["-webkit-min-logical-width",new Set(["-webkit-fill-available","min-content","max-content","fit-content"])],["-webkit-perspective-origin-x",new Set(["left","right","center"])],["-webkit-perspective-origin-y",new Set(["top","bottom","center"])],["-webkit-text-decorations-in-effect",new Set(["blink","line-through","overline","underline"])],["-webkit-text-stroke",new Set(["medium","thick","thin"])],["-webkit-text-stroke-width",new Set(["medium","thick","thin"])],["-webkit-transform-origin-x",new Set(["left","right","center"])],["-webkit-transform-origin-y",new Set(["top","bottom","center"])],["width",new Set(["-webkit-fill-available"])],["contain-intrinsic-width",new Set(["auto none","auto 100px"])],["contain-intrinsic-height",new Set(["auto none","auto 100px"])],["contain-intrinsic-size",new Set(["auto none","auto 100px"])],["contain-intrinsic-inline-size",new Set(["auto none","auto 100px"])],["contain-intrinsic-block-size",new Set(["auto none","auto 100px"])],["white-space",new Set(["normal","pre","pre-wrap","pre-line","nowrap","break-spaces"])]]),L=new Map([["align-content",57],["align-items",129],["align-self",55],["animation",175],["animation-delay",114],["animation-direction",113],["animation-duration",137],["animation-fill-mode",132],["animation-iteration-count",124],["animation-name",139],["animation-play-state",104],["animation-timing-function",141],["backface-visibility",123],["background",260],["background-attachment",119],["background-clip",165],["background-color",259],["background-image",246],["background-origin",107],["background-position",237],["background-position-x",108],["background-position-y",93],["background-repeat",234],["background-size",203],["border",263],["border-bottom",233],["border-bottom-color",190],["border-bottom-left-radius",186],["border-bottom-right-radius",185],["border-bottom-style",150],["border-bottom-width",179],["border-collapse",209],["border-color",226],["border-image",89],["border-image-outset",50],["border-image-repeat",49],["border-image-slice",58],["border-image-source",32],["border-image-width",52],["border-left",221],["border-left-color",174],["border-left-style",142],["border-left-width",172],["border-radius",224],["border-right",223],["border-right-color",182],["border-right-style",130],["border-right-width",178],["border-spacing",198],["border-style",206],["border-top",231],["border-top-color",192],["border-top-left-radius",187],["border-top-right-radius",189],["border-top-style",152],["border-top-width",180],["border-width",214],["bottom",227],["box-shadow",213],["box-sizing",216],["caption-side",96],["clear",229],["clip",173],["clip-rule",5],["color",256],["content",219],["counter-increment",111],["counter-reset",110],["cursor",250],["direction",176],["display",262],["empty-cells",99],["fill",140],["fill-opacity",82],["fill-rule",22],["filter",160],["flex",133],["flex-basis",66],["flex-direction",85],["flex-flow",94],["flex-grow",112],["flex-shrink",61],["flex-wrap",68],["float",252],["font",211],["font-family",254],["font-kerning",18],["font-size",264],["font-stretch",77],["font-style",220],["font-variant",161],["font-weight",257],["height",266],["image-rendering",90],["justify-content",127],["left",248],["letter-spacing",188],["line-height",244],["list-style",215],["list-style-image",145],["list-style-position",149],["list-style-type",199],["margin",267],["margin-bottom",241],["margin-left",243],["margin-right",238],["margin-top",253],["mask",20],["max-height",205],["max-width",225],["min-height",217],["min-width",218],["object-fit",33],["opacity",251],["order",117],["orphans",146],["outline",222],["outline-color",153],["outline-offset",147],["outline-style",151],["outline-width",148],["overflow",255],["overflow-wrap",105],["overflow-x",184],["overflow-y",196],["padding",265],["padding-bottom",230],["padding-left",235],["padding-right",232],["padding-top",240],["page",8],["page-break-after",120],["page-break-before",69],["page-break-inside",121],["perspective",92],["perspective-origin",103],["pointer-events",183],["position",261],["quotes",158],["resize",168],["right",245],["shape-rendering",38],["size",64],["speak",118],["src",170],["stop-color",42],["stop-opacity",31],["stroke",98],["stroke-dasharray",36],["stroke-dashoffset",3],["stroke-linecap",30],["stroke-linejoin",21],["stroke-miterlimit",12],["stroke-opacity",34],["stroke-width",87],["table-layout",171],["tab-size",46],["text-align",260],["text-anchor",35],["text-decoration",247],["text-indent",207],["text-overflow",204],["text-rendering",155],["text-shadow",208],["text-transform",202],["top",258],["touch-action",80],["transform",181],["transform-origin",162],["transform-style",86],["transition",193],["transition-delay",134],["transition-duration",135],["transition-property",131],["transition-timing-function",122],["unicode-bidi",156],["unicode-range",136],["vertical-align",236],["visibility",242],["-webkit-appearance",191],["-webkit-backface-visibility",154],["-webkit-background-clip",164],["-webkit-background-origin",40],["-webkit-background-size",163],["-webkit-border-end",9],["-webkit-border-horizontal-spacing",81],["-webkit-border-image",75],["-webkit-border-radius",212],["-webkit-border-start",10],["-webkit-border-start-color",16],["-webkit-border-start-width",13],["-webkit-border-vertical-spacing",43],["-webkit-box-align",101],["-webkit-box-direction",51],["-webkit-box-flex",128],["-webkit-box-ordinal-group",91],["-webkit-box-orient",144],["-webkit-box-pack",106],["-webkit-box-reflect",39],["-webkit-box-shadow",210],["-webkit-column-break-inside",60],["-webkit-column-count",84],["-webkit-column-gap",76],["-webkit-column-rule",25],["-webkit-column-rule-color",23],["-webkit-columns",44],["-webkit-column-span",29],["-webkit-column-width",47],["-webkit-filter",159],["-webkit-font-feature-settings",59],["-webkit-font-smoothing",177],["-webkit-line-break",45],["-webkit-line-clamp",126],["-webkit-margin-after",67],["-webkit-margin-before",70],["-webkit-margin-collapse",14],["-webkit-margin-end",65],["-webkit-margin-start",100],["-webkit-mask",19],["-webkit-mask-box-image",72],["-webkit-mask-image",88],["-webkit-mask-position",54],["-webkit-mask-repeat",63],["-webkit-mask-size",79],["-webkit-padding-after",15],["-webkit-padding-before",28],["-webkit-padding-end",48],["-webkit-padding-start",73],["-webkit-print-color-adjust",83],["-webkit-rtl-ordering",7],["-webkit-tap-highlight-color",169],["-webkit-text-emphasis-color",11],["-webkit-text-fill-color",71],["-webkit-text-security",17],["-webkit-text-stroke",56],["-webkit-text-stroke-color",37],["-webkit-text-stroke-width",53],["-webkit-user-drag",95],["-webkit-user-modify",62],["-webkit-user-select",194],["-webkit-writing-mode",4],["white-space",228],["widows",115],["width",268],["will-change",74],["word-break",166],["word-spacing",157],["word-wrap",197],["writing-mode",41],["z-index",239],["zoom",200]]),E=["auto","none"];var A=Object.freeze({__proto__:null,CSSMetadata:f,CSSWideKeywords:b,VariableNameRegex:/(\s*--.*?)/gs,VariableRegex:/(var\(\s*--.*?\))/gs,CustomVariableRegex:/(var\(*--[\w\d]+-([\w]+-[\w]+)\))/g,URLRegex:/url\(\s*('.+?'|".+?"|[^)]+)\s*\)/g,GridAreaRowRegex:y,cssMetadata:v});class O extends t.ProfileTreeModel.ProfileTreeModel{}var D=Object.freeze({__proto__:null,ProfileTreeModel:O});const N="<opaque>";class F{#h;#u;#g;#p;#m;#f;#b;#y;constructor(e,t,n,r,s){this.#h=e,this.#u=t,this.#g=n,this.#p=new Map,this.#m=0,this.#f=r||"Medium",this.#b=null,this.#y=s}static fromProtocolCookie(e){const t=new F(e.name,e.value,null,e.priority);return t.addAttribute("domain",e.domain),t.addAttribute("path",e.path),e.expires&&t.addAttribute("expires",1e3*e.expires),e.httpOnly&&t.addAttribute("http-only"),e.secure&&t.addAttribute("secure"),e.sameSite&&t.addAttribute("same-site",e.sameSite),"sourcePort"in e&&t.addAttribute("source-port",e.sourcePort),"sourceScheme"in e&&t.addAttribute("source-scheme",e.sourceScheme),"partitionKey"in e&&e.partitionKey&&t.setPartitionKey(e.partitionKey?e.partitionKey.topLevelSite:"",!!e.partitionKey&&e.partitionKey.hasCrossSiteAncestor),"partitionKeyOpaque"in e&&e.partitionKeyOpaque&&t.addAttribute("partition-key",N),t.setSize(e.size),t}key(){return(this.domain()||"-")+" "+this.name()+" "+(this.path()||"-")+" "+(this.partitionKey()?this.topLevelSite()+" "+(this.hasCrossSiteAncestor()?"cross_site":"same_site"):"-")}name(){return this.#h}value(){return this.#u}type(){return this.#g}httpOnly(){return this.#p.has("http-only")}secure(){return this.#p.has("secure")}partitioned(){return this.#p.has("partitioned")||Boolean(this.partitionKey())||this.partitionKeyOpaque()}sameSite(){return this.#p.get("same-site")}partitionKey(){return this.#y}setPartitionKey(e,t){this.#y={topLevelSite:e,hasCrossSiteAncestor:t},this.#p.has("partitioned")||this.addAttribute("partitioned")}topLevelSite(){return this.#y?this.#y?.topLevelSite:""}setTopLevelSite(e,t){this.setPartitionKey(e,t)}hasCrossSiteAncestor(){return!!this.#y&&this.#y?.hasCrossSiteAncestor}setHasCrossSiteAncestor(e){this.partitionKey()&&Boolean(this.topLevelSite())&&this.setPartitionKey(this.topLevelSite(),e)}partitionKeyOpaque(){return!!this.#y&&this.topLevelSite()===N}setPartitionKeyOpaque(){this.addAttribute("partition-key",N),this.setPartitionKey(N,!1)}priority(){return this.#f}session(){return!(this.#p.has("expires")||this.#p.has("max-age"))}path(){return this.#p.get("path")}domain(){return this.#p.get("domain")}expires(){return this.#p.get("expires")}maxAge(){return this.#p.get("max-age")}sourcePort(){return this.#p.get("source-port")}sourceScheme(){return this.#p.get("source-scheme")}size(){return this.#m}url(){if(!this.domain()||!this.path())return null;let e="";const t=this.sourcePort();return t&&80!==t&&443!==t&&(e=`:${this.sourcePort()}`),(this.secure()?"https://":"http://")+this.domain()+e+this.path()}setSize(e){this.#m=e}expiresDate(e){return this.maxAge()?new Date(e.getTime()+1e3*this.maxAge()):this.expires()?new Date(this.expires()):null}addAttribute(e,t){if(e)if("priority"===e)this.#f=t;else this.#p.set(e,t)}setCookieLine(e){this.#b=e}getCookieLine(){return this.#b}matchesSecurityOrigin(e){const t=new URL(e).hostname;return F.isDomainMatch(this.domain(),t)}static isDomainMatch(e,t){return t===e||!(!e||"."!==e[0])&&(e.substr(1)===t||t.length>e.length&&t.endsWith(e))}}var B,H=Object.freeze({__proto__:null,Cookie:F});class U extends l.InspectorBackend.TargetBase{#I;#h;#v;#k;#S;#g;#w;#C;#R;#x;#T;#M;constructor(t,n,r,i,o,a,l,d,c){switch(super(i===B.Node,o,a,d),this.#I=t,this.#h=r,this.#v=s.DevToolsPath.EmptyUrlString,this.#k="",this.#S=0,i){case B.Frame:this.#S=1027519,o?.type()!==B.Frame&&(this.#S|=21056,e.ParsedURL.schemeIs(c?.url,"chrome-extension:")&&(this.#S&=-513));break;case B.ServiceWorker:this.#S=657468,o?.type()!==B.Frame&&(this.#S|=1);break;case B.SharedWorker:this.#S=919612;break;case B.SharedStorageWorklet:this.#S=526348;break;case B.Worker:this.#S=917820;break;case B.Worklet:this.#S=524300;break;case B.Node:this.#S=4;break;case B.AuctionWorklet:this.#S=524292;break;case B.Browser:this.#S=131104;break;case B.Tab:this.#S=160}this.#g=i,this.#w=o,this.#C=n,this.#R=new Map,this.#x=l,this.#T=c}createModels(e){this.#M=!0;const t=Array.from(h.registeredModels.entries());for(const[e,n]of t)n.early&&this.model(e);for(const[n,r]of t)(r.autostart||e.has(n))&&this.model(n);this.#M=!1}id(){return this.#C}name(){return this.#h||this.#k}setName(e){this.#h!==e&&(this.#h=e,this.#I.onNameChange(this))}type(){return this.#g}markAsNodeJSForTest(){super.markAsNodeJSForTest(),this.#g=B.Node}targetManager(){return this.#I}hasAllCapabilities(e){return(this.#S&e)===e}decorateLabel(e){return this.#g===B.Worker||this.#g===B.ServiceWorker?"⚙ "+e:e}parentTarget(){return this.#w}outermostTarget(){let e=null,t=this;do{t.type()!==B.Tab&&t.type()!==B.Browser&&(e=t),t=t.parentTarget()}while(t);return e}dispose(e){super.dispose(e),this.#I.removeTarget(this);for(const e of this.#R.values())e.dispose()}model(e){if(!this.#R.get(e)){const t=h.registeredModels.get(e);if(void 0===t)throw"Model class is not registered @"+(new Error).stack;if((this.#S&t.capabilities)===t.capabilities){const t=new e(this);this.#R.set(e,t),this.#M||this.#I.modelAdded(this,e,t,this.#I.isInScope(this))}}return this.#R.get(e)||null}models(){return this.#R}inspectedURL(){return this.#v}setInspectedURL(t){this.#v=t;const n=e.ParsedURL.ParsedURL.fromString(t);this.#k=n?n.lastPathComponentWithFragment():"#"+this.#C,this.#I.onInspectedURLChange(this),this.#h||this.#I.onNameChange(this)}async suspend(e){this.#x||(this.#x=!0,await Promise.all(Array.from(this.models().values(),(t=>t.preSuspendModel(e)))),await Promise.all(Array.from(this.models().values(),(t=>t.suspendModel(e)))))}async resume(){this.#x&&(this.#x=!1,await Promise.all(Array.from(this.models().values(),(e=>e.resumeModel()))),await Promise.all(Array.from(this.models().values(),(e=>e.postResumeModel()))))}suspended(){return this.#x}updateTargetInfo(e){this.#T=e}targetInfo(){return this.#T}}!function(e){e.Frame="frame",e.ServiceWorker="service-worker",e.Worker="worker",e.SharedWorker="shared-worker",e.SharedStorageWorklet="shared-storage-worklet",e.Node="node",e.Browser="browser",e.AuctionWorklet="auction-worklet",e.Worklet="worklet",e.Tab="tab"}(B||(B={}));var _=Object.freeze({__proto__:null,Target:U,get Type(){return B}});let q;class z extends e.ObjectWrapper.ObjectWrapper{#P;#L;#E;#A;#O;#x;#D;#N;#F;#B;constructor(){super(),this.#P=new Set,this.#L=new Set,this.#E=new s.MapUtilities.Multimap,this.#A=new s.MapUtilities.Multimap,this.#x=!1,this.#D=null,this.#N=null,this.#O=new WeakSet,this.#F=!1,this.#B=new Set}static instance({forceNew:e}={forceNew:!1}){return q&&!e||(q=new z),q}static removeInstance(){q=void 0}onInspectedURLChange(e){e===this.#N&&(a.InspectorFrontendHost.InspectorFrontendHostInstance.inspectedURLChanged(e.inspectedURL()||s.DevToolsPath.EmptyUrlString),this.dispatchEventToListeners("InspectedURLChanged",e))}onNameChange(e){this.dispatchEventToListeners("NameChanged",e)}async suspendAllTargets(e){if(this.#x)return;this.#x=!0,this.dispatchEventToListeners("SuspendStateChanged");const t=Array.from(this.#P.values(),(t=>t.suspend(e)));await Promise.all(t)}async resumeAllTargets(){if(!this.#x)return;this.#x=!1,this.dispatchEventToListeners("SuspendStateChanged");const e=Array.from(this.#P.values(),(e=>e.resume()));await Promise.all(e)}allTargetsSuspended(){return this.#x}models(e,t){const n=[];for(const r of this.#P){if(t?.scoped&&!this.isInScope(r))continue;const s=r.model(e);s&&n.push(s)}return n}inspectedURL(){const e=this.primaryPageTarget();return e?e.inspectedURL():""}observeModels(e,t,n){const r=this.models(e,n);this.#A.set(e,t),n?.scoped&&this.#O.add(t);for(const e of r)t.modelAdded(e)}unobserveModels(e,t){this.#A.delete(e,t),this.#O.delete(t)}modelAdded(e,t,n,r){for(const e of this.#A.get(t).values())this.#O.has(e)&&!r||e.modelAdded(n)}modelRemoved(e,t,n,r){for(const e of this.#A.get(t).values())this.#O.has(e)&&!r||e.modelRemoved(n)}addModelListener(e,t,n,r,s){const i=e=>{s?.scoped&&!this.isInScope(e)||n.call(r,e)};for(const n of this.models(e))n.addEventListener(t,i);this.#E.set(t,{modelClass:e,thisObject:r,listener:n,wrappedListener:i})}removeModelListener(e,t,n,r){if(!this.#E.has(t))return;let s=null;for(const i of this.#E.get(t))i.modelClass===e&&i.listener===n&&i.thisObject===r&&(s=i.wrappedListener,this.#E.delete(t,i));if(s)for(const n of this.models(e))n.removeEventListener(t,s)}observeTargets(e,t){if(this.#L.has(e))throw new Error("Observer can only be registered once");t?.scoped&&this.#O.add(e);for(const n of this.#P)t?.scoped&&!this.isInScope(n)||e.targetAdded(n);this.#L.add(e)}unobserveTargets(e){this.#L.delete(e),this.#O.delete(e)}createTarget(e,t,n,r,s,i,o,a){const l=new U(this,e,t,n,r,s||"",this.#x,o||null,a);i&&l.pageAgent().invoke_waitForDebugger(),l.createModels(new Set(this.#A.keysArray())),this.#P.add(l);const d=this.isInScope(l);for(const e of[...this.#L])this.#O.has(e)&&!d||e.targetAdded(l);for(const[e,t]of l.models().entries())this.modelAdded(l,e,t,d);for(const e of this.#E.keysArray())for(const t of this.#E.get(e)){const n=l.model(t.modelClass);n&&n.addEventListener(e,t.wrappedListener)}return l!==l.outermostTarget()||l.type()===B.Frame&&l!==this.primaryPageTarget()||this.#F||this.setScopeTarget(l),l}removeTarget(e){if(!this.#P.has(e))return;const t=this.isInScope(e);this.#P.delete(e);for(const n of e.models().keys()){const r=e.models().get(n);i(r),this.modelRemoved(e,n,r,t)}for(const n of[...this.#L])this.#O.has(n)&&!t||n.targetRemoved(e);for(const t of this.#E.keysArray())for(const n of this.#E.get(t)){const r=e.model(n.modelClass);r&&r.removeEventListener(t,n.wrappedListener)}}targets(){return[...this.#P]}targetById(e){return this.targets().find((t=>t.id()===e))||null}rootTarget(){return this.#P.size?this.#P.values().next().value:null}primaryPageTarget(){let e=this.rootTarget();return e?.type()===B.Tab&&(e=this.targets().find((t=>t.parentTarget()===e&&t.type()===B.Frame&&!t.targetInfo()?.subtype?.length))||null),e}browserTarget(){return this.#D}async maybeAttachInitialTarget(){if(!Boolean(o.Runtime.Runtime.queryParam("browserConnection")))return!1;this.#D||(this.#D=new U(this,"main","browser",B.Browser,null,"",!1,null,void 0),this.#D.createModels(new Set(this.#A.keysArray())));const e=await a.InspectorFrontendHost.InspectorFrontendHostInstance.initialTargetId();return this.#D.targetAgent().invoke_autoAttachRelated({targetId:e,waitForDebuggerOnStart:!0}),!0}clearAllTargetsForTest(){this.#P.clear()}isInScope(e){if(!e)return!1;for(function(e){return"source"in e&&e.source instanceof h}(e)&&(e=e.source),e instanceof h&&(e=e.target());e&&e!==this.#N;)e=e.parentTarget();return Boolean(e)&&e===this.#N}setScopeTarget(e){if(e!==this.#N){for(const e of this.targets())if(this.isInScope(e)){for(const t of this.#A.keysArray()){const n=e.models().get(t);if(n)for(const e of[...this.#A.get(t)].filter((e=>this.#O.has(e))))e.modelRemoved(n)}for(const t of[...this.#L].filter((e=>this.#O.has(e))))t.targetRemoved(e)}this.#N=e;for(const e of this.targets())if(this.isInScope(e)){for(const t of[...this.#L].filter((e=>this.#O.has(e))))t.targetAdded(e);for(const[t,n]of e.models().entries())for(const e of[...this.#A.get(t)].filter((e=>this.#O.has(e))))e.modelAdded(n)}for(const e of this.#B)e();e&&e.inspectedURL()&&this.onInspectedURLChange(e)}}addScopeChangeListener(e){this.#B.add(e)}removeScopeChangeListener(e){this.#B.delete(e)}scopeTarget(){return this.#N}}var j=Object.freeze({__proto__:null,TargetManager:z,Observer:class{targetAdded(e){}targetRemoved(e){}},SDKModelObserver:class{modelAdded(e){}modelRemoved(e){}}});const V={noContentForWebSocket:"Content for WebSockets is currently not supported",noContentForRedirect:"No content available because this request was redirected",noContentForPreflight:"No content available for preflight request",noThrottling:"No throttling",offline:"Offline",slowG:"3G",fastG:"Slow 4G",fast4G:"Fast 4G",requestWasBlockedByDevtoolsS:'Request was blocked by DevTools: "{PH1}"',sFailedLoadingSS:'{PH1} failed loading: {PH2} "{PH3}".',sFinishedLoadingSS:'{PH1} finished loading: {PH2} "{PH3}".'},W=r.i18n.registerUIStrings("core/sdk/NetworkManager.ts",V),G=r.i18n.getLocalizedString.bind(void 0,W),K=r.i18n.getLazilyComputedLocalizedString.bind(void 0,W),Q=new WeakMap,$=new Map([["2g","cellular2g"],["3g","cellular3g"],["4g","cellular4g"],["bluetooth","bluetooth"],["wifi","wifi"],["wimax","wimax"]]);class X extends h{dispatcher;fetchDispatcher;#H;#U;constructor(t){super(t),this.dispatcher=new ie(this),this.fetchDispatcher=new se(t.fetchAgent(),this),this.#H=t.networkAgent(),t.registerNetworkDispatcher(this.dispatcher),t.registerFetchDispatcher(this.fetchDispatcher),e.Settings.Settings.instance().moduleSetting("cache-disabled").get()&&this.#H.invoke_setCacheDisabled({cacheDisabled:!0}),this.#H.invoke_enable({maxPostDataSize:re}),this.#H.invoke_setAttachDebugStack({enabled:!0}),this.#U=e.Settings.Settings.instance().createSetting("bypass-service-worker",!1),this.#U.get()&&this.bypassServiceWorkerChanged(),this.#U.addChangeListener(this.bypassServiceWorkerChanged,this),e.Settings.Settings.instance().moduleSetting("cache-disabled").addChangeListener(this.cacheDisabledSettingChanged,this)}static forRequest(e){return Q.get(e)||null}static canReplayRequest(t){return Boolean(Q.get(t))&&Boolean(t.backendRequestId())&&!t.isRedirect()&&t.resourceType()===e.ResourceType.resourceTypes.XHR}static replayRequest(e){const t=Q.get(e),n=e.backendRequestId();t&&n&&!e.isRedirect()&&t.#H.invoke_replayXHR({requestId:n})}static async searchInRequest(e,t,r,s){const i=X.forRequest(e),o=e.backendRequestId();if(!i||!o||e.isRedirect())return[];const a=await i.#H.invoke_searchInResponseBody({requestId:o,query:t,caseSensitive:r,isRegex:s});return n.TextUtils.performSearchInSearchMatches(a.result||[],t,r,s)}static async requestContentData(t){if(t.resourceType()===e.ResourceType.resourceTypes.WebSocket)return{error:G(V.noContentForWebSocket)};if(t.finished||await t.once(ps.FinishedLoading),t.isRedirect())return{error:G(V.noContentForRedirect)};if(t.isPreflightRequest())return{error:G(V.noContentForPreflight)};const r=X.forRequest(t);if(!r)return{error:"No network manager for request"};const s=t.backendRequestId();if(!s)return{error:"No backend request id for request"};const i=await r.#H.invoke_getResponseBody({requestId:s}),o=i.getError();return o?{error:o}:new n.ContentData.ContentData(i.body,i.base64Encoded,t.mimeType,t.charset()??void 0)}static async streamResponseBody(e){if(e.finished)return{error:"Streaming the response body is only available for in-flight requests."};const t=X.forRequest(e);if(!t)return{error:"No network manager for request"};const r=e.backendRequestId();if(!r)return{error:"No backend request id for request"};const s=await t.#H.invoke_streamResourceContent({requestId:r}),i=s.getError();return i?{error:i}:(await e.waitForResponseReceived(),new n.ContentData.ContentData(s.bufferedData,!0,e.mimeType,e.charset()??void 0))}static async requestPostData(e){const t=X.forRequest(e);if(!t)return console.error("No network manager for request"),null;const n=e.backendRequestId();if(!n)return console.error("No backend request id for request"),null;try{const{postData:e}=await t.#H.invoke_getRequestPostData({requestId:n});return e}catch(e){return e.message}}static connectionType(e){if(!e.download&&!e.upload)return"none";try{const t="function"==typeof e.title?e.title().toLowerCase():e.title.toLowerCase();for(const[e,n]of $)if(t.includes(e))return n}catch{return"none"}return"other"}static lowercaseHeaders(e){const t={};for(const n in e)t[n.toLowerCase()]=e[n];return t}requestForURL(e){return this.dispatcher.requestForURL(e)}requestForId(e){return this.dispatcher.requestForId(e)}requestForLoaderId(e){return this.dispatcher.requestForLoaderId(e)}cacheDisabledSettingChanged({data:e}){this.#H.invoke_setCacheDisabled({cacheDisabled:e})}dispose(){e.Settings.Settings.instance().moduleSetting("cache-disabled").removeChangeListener(this.cacheDisabledSettingChanged,this)}bypassServiceWorkerChanged(){this.#H.invoke_setBypassServiceWorker({bypass:this.#U.get()})}async getSecurityIsolationStatus(e){const t=await this.#H.invoke_getSecurityIsolationStatus({frameId:e??void 0});return t.getError()?null:t.status}async enableReportingApi(e=!0){return this.#H.invoke_enableReportingApi({enable:e})}async loadNetworkResource(e,t,n){const r=await this.#H.invoke_loadNetworkResource({frameId:e??void 0,url:t,options:n});if(r.getError())throw new Error(r.getError());return r.resource}clearRequests(){this.dispatcher.clearRequests()}}var J;!function(e){e.RequestStarted="RequestStarted",e.RequestUpdated="RequestUpdated",e.RequestFinished="RequestFinished",e.RequestUpdateDropped="RequestUpdateDropped",e.ResponseReceived="ResponseReceived",e.MessageGenerated="MessageGenerated",e.RequestRedirected="RequestRedirected",e.LoadingFinished="LoadingFinished",e.ReportingApiReportAdded="ReportingApiReportAdded",e.ReportingApiReportUpdated="ReportingApiReportUpdated",e.ReportingApiEndpointsChangedForOrigin="ReportingApiEndpointsChangedForOrigin"}(J||(J={}));const Y={title:K(V.noThrottling),i18nTitleKey:V.noThrottling,download:-1,upload:-1,latency:0},Z={title:K(V.offline),i18nTitleKey:V.offline,download:0,upload:0,latency:0},ee={title:K(V.slowG),i18nTitleKey:V.slowG,download:5e4,upload:5e4,latency:2e3,targetLatency:400},te={title:K(V.fastG),i18nTitleKey:V.fastG,download:18e4,upload:84375,latency:562.5,targetLatency:150},ne={title:K(V.fast4G),i18nTitleKey:V.fast4G,download:1012500,upload:168750,latency:165,targetLatency:60},re=65536;class se{#_;#q;constructor(e,t){this.#_=e,this.#q=t}requestPaused({requestId:e,request:t,resourceType:n,responseStatusCode:r,responseHeaders:s,networkId:i}){const o=i?this.#q.requestForId(i):null;0===o?.originalResponseHeaders.length&&s&&(o.originalResponseHeaders=s),ae.instance().requestIntercepted(new le(this.#_,t,n,e,o,r,s))}authRequired({}){}}class ie{#q;#z;#j;#V;#W;#G;constructor(e){this.#q=e,this.#z=new Map,this.#j=new Map,this.#V=new Map,this.#W=new Map,this.#G=new Map,ae.instance().addEventListener("RequestIntercepted",this.#K.bind(this))}#K(e){const t=this.requestForId(e.data);t&&t.setWasIntercepted(!0)}headersMapToHeadersArray(e){const t=[];for(const n in e){const r=e[n].split("\n");for(let e=0;e<r.length;++e)t.push({name:n,value:r[e]})}return t}updateNetworkRequestWithRequest(e,t){e.requestMethod=t.method,e.setRequestHeaders(this.headersMapToHeadersArray(t.headers)),e.setRequestFormData(Boolean(t.hasPostData),t.postData||null),e.setInitialPriority(t.initialPriority),e.mixedContentType=t.mixedContentType||"none",e.setReferrerPolicy(t.referrerPolicy),e.setIsSameSite(t.isSameSite||!1)}updateNetworkRequestWithResponse(t,n){n.url&&t.url()!==n.url&&t.setUrl(n.url),t.mimeType=n.mimeType,t.setCharset(n.charset),t.statusCode&&!t.wasIntercepted()||(t.statusCode=n.status),t.statusText&&!t.wasIntercepted()||(t.statusText=n.statusText),t.hasExtraResponseInfo()&&!t.wasIntercepted()||(t.responseHeaders=this.headersMapToHeadersArray(n.headers)),n.encodedDataLength>=0&&t.setTransferSize(n.encodedDataLength),n.requestHeaders&&!t.hasExtraRequestInfo()&&(t.setRequestHeaders(this.headersMapToHeadersArray(n.requestHeaders)),t.setRequestHeadersText(n.requestHeadersText||"")),t.connectionReused=n.connectionReused,t.connectionId=String(n.connectionId),n.remoteIPAddress&&t.setRemoteAddress(n.remoteIPAddress,n.remotePort||-1),n.fromServiceWorker&&(t.fetchedViaServiceWorker=!0),n.fromDiskCache&&t.setFromDiskCache(),n.fromPrefetchCache&&t.setFromPrefetchCache(),n.fromEarlyHints&&t.setFromEarlyHints(),n.cacheStorageCacheName&&t.setResponseCacheStorageCacheName(n.cacheStorageCacheName),n.serviceWorkerRouterInfo&&(t.serviceWorkerRouterInfo=n.serviceWorkerRouterInfo),n.responseTime&&t.setResponseRetrievalTime(new Date(n.responseTime)),t.timing=n.timing,t.protocol=n.protocol||"",t.alternateProtocolUsage=n.alternateProtocolUsage,n.serviceWorkerResponseSource&&t.setServiceWorkerResponseSource(n.serviceWorkerResponseSource),t.setSecurityState(n.securityState),n.securityDetails&&t.setSecurityDetails(n.securityDetails);const r=e.ResourceType.ResourceType.fromMimeTypeOverride(t.mimeType);r&&t.setResourceType(r),t.responseReceivedPromiseResolve?t.responseReceivedPromiseResolve():t.responseReceivedPromise=Promise.resolve()}requestForId(e){return this.#z.get(e)||null}requestForURL(e){return this.#j.get(e)||null}requestForLoaderId(e){return this.#V.get(e)||null}resourceChangedPriority({requestId:e,newPriority:t}){const n=this.#z.get(e);n&&n.setPriority(t)}signedExchangeReceived({requestId:t,info:n}){let r=this.#z.get(t);(r||(r=this.#j.get(n.outerResponse.url),r))&&(r.setSignedExchangeInfo(n),r.setResourceType(e.ResourceType.resourceTypes.SignedExchange),this.updateNetworkRequestWithResponse(r,n.outerResponse),this.updateNetworkRequest(r),this.#q.dispatchEventToListeners(J.ResponseReceived,{request:r,response:n.outerResponse}))}requestWillBeSent({requestId:t,loaderId:n,documentURL:r,request:s,timestamp:i,wallTime:o,initiator:a,redirectResponse:l,type:d,frameId:c,hasUserGesture:h}){let u=this.#z.get(t);if(u){if(!l)return;u.signedExchangeInfo()||this.responseReceived({requestId:t,loaderId:n,timestamp:i,type:d||"Other",response:l,hasExtraInfo:!1,frameId:c}),u=this.appendRedirect(t,i,s.url),this.#q.dispatchEventToListeners(J.RequestRedirected,u)}else u=gs.create(t,s.url,r,c??null,n,a,h),Q.set(u,this.#q);u.hasNetworkData=!0,this.updateNetworkRequestWithRequest(u,s),u.setIssueTime(i,o),u.setResourceType(d?e.ResourceType.resourceTypes[d]:e.ResourceType.resourceTypes.Other),s.trustTokenParams&&u.setTrustTokenParams(s.trustTokenParams);const g=this.#G.get(t);g&&(u.setTrustTokenOperationDoneEvent(g),this.#G.delete(t)),this.getExtraInfoBuilder(t).addRequest(u),this.startNetworkRequest(u,s)}requestServedFromCache({requestId:e}){const t=this.#z.get(e);t&&t.setFromMemoryCache()}responseReceived({requestId:t,loaderId:n,timestamp:r,type:s,response:i,frameId:o}){const a=this.#z.get(t),l=X.lowercaseHeaders(i.headers);if(a)a.responseReceivedTime=r,a.setResourceType(e.ResourceType.resourceTypes[s]),this.updateNetworkRequestWithResponse(a,i),this.updateNetworkRequest(a),this.#q.dispatchEventToListeners(J.ResponseReceived,{request:a,response:i});else{const e=l["last-modified"],t={url:i.url,frameId:o??null,loaderId:n,resourceType:s,mimeType:i.mimeType,lastModified:e?new Date(e):null};this.#q.dispatchEventToListeners(J.RequestUpdateDropped,t)}}dataReceived(e){let t=this.#z.get(e.requestId);t||(t=this.maybeAdoptMainResourceRequest(e.requestId)),t&&(t.addDataReceivedEvent(e),this.updateNetworkRequest(t))}loadingFinished({requestId:e,timestamp:t,encodedDataLength:n}){let r=this.#z.get(e);r||(r=this.maybeAdoptMainResourceRequest(e)),r&&(this.getExtraInfoBuilder(e).finished(),this.finishNetworkRequest(r,t,n),this.#q.dispatchEventToListeners(J.LoadingFinished,r))}loadingFailed({requestId:t,timestamp:n,type:r,errorText:s,canceled:i,blockedReason:o,corsErrorStatus:a}){const l=this.#z.get(t);if(l){if(l.failed=!0,l.setResourceType(e.ResourceType.resourceTypes[r]),l.canceled=Boolean(i),o&&(l.setBlockedReason(o),"inspector"===o)){const e=G(V.requestWasBlockedByDevtoolsS,{PH1:l.url()});this.#q.dispatchEventToListeners(J.MessageGenerated,{message:e,requestId:t,warning:!0})}a&&l.setCorsErrorStatus(a),l.localizedFailDescription=s,this.getExtraInfoBuilder(t).finished(),this.finishNetworkRequest(l,n,-1)}}webSocketCreated({requestId:t,url:n,initiator:r}){const s=gs.createForWebSocket(t,n,r);Q.set(s,this.#q),s.setResourceType(e.ResourceType.resourceTypes.WebSocket),this.startNetworkRequest(s,null)}webSocketWillSendHandshakeRequest({requestId:e,timestamp:t,wallTime:n,request:r}){const s=this.#z.get(e);s&&(s.requestMethod="GET",s.setRequestHeaders(this.headersMapToHeadersArray(r.headers)),s.setIssueTime(t,n),this.updateNetworkRequest(s))}webSocketHandshakeResponseReceived({requestId:e,timestamp:t,response:n}){const r=this.#z.get(e);r&&(r.statusCode=n.status,r.statusText=n.statusText,r.responseHeaders=this.headersMapToHeadersArray(n.headers),r.responseHeadersText=n.headersText||"",n.requestHeaders&&r.setRequestHeaders(this.headersMapToHeadersArray(n.requestHeaders)),n.requestHeadersText&&r.setRequestHeadersText(n.requestHeadersText),r.responseReceivedTime=t,r.protocol="websocket",this.updateNetworkRequest(r))}webSocketFrameReceived({requestId:e,timestamp:t,response:n}){const r=this.#z.get(e);r&&(r.addProtocolFrame(n,t,!1),r.responseReceivedTime=t,this.updateNetworkRequest(r))}webSocketFrameSent({requestId:e,timestamp:t,response:n}){const r=this.#z.get(e);r&&(r.addProtocolFrame(n,t,!0),r.responseReceivedTime=t,this.updateNetworkRequest(r))}webSocketFrameError({requestId:e,timestamp:t,errorMessage:n}){const r=this.#z.get(e);r&&(r.addProtocolFrameError(n,t),r.responseReceivedTime=t,this.updateNetworkRequest(r))}webSocketClosed({requestId:e,timestamp:t}){const n=this.#z.get(e);n&&this.finishNetworkRequest(n,t,-1)}eventSourceMessageReceived({requestId:e,timestamp:t,eventName:n,eventId:r,data:s}){const i=this.#z.get(e);i&&i.addEventSourceMessage(t,n,r,s)}requestIntercepted({}){}requestWillBeSentExtraInfo({requestId:e,associatedCookies:t,headers:n,clientSecurityState:r,connectTiming:s,siteHasCookieInOtherPartition:i}){const o=[],a=[];for(const{blockedReasons:e,exemptionReason:n,cookie:r}of t)0===e.length?a.push({exemptionReason:n,cookie:F.fromProtocolCookie(r)}):o.push({blockedReasons:e,cookie:F.fromProtocolCookie(r)});const l={blockedRequestCookies:o,includedRequestCookies:a,requestHeaders:this.headersMapToHeadersArray(n),clientSecurityState:r,connectTiming:s,siteHasCookieInOtherPartition:i};this.getExtraInfoBuilder(e).addRequestExtraInfo(l)}responseReceivedEarlyHints({requestId:e,headers:t}){this.getExtraInfoBuilder(e).setEarlyHintsHeaders(this.headersMapToHeadersArray(t))}responseReceivedExtraInfo({requestId:e,blockedCookies:t,headers:n,headersText:r,resourceIPAddressSpace:s,statusCode:i,cookiePartitionKey:o,cookiePartitionKeyOpaque:a,exemptedCookies:l}){const d={blockedResponseCookies:t.map((e=>({blockedReasons:e.blockedReasons,cookieLine:e.cookieLine,cookie:e.cookie?F.fromProtocolCookie(e.cookie):null}))),responseHeaders:this.headersMapToHeadersArray(n),responseHeadersText:r,resourceIPAddressSpace:s,statusCode:i,cookiePartitionKey:o,cookiePartitionKeyOpaque:a,exemptedResponseCookies:l?.map((e=>({cookie:F.fromProtocolCookie(e.cookie),cookieLine:e.cookieLine,exemptionReason:e.exemptionReason})))};this.getExtraInfoBuilder(e).addResponseExtraInfo(d)}getExtraInfoBuilder(e){let t;return this.#W.has(e)?t=this.#W.get(e):(t=new de,this.#W.set(e,t)),t}appendRedirect(e,t,n){const r=this.#z.get(e);if(!r)throw new Error(`Could not find original network request for ${e}`);let s=0;for(let e=r.redirectSource();e;e=e.redirectSource())s++;r.markAsRedirect(s),this.finishNetworkRequest(r,t,-1);const i=gs.create(e,n,r.documentURL,r.frameId,r.loaderId,r.initiator(),r.hasUserGesture()??void 0);return Q.set(i,this.#q),i.setRedirectSource(r),r.setRedirectDestination(i),i}maybeAdoptMainResourceRequest(e){const t=ae.instance().inflightMainResourceRequests.get(e);if(!t)return null;const n=X.forRequest(t).dispatcher;n.#z.delete(e),n.#j.delete(t.url());const r=t.loaderId;r&&n.#V.delete(r);const s=n.#W.get(e);return n.#W.delete(e),this.#z.set(e,t),this.#j.set(t.url(),t),r&&this.#V.set(r,t),s&&this.#W.set(e,s),Q.set(t,this.#q),t}startNetworkRequest(e,t){this.#z.set(e.requestId(),e),this.#j.set(e.url(),e);const n=e.loaderId;n&&this.#V.set(n,e),e.loaderId===e.requestId()&&ae.instance().inflightMainResourceRequests.set(e.requestId(),e),this.#q.dispatchEventToListeners(J.RequestStarted,{request:e,originalRequest:t})}updateNetworkRequest(e){this.#q.dispatchEventToListeners(J.RequestUpdated,e)}finishNetworkRequest(t,n,r){if(t.endTime=n,t.finished=!0,r>=0){const e=t.redirectSource();e&&e.signedExchangeInfo()?(t.setTransferSize(0),e.setTransferSize(r),this.updateNetworkRequest(e)):t.setTransferSize(r)}if(this.#q.dispatchEventToListeners(J.RequestFinished,t),ae.instance().inflightMainResourceRequests.delete(t.requestId()),e.Settings.Settings.instance().moduleSetting("monitoring-xhr-enabled").get()&&t.resourceType().category()===e.ResourceType.resourceCategories.XHR){let e;const n=t.failed||t.hasErrorStatusCode();e=G(n?V.sFailedLoadingSS:V.sFinishedLoadingSS,{PH1:t.resourceType().title(),PH2:t.requestMethod,PH3:t.url()}),this.#q.dispatchEventToListeners(J.MessageGenerated,{message:e,requestId:t.requestId(),warning:!1})}}clearRequests(){for(const[e,t]of this.#z)t.finished&&this.#z.delete(e);for(const[e,t]of this.#j)t.finished&&this.#j.delete(e);for(const[e,t]of this.#V)t.finished&&this.#V.delete(e);for(const[e,t]of this.#W)t.isFinished()&&this.#W.delete(e)}webTransportCreated({transportId:t,url:n,timestamp:r,initiator:s}){const i=gs.createForWebSocket(t,n,s);i.hasNetworkData=!0,Q.set(i,this.#q),i.setResourceType(e.ResourceType.resourceTypes.WebTransport),i.setIssueTime(r,0),this.startNetworkRequest(i,null)}webTransportConnectionEstablished({transportId:e,timestamp:t}){const n=this.#z.get(e);n&&(n.responseReceivedTime=t,n.endTime=t+.001,this.updateNetworkRequest(n))}webTransportClosed({transportId:e,timestamp:t}){const n=this.#z.get(e);n&&(n.endTime=t,this.finishNetworkRequest(n,t,0))}trustTokenOperationDone(e){const t=this.#z.get(e.requestId);t?t.setTrustTokenOperationDoneEvent(e):this.#G.set(e.requestId,e)}subresourceWebBundleMetadataReceived({requestId:e,urls:t}){const n=this.getExtraInfoBuilder(e);n.setWebBundleInfo({resourceUrls:t});const r=n.finalRequest();r&&this.updateNetworkRequest(r)}subresourceWebBundleMetadataError({requestId:e,errorMessage:t}){const n=this.getExtraInfoBuilder(e);n.setWebBundleInfo({errorMessage:t});const r=n.finalRequest();r&&this.updateNetworkRequest(r)}subresourceWebBundleInnerResponseParsed({innerRequestId:e,bundleRequestId:t}){const n=this.getExtraInfoBuilder(e);n.setWebBundleInnerRequestInfo({bundleRequestId:t});const r=n.finalRequest();r&&this.updateNetworkRequest(r)}subresourceWebBundleInnerResponseError({innerRequestId:e,errorMessage:t}){const n=this.getExtraInfoBuilder(e);n.setWebBundleInnerRequestInfo({errorMessage:t});const r=n.finalRequest();r&&this.updateNetworkRequest(r)}reportingApiReportAdded(e){this.#q.dispatchEventToListeners(J.ReportingApiReportAdded,e.report)}reportingApiReportUpdated(e){this.#q.dispatchEventToListeners(J.ReportingApiReportUpdated,e.report)}reportingApiEndpointsChangedForOrigin(e){this.#q.dispatchEventToListeners(J.ReportingApiEndpointsChangedForOrigin,e)}policyUpdated(){}createNetworkRequest(e,t,n,r,s,i){const o=gs.create(e,r,s,t,n,i);return Q.set(o,this.#q),o}}let oe;class ae extends e.ObjectWrapper.ObjectWrapper{#Q;#$;#X;#J;#Y;inflightMainResourceRequests;#Z;#ee;#te;#ne;#re;#se;#ie;#oe;constructor(){super(),this.#Q="",this.#$=null,this.#X=null,this.#J=new Set,this.#Y=new Set,this.inflightMainResourceRequests=new Map,this.#Z=Y,this.#ee=null,this.#te=e.Settings.Settings.instance().moduleSetting("request-blocking-enabled"),this.#ne=e.Settings.Settings.instance().createSetting("network-blocked-patterns",[]),this.#re=[],this.updateBlockedPatterns(),this.#se=new s.MapUtilities.Multimap,z.instance().observeModels(X,this)}static instance(e={forceNew:null}){const{forceNew:t}=e;return oe&&!t||(oe=new ae),oe}static dispose(){oe=null}static getChromeVersion(){const e=navigator.userAgent.match(/(?:^|\W)(?:Chrome|HeadlessChrome)\/(\S+)/);return e&&e.length>1?e[1]:""}static patchUserAgentWithChromeVersion(e){const t=ae.getChromeVersion();if(t.length>0){const n=t.split(".",1)[0]+".0.100.0";return s.StringUtilities.sprintf(e,t,n)}return e}static patchUserAgentMetadataWithChromeVersion(e){if(!e.brands)return;const t=ae.getChromeVersion();if(0===t.length)return;const n=t.split(".",1)[0];for(const t of e.brands)t.version.includes("%s")&&(t.version=s.StringUtilities.sprintf(t.version,n));e.fullVersion&&e.fullVersion.includes("%s")&&(e.fullVersion=s.StringUtilities.sprintf(e.fullVersion,t))}modelAdded(e){const t=e.target().networkAgent(),n=e.target().fetchAgent();this.#ie&&t.invoke_setExtraHTTPHeaders({headers:this.#ie}),this.currentUserAgent()&&t.invoke_setUserAgentOverride({userAgent:this.currentUserAgent(),userAgentMetadata:this.#$||void 0}),this.#re.length&&t.invoke_setBlockedURLs({urls:this.#re}),this.isIntercepting()&&n.invoke_enable({patterns:this.#se.valuesArray()}),null===this.#X?t.invoke_clearAcceptedEncodingsOverride():t.invoke_setAcceptedEncodings({encodings:this.#X}),this.#J.add(t),this.#Y.add(n),this.isThrottling()&&this.updateNetworkConditions(t)}modelRemoved(e){for(const t of this.inflightMainResourceRequests){X.forRequest(t[1])===e&&this.inflightMainResourceRequests.delete(t[0])}this.#J.delete(e.target().networkAgent()),this.#Y.delete(e.target().fetchAgent())}isThrottling(){return this.#Z.download>=0||this.#Z.upload>=0||this.#Z.latency>0}isOffline(){return!this.#Z.download&&!this.#Z.upload}setNetworkConditions(e){this.#Z=e;for(const e of this.#J)this.updateNetworkConditions(e);this.dispatchEventToListeners("ConditionsChanged")}networkConditions(){return this.#Z}updateNetworkConditions(e){const t=this.#Z;this.isThrottling()?e.invoke_emulateNetworkConditions({offline:this.isOffline(),latency:t.latency,downloadThroughput:t.download<0?0:t.download,uploadThroughput:t.upload<0?0:t.upload,packetLoss:(t.packetLoss??0)<0?0:t.packetLoss,packetQueueLength:t.packetQueueLength,packetReordering:t.packetReordering,connectionType:X.connectionType(t)}):e.invoke_emulateNetworkConditions({offline:!1,latency:0,downloadThroughput:0,uploadThroughput:0})}setExtraHTTPHeaders(e){this.#ie=e;for(const e of this.#J)e.invoke_setExtraHTTPHeaders({headers:this.#ie})}currentUserAgent(){return this.#oe?this.#oe:this.#Q}updateUserAgentOverride(){const e=this.currentUserAgent();for(const t of this.#J)t.invoke_setUserAgentOverride({userAgent:e,userAgentMetadata:this.#$||void 0})}setUserAgentOverride(e,t){const n=this.#Q!==e;this.#Q=e,this.#oe?this.#$=null:(this.#$=t,this.updateUserAgentOverride()),n&&this.dispatchEventToListeners("UserAgentChanged")}userAgentOverride(){return this.#Q}setCustomUserAgentOverride(e,t=null){this.#oe=e,this.#$=t,this.updateUserAgentOverride()}setCustomAcceptedEncodingsOverride(e){this.#X=e,this.updateAcceptedEncodingsOverride(),this.dispatchEventToListeners("AcceptedEncodingsChanged")}clearCustomAcceptedEncodingsOverride(){this.#X=null,this.updateAcceptedEncodingsOverride(),this.dispatchEventToListeners("AcceptedEncodingsChanged")}isAcceptedEncodingOverrideSet(){return null!==this.#X}updateAcceptedEncodingsOverride(){const e=this.#X;for(const t of this.#J)null===e?t.invoke_clearAcceptedEncodingsOverride():t.invoke_setAcceptedEncodings({encodings:e})}blockedPatterns(){return this.#ne.get().slice()}blockingEnabled(){return this.#te.get()}isBlocking(){return Boolean(this.#re.length)}setBlockedPatterns(e){this.#ne.set(e),this.updateBlockedPatterns(),this.dispatchEventToListeners("BlockedPatternsChanged")}setBlockingEnabled(e){this.#te.get()!==e&&(this.#te.set(e),this.updateBlockedPatterns(),this.dispatchEventToListeners("BlockedPatternsChanged"))}updateBlockedPatterns(){const e=[];if(this.#te.get())for(const t of this.#ne.get())t.enabled&&e.push(t.url);if(e.length||this.#re.length){this.#re=e;for(const e of this.#J)e.invoke_setBlockedURLs({urls:this.#re})}}isIntercepting(){return Boolean(this.#se.size)}setInterceptionHandlerForPatterns(e,t){this.#se.deleteAll(t);for(const n of e)this.#se.set(t,n);return this.updateInterceptionPatternsOnNextTick()}updateInterceptionPatternsOnNextTick(){return this.#ee||(this.#ee=Promise.resolve().then(this.updateInterceptionPatterns.bind(this))),this.#ee}async updateInterceptionPatterns(){e.Settings.Settings.instance().moduleSetting("cache-disabled").get()||e.Settings.Settings.instance().moduleSetting("cache-disabled").set(!0),this.#ee=null;const t=[];for(const e of this.#Y)t.push(e.invoke_enable({patterns:this.#se.valuesArray()}));this.dispatchEventToListeners("InterceptorsChanged"),await Promise.all(t)}async requestIntercepted(e){for(const t of this.#se.keysArray())if(await t(e),e.hasResponded()&&e.networkRequest)return void this.dispatchEventToListeners("RequestIntercepted",e.networkRequest.requestId());e.hasResponded()||e.continueRequestWithoutChange()}clearBrowserCache(){for(const e of this.#J)e.invoke_clearBrowserCache()}clearBrowserCookies(){for(const e of this.#J)e.invoke_clearBrowserCookies()}async getCertificate(e){const t=z.instance().primaryPageTarget();if(!t)return[];const n=await t.networkAgent().invoke_getCertificate({origin:e});return n?n.tableNames:[]}async loadResource(t){const n={},r=this.currentUserAgent();r&&(n["User-Agent"]=r),e.Settings.Settings.instance().moduleSetting("cache-disabled").get()&&(n["Cache-Control"]="no-cache");const s=e.Settings.Settings.instance().moduleSetting("network.enable-remote-file-loading").get();return new Promise((e=>a.ResourceLoader.load(t,n,((t,n,r,s)=>{e({success:t,content:r,errorDescription:s})}),s)))}}class le{#_;#ae;request;resourceType;responseStatusCode;responseHeaders;requestId;networkRequest;constructor(e,t,n,r,s,i,o){this.#_=e,this.#ae=!1,this.request=t,this.resourceType=n,this.responseStatusCode=i,this.responseHeaders=o,this.requestId=r,this.networkRequest=s}hasResponded(){return this.#ae}static mergeSetCookieHeaders(e,t){const n=e=>{const t=new Map;for(const n of e){const e=n.value.match(/^([a-zA-Z0-9!#$%&'*+.^_`|~-]+=)(.*)$/);e?t.has(e[1])?t.get(e[1])?.push(n.value):t.set(e[1],[n.value]):t.has(n.value)?t.get(n.value)?.push(n.value):t.set(n.value,[n.value])}return t},r=n(e),s=n(t),i=[];for(const[e,t]of r)if(s.has(e))for(const t of s.get(e)||[])i.push({name:"set-cookie",value:t});else for(const e of t)i.push({name:"set-cookie",value:e});for(const[e,t]of s)if(!r.has(e))for(const e of t)i.push({name:"set-cookie",value:e});return i}async continueRequestWithContent(t,n,r,s){this.#ae=!0;const i=n?await t.text():await e.Base64.encode(t).catch((e=>(console.error(e),""))),o=s?200:this.responseStatusCode||200;if(this.networkRequest){const e=this.networkRequest?.originalResponseHeaders.filter((e=>"set-cookie"===e.name))||[],t=r.filter((e=>"set-cookie"===e.name));this.networkRequest.setCookieHeaders=le.mergeSetCookieHeaders(e,t),this.networkRequest.hasOverriddenContent=s}this.#_.invoke_fulfillRequest({requestId:this.requestId,responseCode:o,body:i,responseHeaders:r}),ae.instance().dispatchEventToListeners("RequestFulfilled",this.request.url)}continueRequestWithoutChange(){console.assert(!this.#ae),this.#ae=!0,this.#_.invoke_continueRequest({requestId:this.requestId})}continueRequestWithError(e){console.assert(!this.#ae),this.#ae=!0,this.#_.invoke_failRequest({requestId:this.requestId,errorReason:e})}async responseBody(){const e=await this.#_.invoke_getResponseBody({requestId:this.requestId}),t=e.getError();if(t)return{error:t};const{mimeType:r,charset:s}=this.getMimeTypeAndCharset();return new n.ContentData.ContentData(e.body,e.base64Encoded,r??"application/octet-stream",s??void 0)}isRedirect(){return void 0!==this.responseStatusCode&&this.responseStatusCode>=300&&this.responseStatusCode<400}getMimeTypeAndCharset(){for(const e of this.responseHeaders??[])if("content-type"===e.name.toLowerCase())return s.MimeType.parseContentType(e.value);return{mimeType:this.networkRequest?.mimeType??null,charset:this.networkRequest?.charset()??null}}}class de{#le;#de;#ce;#he;#ue;#ge;#pe;constructor(){this.#le=[],this.#de=[],this.#he=[],this.#ce=[],this.#ue=!1,this.#ge=null,this.#pe=null}addRequest(e){this.#le.push(e),this.sync(this.#le.length-1)}addRequestExtraInfo(e){this.#de.push(e),this.sync(this.#de.length-1)}addResponseExtraInfo(e){this.#ce.push(e),this.sync(this.#ce.length-1)}setEarlyHintsHeaders(e){this.#he=e,this.updateFinalRequest()}setWebBundleInfo(e){this.#ge=e,this.updateFinalRequest()}setWebBundleInnerRequestInfo(e){this.#pe=e,this.updateFinalRequest()}finished(){this.#ue=!0,this.updateFinalRequest()}isFinished(){return this.#ue}sync(e){const t=this.#le[e];if(!t)return;const n=this.#de[e];n&&(t.addExtraRequestInfo(n),this.#de[e]=null);const r=this.#ce[e];r&&(t.addExtraResponseInfo(r),this.#ce[e]=null)}finalRequest(){return this.#ue&&this.#le[this.#le.length-1]||null}updateFinalRequest(){if(!this.#ue)return;const e=this.finalRequest();e?.setWebBundleInfo(this.#ge),e?.setWebBundleInnerRequestInfo(this.#pe),e?.setEarlyHintsHeaders(this.#he)}}h.register(X,{capabilities:16,autostart:!0});var ce=Object.freeze({__proto__:null,NetworkManager:X,get Events(){return J},NoThrottlingConditions:Y,OfflineConditions:Z,Slow3GConditions:ee,Slow4GConditions:te,Fast4GConditions:ne,FetchDispatcher:se,NetworkDispatcher:ie,MultitargetNetworkManager:ae,InterceptedRequest:le,ConditionsSerializer:class{stringify(e){const t=e;return JSON.stringify({...t,title:"function"==typeof t.title?t.title():t.title})}parse(e){const t=JSON.parse(e);return{...t,title:t.i18nTitleKey?K(t.i18nTitleKey):t.title}}},networkConditionsEqual:function(e,t){const n=e.i18nTitleKey||("function"==typeof e.title?e.title():e.title),r=t.i18nTitleKey||("function"==typeof t.title?t.title():t.title);return t.download===e.download&&t.upload===e.upload&&t.latency===e.latency&&e.packetLoss===t.packetLoss&&e.packetQueueLength===t.packetQueueLength&&e.packetReordering===t.packetReordering&&r===n}});class he{#me;#fe;#be;#ye;#Ie;constructor(e){this.#me=e.fontFamily,this.#fe=e.fontVariationAxes||[],this.#be=new Map,this.#ye=e.src,this.#Ie=e.fontDisplay;for(const e of this.#fe)this.#be.set(e.tag,e)}getFontFamily(){return this.#me}getSrc(){return this.#ye}getFontDisplay(){return this.#Ie}getVariationAxisByTag(e){return this.#be.get(e)}}var ue=Object.freeze({__proto__:null,CSSFontFace:he});const ge=new Set(["inherit","initial","unset"]),pe=/[\x20-\x7E]{4}/,me=new RegExp(`(?:'(${pe.source})')|(?:"(${pe.source})")\\s+(${/[+-]?(?:\d*\.)?\d+(?:[eE]\d+)?/.source})`);const fe=/^"(.+)"|'(.+)'$/;function be(e){return e.split(",").map((e=>e.trim()))}function ye(e){return e.replaceAll(/(\/\*(?:.|\s)*?\*\/)/g,"")}const Ie=d.css.cssLanguage.parser;function ve(e,t){return ke(e,e,t)}function ke(e,t,n){return n.substring(e.from,t.to)}class Se{propertyValue;rule;tree;trailingNodes;propertyName;constructor(e,t,n,r,s=[]){this.propertyName=r,this.propertyValue=e,this.rule=t,this.tree=n,this.trailingNodes=s}text(e){return null===e?"":ve(e??this.tree,this.rule)}textRange(e,t){return ke(e,t,this.rule)}subtree(e){return new Se(this.propertyValue,this.rule,e)}}class we{ast;constructor(e){this.ast=e}static walkExcludingSuccessors(e,...t){const n=new this(e,...t);return"Declaration"===e.tree.name?n.iterateDeclaration(e.tree):n.iterateExcludingSuccessors(e.tree),n}static walk(e,...t){const n=new this(e,...t);return"Declaration"===e.tree.name?n.iterateDeclaration(e.tree):n.iterate(e.tree),n}iterateDeclaration(e){"Declaration"===e.name&&(this.enter(e)&&Le.declValue(e)?.cursor().iterate(this.enter.bind(this),this.leave.bind(this)),this.leave(e))}iterate(e){e.cursor().iterate(this.enter.bind(this),this.leave.bind(this))}iterateExcludingSuccessors(e){this.enter(e)&&e.firstChild?.cursor().iterate(this.enter.bind(this),this.leave.bind(this)),this.leave(e)}enter(e){return!0}leave(e){}}function Ce(e){return class{matchType=e;accepts(e){return!0}matches(e,t){return null}}}class Re extends we{#ve=[];#ke=new Map;computedText;#Se(e){return`${e.from}:${e.to}`}constructor(e,t){super(e),this.computedText=new Te(e.rule.substring(e.tree.from)),this.#ve.push(...t.filter((t=>!e.propertyName||t.accepts(e.propertyName)))),this.#ve.push(new De)}leave({node:e}){for(const t of this.#ve){const n=t.matches(e,this);if(n){this.computedText.push(n,e.from-this.ast.tree.from),this.#ke.set(this.#Se(e),n);break}}}matchText(e){const t=this.#ve.splice(0);this.#ve.push(new De),this.iterateExcludingSuccessors(e),this.#ve.push(...t)}getMatch(e){return this.#ke.get(this.#Se(e))}hasUnresolvedVars(e){return this.hasUnresolvedVarsRange(e,e)}hasUnresolvedVarsRange(e,t){return this.computedText.hasUnresolvedVars(e.from-this.ast.tree.from,t.to-this.ast.tree.from)}getComputedText(e,t){return this.getComputedTextRange(e,e,t)}getComputedTextRange(e,t,n){return this.computedText.get(e.from-this.ast.tree.from,t.to-this.ast.tree.from,n)}}class xe{match;offset;#we=null;constructor(e,t){this.match=e,this.offset=t}get end(){return this.offset+this.length}get length(){return this.match.text.length}get computedText(){return null===this.#we&&(this.#we=this.match.computedText()),this.#we}}class Te{#Ce=[];text;#Re=!0;constructor(e){this.text=e}clear(){this.#Ce.splice(0)}get chunkCount(){return this.#Ce.length}#xe(){this.#Re||(this.#Ce.sort(((e,t)=>e.offset<t.offset?-1:t.offset<e.offset?1:e.end>t.end?-1:e.end<t.end?1:0)),this.#Re=!0)}push(e,t){if(!function(e){return Boolean(e.computedText)}(e)||t<0||t>=this.text.length)return;const n=new xe(e,t);n.end>this.text.length||(this.#Re=!1,this.#Ce.push(n))}*#Te(e,t){this.#xe();let n=this.#Ce.findIndex((t=>t.offset>=e));for(;n>=0&&n<this.#Ce.length&&this.#Ce[n].end>e&&e<t;)if(this.#Ce[n].end>t)n++;else for(yield this.#Ce[n],e=this.#Ce[n].end;e<t&&n<this.#Ce.length&&this.#Ce[n].offset<e;)n++}hasUnresolvedVars(e,t){for(const n of this.#Te(e,t))if(null===n.computedText)return!0;return!1}*#Me(e,t){for(const n of this.#Te(e,t)){const r=this.text.substring(e,Math.min(n.offset,t));yield r,t>=n.end&&(yield n),e=n.end}if(e<t){const n=this.text.substring(e,t);yield n}}get(e,t,n){const r=[],s=e=>{if("string"==typeof e)return e;const t=n?.get(e.match);return t?s(t):e.computedText??e.match.text};for(const n of this.#Me(e,t)){const e=s(n);0!==e.length&&(r.length>0&&Me(r[r.length-1],e)&&r.push(" "),r.push(e))}return r.join("")}}function Me(e,t){const n=Array.isArray(e)?e.findLast((e=>e.textContent))?.textContent:e,r=Array.isArray(t)?t.find((e=>e.textContent))?.textContent:t,s=n?n[n.length-1]:"",i=r?r[0]:"";return!(/\s/.test(s)||/\s/.test(i)||["","(","{","}",";","["].includes(s)||["","(",")",",",":","*","{",";","]"].includes(i))}const Pe=Map;var Le;!function(e){function t(e){const t=[];for(;e;)t.push(e),e=e.nextSibling;return t}function n(e){return t(e?.firstChild??null)}function r(e){const t=[];let n=[];for(const r of e)","===r.name?(t.push(n),n=[]):n.push(r);return t.push(n),t}e.siblings=t,e.children=n,e.declValue=function(e){return"Declaration"!==e.name?null:n(e).find((e=>":"===e.name))?.nextSibling??null},e.stripComments=function*(e){for(const t of e)"Comment"!==t.type.name&&(yield t)},e.split=r,e.callArgs=function(e){const t=n(e.getChild("ArgList")),s=t.splice(0,1)[0],i=t.pop();return"("!==s?.name||")"!==i?.name?[]:r(t)}}(Le||(Le={}));class Ee{text;node;name;fallback;matching;computedTextCallback;constructor(e,t,n,r,s,i){this.text=e,this.node=t,this.name=n,this.fallback=r,this.matching=s,this.computedTextCallback=i}computedText(){return this.computedTextCallback(this,this.matching)}}class Ae extends(Ce(Ee)){#Pe;constructor(e){super(),this.#Pe=e}matches(e,t){const n=e.getChild("Callee"),r=e.getChild("ArgList");if("CallExpression"!==e.name||!n||"var"!==t.ast.text(n)||!r)return null;const[s,i,...o]=Le.children(r);if("("!==s?.name||"VariableName"!==i?.name)return null;if(o.length<=1&&")"!==o[0]?.name)return null;let a=[];if(o.length>1){if(","!==o.shift()?.name)return null;if(")"!==o.pop()?.name)return null;if(a=o,0===a.length)return null;if(a.some((e=>","===e.name)))return null}const l=t.ast.text(i);return l.startsWith("--")?new Ee(t.ast.text(e),e,l,a,t,this.#Pe):null}}class Oe{text;node;computedText;constructor(e,t){this.text=e,this.node=t,"Comment"===t.name&&(this.computedText=()=>"")}render(){return[document.createTextNode(this.text)]}}class De extends(Ce(Oe)){accepts(){return!0}matches(e,t){if(!e.firstChild||"NumberLiteral"===e.name){const n=t.ast.text(e);if(n.length)return new Oe(n,e)}return null}}function Ne(e){return Ie.parse(e).topNode.getChild("RuleSet")?.getChild("Block")?.getChild("Declaration")??null}function Fe(e,t){const n=Be(e);if(!n)return null;const r=`*{${n}: ${t};}`,s=Ne(r);if(!s||s.type.isError)return null;const i=Le.children(s);if(i.length<3)return null;const[o,a,l]=i;if(!o||o.type.isError||!a||a.type.isError||!l||l.type.isError)return null;const d=Le.siblings(s).slice(1),[c,h]=d.splice(d.length-2,2);if(";"!==c?.name&&"}"!==h?.name)return null;const u=new Se(t,r,s,n,d);return u.text(o)!==n||":"!==a.name?null:u}function Be(e){const t=`*{${e}: inherit;}`,n=Ne(t);if(!n||n.type.isError)return null;const r=n.getChild("PropertyName")??n.getChild("VariableName");return r?ve(r,t):null}var He=Object.freeze({__proto__:null,parseFontVariationSettings:function(e){if(ge.has(e.trim())||"normal"===e.trim())return[];const t=[];for(const n of be(ye(e))){const e=n.match(me);e&&t.push({tag:e[1]||e[2],value:parseFloat(e[3])})}return t},parseFontFamily:function(e){if(ge.has(e.trim()))return[];const t=[];for(const n of be(ye(e))){const e=n.match(fe);e?t.push(e[1]||e[2]):t.push(n)}return t},splitByComma:be,stripComments:ye,SyntaxTree:Se,TreeWalker:we,matcherBase:Ce,BottomUpTreeMatching:Re,ComputedText:Te,requiresSpace:Me,CSSControlMap:Pe,get ASTUtils(){return Le},VariableMatch:Ee,VariableMatcher:Ae,TextMatch:Oe,tokenizeDeclaration:Fe,tokenizePropertyName:Be});class Ue{text="";range;styleSheetId;cssModel;constructor(e){this.cssModel=e}rebase(e){this.styleSheetId===e.styleSheetId&&this.range&&(e.oldRange.equal(this.range)?this.reinitialize(e.payload):this.range=this.range.rebaseAfterTextEdit(e.oldRange,e.newRange))}equal(e){return!!(this.styleSheetId&&this.range&&e.range)&&(this.styleSheetId===e.styleSheetId&&this.range.equal(e.range))}lineNumberInSource(){if(this.range)return this.header()?.lineNumberInSource(this.range.startLine)}columnNumberInSource(){if(this.range)return this.header()?.columnNumberInSource(this.range.startLine,this.range.startColumn)}header(){return this.styleSheetId?this.cssModel.styleSheetHeaderForId(this.styleSheetId):null}rawLocation(){const e=this.header();if(!e||void 0===this.lineNumberInSource())return null;const t=Number(this.lineNumberInSource());return new Pn(e,t,this.columnNumberInSource())}}var _e=Object.freeze({__proto__:null,CSSQuery:Ue});class qe extends Ue{name;physicalAxes;logicalAxes;static parseContainerQueriesPayload(e,t){return t.map((t=>new qe(e,t)))}constructor(e,t){super(e),this.reinitialize(t)}reinitialize(e){this.text=e.text,this.range=e.range?n.TextRange.TextRange.fromObject(e.range):null,this.styleSheetId=e.styleSheetId,this.name=e.name,this.physicalAxes=e.physicalAxes,this.logicalAxes=e.logicalAxes}active(){return!0}async getContainerForNode(e){const t=await this.cssModel.domModel().getContainerForNode(e,this.name,this.physicalAxes,this.logicalAxes);if(t)return new ze(t)}}class ze{containerNode;constructor(e){this.containerNode=e}async getContainerSizeDetails(){const e=await this.containerNode.domModel().cssModel().getComputedStyle(this.containerNode.id);if(!e)return;const t=e.get("container-type"),n=e.get("contain"),r=e.get("writing-mode");if(!t||!n||!r)return;const s=je(`${t} ${n}`),i=Ve(s,r);let o,a;return"Both"!==i&&"Horizontal"!==i||(o=e.get("width")),"Both"!==i&&"Vertical"!==i||(a=e.get("height")),{queryAxis:s,physicalAxis:i,width:o,height:a}}}const je=e=>{const t=e.split(" ");let n=!1,r=!1;for(const e of t){if("size"===e)return"size";n=n||"inline-size"===e,r=r||"block-size"===e}return n&&r?"size":n?"inline-size":r?"block-size":""},Ve=(e,t)=>{const n=t.startsWith("vertical");switch(e){case"":return"";case"size":return"Both";case"inline-size":return n?"Vertical":"Horizontal";case"block-size":return n?"Horizontal":"Vertical"}};var We=Object.freeze({__proto__:null,CSSContainerQuery:qe,CSSContainerQueryContainer:ze,getQueryAxis:je,getPhysicalAxisFromQueryAxis:Ve});class Ge extends Ue{static parseLayerPayload(e,t){return t.map((t=>new Ge(e,t)))}constructor(e,t){super(e),this.reinitialize(t)}reinitialize(e){this.text=e.text,this.range=e.range?n.TextRange.TextRange.fromObject(e.range):null,this.styleSheetId=e.styleSheetId}active(){return!0}}var Ke=Object.freeze({__proto__:null,CSSLayer:Ge});class Qe{#Le;#Ee;constructor(e){this.#Le=e.active,this.#Ee=[];for(let t=0;t<e.expressions.length;++t)this.#Ee.push($e.parsePayload(e.expressions[t]))}static parsePayload(e){return new Qe(e)}active(){return this.#Le}expressions(){return this.#Ee}}class $e{#u;#Ae;#Oe;#De;#Ne;constructor(e){this.#u=e.value,this.#Ae=e.unit,this.#Oe=e.feature,this.#De=e.valueRange?n.TextRange.TextRange.fromObject(e.valueRange):null,this.#Ne=e.computedLength||null}static parsePayload(e){return new $e(e)}value(){return this.#u}unit(){return this.#Ae}feature(){return this.#Oe}valueRange(){return this.#De}computedLength(){return this.#Ne}}class Xe extends Ue{source;sourceURL;mediaList;static parseMediaArrayPayload(e,t){return t.map((t=>new Xe(e,t)))}constructor(e,t){super(e),this.reinitialize(t)}reinitialize(e){if(this.text=e.text,this.source=e.source,this.sourceURL=e.sourceURL||"",this.range=e.range?n.TextRange.TextRange.fromObject(e.range):null,this.styleSheetId=e.styleSheetId,this.mediaList=null,e.mediaList){this.mediaList=[];for(let t=0;t<e.mediaList.length;++t)this.mediaList.push(Qe.parsePayload(e.mediaList[t]))}}active(){if(!this.mediaList)return!0;for(let e=0;e<this.mediaList.length;++e)if(this.mediaList[e].active())return!0;return!1}}var Je=Object.freeze({__proto__:null,CSSMediaQuery:Qe,CSSMediaQueryExpression:$e,CSSMedia:Xe,Source:{LINKED_SHEET:"linkedSheet",INLINE_SHEET:"inlineSheet",MEDIA_RULE:"mediaRule",IMPORT_RULE:"importRule"}});class Ye extends Ue{static parseScopesPayload(e,t){return t.map((t=>new Ye(e,t)))}constructor(e,t){super(e),this.reinitialize(t)}reinitialize(e){this.text=e.text,this.range=e.range?n.TextRange.TextRange.fromObject(e.range):null,this.styleSheetId=e.styleSheetId}active(){return!0}}var Ze=Object.freeze({__proto__:null,CSSScope:Ye});class et{ownerStyle;index;name;value;important;disabled;parsedOk;implicit;text;range;#Fe;#Be;#De;#He;#Ue=[];constructor(e,t,r,s,i,o,a,l,d,c,h){if(this.ownerStyle=e,this.index=t,this.name=r,this.value=s,this.important=i,this.disabled=o,this.parsedOk=a,this.implicit=l,this.text=d,this.range=c?n.TextRange.TextRange.fromObject(c):null,this.#Fe=!0,this.#Be=null,this.#De=null,h&&h.length>0)for(const n of h)this.#Ue.push(new et(e,++t,n.name,n.value,i,o,a,!0));else{const n=v().getLonghands(r);for(const r of n||[])this.#Ue.push(new et(e,++t,r,"",i,o,a,!0))}}static parsePayload(e,t,n){return new et(e,t,n.name,n.value,n.important||!1,n.disabled||!1,!("parsedOk"in n)||Boolean(n.parsedOk),Boolean(n.implicit),n.text,n.range,n.longhandProperties)}ensureRanges(){if(this.#Be&&this.#De)return;const e=this.range,t=this.text?new n.Text.Text(this.text):null;if(!e||!t)return;const r=t.value().indexOf(this.name),s=t.value().lastIndexOf(this.value);if(-1===r||-1===s||r>s)return;const i=new n.TextRange.SourceRange(r,this.name.length),o=new n.TextRange.SourceRange(s,this.value.length);function a(e,t,n){return 0===e.startLine&&(e.startColumn+=n,e.endColumn+=n),e.startLine+=t,e.endLine+=t,e}this.#Be=a(t.toTextRange(i),e.startLine,e.startColumn),this.#De=a(t.toTextRange(o),e.startLine,e.startColumn)}nameRange(){return this.ensureRanges(),this.#Be}valueRange(){return this.ensureRanges(),this.#De}rebase(e){this.ownerStyle.styleSheetId===e.styleSheetId&&this.range&&(this.range=this.range.rebaseAfterTextEdit(e.oldRange,e.newRange))}setActive(e){this.#Fe=e}get propertyText(){return void 0!==this.text?this.text:""===this.name?"":this.name+": "+this.value+(this.important?" !important":"")+";"}activeInStyle(){return this.#Fe}trimmedValueWithoutImportant(){const e="!important";return this.value.endsWith(e)?this.value.slice(0,-10).trim():this.value.trim()}async setText(t,r,i){if(!this.ownerStyle)throw new Error("No ownerStyle for property");if(!this.ownerStyle.styleSheetId)throw new Error("No owner style id");if(!this.range||!this.ownerStyle.range)throw new Error("Style not editable");if(r&&(a.userMetrics.actionTaken(a.UserMetrics.Action.StyleRuleEdited),this.ownerStyle.parentRule?.isKeyframeRule()&&a.userMetrics.actionTaken(a.UserMetrics.Action.StylePropertyInsideKeyframeEdited),this.name.startsWith("--")&&a.userMetrics.actionTaken(a.UserMetrics.Action.CustomPropertyEdited)),i&&t===this.propertyText)return this.ownerStyle.cssModel().domModel().markUndoableState(!r),!0;const o=this.range.relativeTo(this.ownerStyle.range.startLine,this.ownerStyle.range.startColumn),l=this.ownerStyle.cssText?this.detectIndentation(this.ownerStyle.cssText):e.Settings.Settings.instance().moduleSetting("text-editor-indent").get(),d=this.ownerStyle.cssText?l.substring(0,this.ownerStyle.range.endColumn):"",c=new n.Text.Text(this.ownerStyle.cssText||"").replaceRange(o,s.StringUtilities.sprintf(";%s;",t)),h=await et.formatStyle(c,l,d);return this.ownerStyle.setText(h,r)}static async formatStyle(e,t,r){const s=t.substring(r.length)+t;t&&(t="\n"+t);let i="",o="",a="",l=!1,d=!1;const c=n.CodeMirrorUtils.createCssTokenizer();return await c("*{"+e+"}",(function(e,n){if(!l){const r=n?.includes("comment")&&function(e){const t=e.indexOf(":");if(-1===t)return!1;const n=e.substring(2,t).trim();return v().isCSSPropertyName(n)}(e),s=n?.includes("def")||n?.includes("string")||n?.includes("meta")||n?.includes("property")||n?.includes("variableName")&&"variableName.function"!==n;return r?i=i.trimEnd()+t+e:s?(l=!0,a=e):(";"!==e||d)&&(i+=e,e.trim()&&!n?.includes("comment")&&(d=";"!==e)),void("{"!==e||n||(d=!1))}if("}"===e||";"===e){const n=a.trim();return i=i.trimEnd()+t+n+(n.endsWith(":")?" ":"")+e,d=!1,l=!1,void(o="")}if(v().isGridAreaDefiningProperty(o)){const t=y.exec(e);t&&0===t.index&&!a.trimEnd().endsWith("]")&&(a=a.trimEnd()+"\n"+s)}o||":"!==e||(o=a);a+=e})),l&&(i+=a),i=i.substring(2,i.length-1).trimEnd(),i+(t?"\n"+r:"")}detectIndentation(e){const t=e.split("\n");return t.length<2?"":n.TextUtils.Utils.lineIndent(t[1])}setValue(e,t,n,r){const s=this.name+": "+e+(this.important?" !important":"")+";";this.setText(s,t,n).then(r)}async setDisabled(e){if(!this.ownerStyle)return!1;if(e===this.disabled)return!0;if(!this.text)return!0;const t=this.text.trim(),n=e=>e+(e.endsWith(";")?"":";");let r;return r=e?"/* "+n(ye(t))+" */":n(this.text.substring(2,t.length-2).trim()),this.setText(r,!0,!0)}setDisplayedStringForInvalidProperty(e){this.#He=e}getInvalidStringForInvalidProperty(){return this.#He}getLonghandProperties(){return this.#Ue}}var tt,nt=Object.freeze({__proto__:null,CSSProperty:et});class rt{#_e;parentRule;#qe;styleSheetId;range;cssText;#ze;#je;#Ve;#We;type;constructor(e,t,n,r){this.#_e=e,this.parentRule=t,this.#Ge(n),this.type=r}rebase(e){if(this.styleSheetId===e.styleSheetId&&this.range)if(e.oldRange.equal(this.range))this.#Ge(e.payload);else{this.range=this.range.rebaseAfterTextEdit(e.oldRange,e.newRange);for(let t=0;t<this.#qe.length;++t)this.#qe[t].rebase(e)}}#Ge(e){this.styleSheetId=e.styleSheetId,this.range=e.range?n.TextRange.TextRange.fromObject(e.range):null;const t=e.shorthandEntries;this.#ze=new Map,this.#je=new Set;for(let e=0;e<t.length;++e)this.#ze.set(t[e].name,t[e].value),t[e].important&&this.#je.add(t[e].name);if(this.#qe=[],e.cssText&&this.range){const t=[];for(const n of e.cssProperties){if(!n.range)continue;const e=et.parsePayload(this,this.#qe.length,n);this.#qe.push(e);for(const n of e.getLonghandProperties())t.push(n)}for(const e of t)e.index=this.#qe.length,this.#qe.push(e)}else for(const t of e.cssProperties)this.#qe.push(et.parsePayload(this,this.#qe.length,t));this.#Ke(),this.#Qe(),this.#Ve=new Map;for(const e of this.#qe)e.activeInStyle()&&this.#Ve.set(e.name,e);this.cssText=e.cssText,this.#We=null}#Ke(){if(this.range)return;if(!this.#ze.size)return;const e=new Set;for(const t of this.#qe)e.add(t.name);const t=[];for(const n of this.#qe){const r=v().getShorthands(n.name)||[];for(const n of r){if(e.has(n))continue;const r=this.#ze.get(n);if(!r)continue;const s=Boolean(this.#je.has(n)),i=new et(this,this.allProperties().length,n,r,s,!1,!0,!1);t.push(i),e.add(n)}}this.#qe=this.#qe.concat(t)}#$e(){if(this.range)return this.#qe.filter((function(e){return Boolean(e.range)}));const e=[];for(const t of this.#qe){const n=v().getShorthands(t.name)||[];let r=!1;for(const e of n)if(this.#ze.get(e)){r=!0;break}r||e.push(t)}return e}leadingProperties(){return this.#We||(this.#We=this.#$e()),this.#We}target(){return this.#_e.target()}cssModel(){return this.#_e}#Qe(){const e=new Map,t=new Set;for(const n of this.#qe){const r=v().canonicalPropertyName(n.name);if(n.disabled||!n.parsedOk){n.name.startsWith("--")&&(e.get(r)?.setActive(!1),e.delete(r)),n.setActive(!1);continue}if(t.has(n))continue;for(const r of n.getLonghandProperties()){const n=e.get(r.name);n?!n.important||r.important?(n.setActive(!1),e.set(r.name,r)):r.setActive(!1):e.set(r.name,r),t.add(r)}const s=e.get(r);s?!s.important||n.important?(s.setActive(!1),e.set(r,n)):n.setActive(!1):e.set(r,n)}}allProperties(){return this.#qe}hasActiveProperty(e){return this.#Ve.has(e)}getPropertyValue(e){const t=this.#Ve.get(e);return t?t.value:""}isPropertyImplicit(e){const t=this.#Ve.get(e);return!!t&&t.implicit}propertyAt(e){return e<this.allProperties().length?this.allProperties()[e]:null}pastLastSourcePropertyIndex(){for(let e=this.allProperties().length-1;e>=0;--e)if(this.allProperties()[e].range)return e+1;return 0}#Xe(e){const t=this.propertyAt(e);if(t&&t.range)return t.range.collapseToStart();if(!this.range)throw new Error("CSSStyleDeclaration.range is null");return this.range.collapseToEnd()}newBlankProperty(e){e=void 0===e?this.pastLastSourcePropertyIndex():e;return new et(this,e,"","",!1,!1,!0,!1,"",this.#Xe(e))}setText(e,t){return this.range&&this.styleSheetId?this.#_e.setStyleText(this.styleSheetId,this.range,e,t):Promise.resolve(!1)}insertPropertyAt(e,t,n,r){this.newBlankProperty(e).setText(t+": "+n+";",!1,!0).then(r)}appendProperty(e,t,n){this.insertPropertyAt(this.allProperties().length,e,t,n)}}!function(e){e.Regular="Regular",e.Inline="Inline",e.Attributes="Attributes",e.Pseudo="Pseudo"}(tt||(tt={}));var st=Object.freeze({__proto__:null,CSSStyleDeclaration:rt,get Type(){return tt}});class it extends Ue{static parseSupportsPayload(e,t){return t.map((t=>new it(e,t)))}#Fe=!0;constructor(e,t){super(e),this.reinitialize(t)}reinitialize(e){this.text=e.text,this.range=e.range?n.TextRange.TextRange.fromObject(e.range):null,this.styleSheetId=e.styleSheetId,this.#Fe=e.active}active(){return this.#Fe}}var ot=Object.freeze({__proto__:null,CSSSupports:it});class at{cssModelInternal;styleSheetId;sourceURL;origin;style;constructor(e,t){if(this.cssModelInternal=e,this.styleSheetId=t.styleSheetId,this.styleSheetId){const e=this.getStyleSheetHeader(this.styleSheetId);this.sourceURL=e.sourceURL}this.origin=t.origin,this.style=new rt(this.cssModelInternal,this,t.style,tt.Regular)}rebase(e){this.styleSheetId===e.styleSheetId&&this.style.rebase(e)}resourceURL(){if(!this.styleSheetId)return s.DevToolsPath.EmptyUrlString;return this.getStyleSheetHeader(this.styleSheetId).resourceURL()}isUserAgent(){return"user-agent"===this.origin}isInjected(){return"injected"===this.origin}isViaInspector(){return"inspector"===this.origin}isRegular(){return"regular"===this.origin}isKeyframeRule(){return!1}cssModel(){return this.cssModelInternal}getStyleSheetHeader(e){const t=this.cssModelInternal.styleSheetHeaderForId(e);return console.assert(null!==t),t}}class lt{text;range;specificity;constructor(e){this.text=e.text,e.range&&(this.range=n.TextRange.TextRange.fromObject(e.range)),e.specificity&&(this.specificity=e.specificity)}rebase(e){this.range&&(this.range=this.range.rebaseAfterTextEdit(e.oldRange,e.newRange))}}class dt extends at{selectors;nestingSelectors;media;containerQueries;supports;scopes;layers;ruleTypes;wasUsed;constructor(e,t,n){super(e,{origin:t.origin,style:t.style,styleSheetId:t.styleSheetId}),this.reinitializeSelectors(t.selectorList),this.nestingSelectors=t.nestingSelectors,this.media=t.media?Xe.parseMediaArrayPayload(e,t.media):[],this.containerQueries=t.containerQueries?qe.parseContainerQueriesPayload(e,t.containerQueries):[],this.scopes=t.scopes?Ye.parseScopesPayload(e,t.scopes):[],this.supports=t.supports?it.parseSupportsPayload(e,t.supports):[],this.layers=t.layers?Ge.parseLayerPayload(e,t.layers):[],this.ruleTypes=t.ruleTypes||[],this.wasUsed=n||!1}static createDummyRule(e,t){const r={selectorList:{text:"",selectors:[{text:t,value:void 0}]},style:{styleSheetId:"0",range:new n.TextRange.TextRange(0,0,0,0),shorthandEntries:[],cssProperties:[]},origin:"inspector"};return new dt(e,r)}reinitializeSelectors(e){this.selectors=[];for(let t=0;t<e.selectors.length;++t)this.selectors.push(new lt(e.selectors[t]))}setSelectorText(e){const t=this.styleSheetId;if(!t)throw"No rule stylesheet id";const n=this.selectorRange();if(!n)throw"Rule selector is not editable";return this.cssModelInternal.setSelectorText(t,n,e)}selectorText(){return this.selectors.map((e=>e.text)).join(", ")}selectorRange(){if(0===this.selectors.length)return null;const e=this.selectors[0].range,t=this.selectors[this.selectors.length-1].range;return e&&t?new n.TextRange.TextRange(e.startLine,e.startColumn,t.endLine,t.endColumn):null}lineNumberInSource(e){const t=this.selectors[e];if(!t||!t.range||!this.styleSheetId)return 0;return this.getStyleSheetHeader(this.styleSheetId).lineNumberInSource(t.range.startLine)}columnNumberInSource(e){const t=this.selectors[e];if(!t||!t.range||!this.styleSheetId)return;return this.getStyleSheetHeader(this.styleSheetId).columnNumberInSource(t.range.startLine,t.range.startColumn)}rebase(e){if(this.styleSheetId!==e.styleSheetId)return;const t=this.selectorRange();if(t&&t.equal(e.oldRange))this.reinitializeSelectors(e.payload);else for(let t=0;t<this.selectors.length;++t)this.selectors[t].rebase(e);this.media.forEach((t=>t.rebase(e))),this.containerQueries.forEach((t=>t.rebase(e))),this.scopes.forEach((t=>t.rebase(e))),this.supports.forEach((t=>t.rebase(e))),super.rebase(e)}}class ct extends at{#Je;constructor(e,t){super(e,{origin:t.origin,style:t.style,styleSheetId:t.styleSheetId}),this.#Je=new lt(t.propertyName)}propertyName(){return this.#Je}initialValue(){return this.style.hasActiveProperty("initial-value")?this.style.getPropertyValue("initial-value"):null}syntax(){return this.style.getPropertyValue("syntax")}inherits(){return"true"===this.style.getPropertyValue("inherits")}setPropertyName(e){const t=this.styleSheetId;if(!t)throw new Error("No rule stylesheet id");const n=this.#Je.range;if(!n)throw new Error("Property name is not editable");return this.cssModelInternal.setPropertyRulePropertyName(t,n,e)}}class ht extends at{#Ye;constructor(e,t){super(e,{origin:t.origin,style:t.style,styleSheetId:t.styleSheetId}),this.#Ye=new lt(t.fontPaletteName)}name(){return this.#Ye}}class ut{#Ze;#et;constructor(e,t){this.#Ze=new lt(t.animationName),this.#et=t.keyframes.map((t=>new gt(e,t)))}name(){return this.#Ze}keyframes(){return this.#et}}class gt extends at{#tt;constructor(e,t){super(e,{origin:t.origin,style:t.style,styleSheetId:t.styleSheetId}),this.reinitializeKey(t.keyText)}key(){return this.#tt}reinitializeKey(e){this.#tt=new lt(e)}rebase(e){this.styleSheetId===e.styleSheetId&&this.#tt.range&&(e.oldRange.equal(this.#tt.range)?this.reinitializeKey(e.payload):this.#tt.rebase(e),super.rebase(e))}isKeyframeRule(){return!0}setKeyText(e){const t=this.styleSheetId;if(!t)throw"No rule stylesheet id";const n=this.#tt.range;if(!n)throw"Keyframe key is not editable";return this.cssModelInternal.setKeyframeKey(t,n,e)}}class pt extends at{#Je;#Fe;constructor(e,t){super(e,{origin:t.origin,style:t.style,styleSheetId:t.styleSheetId}),this.#Je=new lt(t.name),this.#Fe=t.active}name(){return this.#Je}active(){return this.#Fe}}var mt=Object.freeze({__proto__:null,CSSRule:at,CSSStyleRule:dt,CSSPropertyRule:ct,CSSFontPaletteValuesRule:ht,CSSKeyframesRule:ut,CSSKeyframeRule:gt,CSSPositionTryRule:pt});function ft(e,t){if(!t.styleSheetId||!t.range)return!1;for(const n of e)if(t.styleSheetId===n.styleSheetId&&n.range&&t.range.equal(n.range))return!0;return!1}function bt(e){const t=e.allProperties();for(let e=0;e<t.length;++e){const n=t[e];if(n.activeInStyle()&&v().isPropertyInherited(n.name))return!0}return!1}function yt(e){for(const t of e)s(t);const t=[];for(const s of e){const e=t[t.length-1];e&&"user-agent"===s.rule.origin&&"user-agent"===e.rule.origin&&s.rule.selectorList.text===e.rule.selectorList.text&&r(s)===r(e)?n(s,e):t.push(s)}return t;function n(e,t){const n=new Map,r=new Map;for(const e of t.rule.style.shorthandEntries)n.set(e.name,e.value);for(const e of t.rule.style.cssProperties)r.set(e.name,e.value);for(const t of e.rule.style.shorthandEntries)n.set(t.name,t.value);for(const t of e.rule.style.cssProperties)r.set(t.name,t.value);t.rule.style.shorthandEntries=[...n.entries()].map((([e,t])=>({name:e,value:t}))),t.rule.style.cssProperties=[...r.entries()].map((([e,t])=>({name:e,value:t})))}function r(e){return e.rule.media?e.rule.media.map((e=>e.text)).join(", "):null}function s(e){const{matchingSelectors:t,rule:n}=e;"user-agent"===n.origin&&t.length&&(n.selectorList.selectors=n.selectorList.selectors.filter(((e,n)=>t.includes(n))),n.selectorList.text=n.selectorList.selectors.map((e=>e.text)).join(", "),e.matchingSelectors=t.map(((e,t)=>t)))}}function It(e){const t=new Map;for(let n=0;n<e.matchingSelectors.length;n++){const r=e.matchingSelectors[n],s=e.rule.selectorList.selectors[r].text.match(/::highlight\((.*)\)/);if(s){const e=s[1],n=t.get(e);n?n.push(r):t.set(e,[r])}}return t}class vt{#nt;#rt;#st;constructor(e,t){this.#rt=e,this.#nt=t}isAtProperty(){return this.#nt instanceof ct}propertyName(){return this.#nt instanceof ct?this.#nt.propertyName().text:this.#nt.propertyName}initialValue(){return this.#nt instanceof ct?this.#nt.initialValue():this.#nt.initialValue?.text??null}inherits(){return this.#nt instanceof ct?this.#nt.inherits():this.#nt.inherits}syntax(){return this.#nt instanceof ct?this.#nt.syntax():`"${this.#nt.syntax}"`}#it(){if(this.#nt instanceof ct)return[];const{inherits:e,initialValue:t,syntax:n}=this.#nt,r=[{name:"inherits",value:`${e}`},{name:"syntax",value:`"${n}"`}];return void 0!==t&&r.push({name:"initial-value",value:t.text}),r}style(){return this.#st||(this.#st=this.#nt instanceof ct?this.#nt.style:new rt(this.#rt,null,{cssProperties:this.#it(),shorthandEntries:[]},tt.Pseudo)),this.#st}}class kt{#_e;#ot;#at;#lt;#et;#dt;#ct=new Map;#ht;#ut;#gt;#pt;#mt;#ft;#bt;#yt;#It;static async create(e){const t=new kt(e);return await t.init(e),t}constructor({cssModel:e,node:t,animationsPayload:n,parentLayoutNodeId:r,positionTryRules:s,propertyRules:i,cssPropertyRegistrations:o,fontPaletteValuesRule:a}){this.#_e=e,this.#ot=t,this.#at=new Map,this.#lt=new Map,this.#dt=[...i.map((t=>new ct(e,t))),...o].map((t=>new vt(e,t))),this.#et=[],n&&(this.#et=n.map((t=>new ut(e,t)))),this.#mt=s.map((t=>new pt(e,t))),this.#pt=r,this.#It=a?new ht(e,a):void 0,this.#ht=new Map,this.#ut=new Set,this.#gt=new Map,this.#ct=new Map}async init({matchedPayload:e,inheritedPayload:t,inlinePayload:n,attributesPayload:r,pseudoPayload:s,inheritedPseudoPayload:i}){e=yt(e);for(const e of t)e.matchedCSSRules=yt(e.matchedCSSRules);this.#ft=await this.buildMainCascade(n,r,e,t),[this.#bt,this.#yt]=this.buildPseudoCascades(s,i);for(const e of Array.from(this.#yt.values()).concat(Array.from(this.#bt.values())).concat(this.#ft))for(const t of e.styles())this.#gt.set(t,e);for(const e of this.#dt)this.#ct.set(e.propertyName(),e)}async buildMainCascade(e,t,n,r){const s=[],i=[];function o(){if(!t)return;const e=new rt(this.#_e,null,t,tt.Attributes);this.#ht.set(e,this.#ot),i.push(e)}if(e&&this.#ot.nodeType()===Node.ELEMENT_NODE){const t=new rt(this.#_e,null,e,tt.Inline);this.#ht.set(t,this.#ot),i.push(t)}let a;for(let e=n.length-1;e>=0;--e){const t=new dt(this.#_e,n[e].rule);!t.isInjected()&&!t.isUserAgent()||a||(a=!0,o.call(this)),this.#ht.set(t.style,this.#ot),i.push(t.style),this.addMatchingSelectors(this.#ot,t,n[e].matchingSelectors)}a||o.call(this),s.push(new St(this,i,!1));let l=this.#ot.parentNode;const d=async e=>e.hasAssignedSlot()?await(e.assignedSlot?.deferredNode.resolvePromise())??null:e.parentNode;for(let e=0;l&&r&&e<r.length;++e){const t=[],n=r[e],o=n.inlineStyle?new rt(this.#_e,null,n.inlineStyle,tt.Inline):null;o&&bt(o)&&(this.#ht.set(o,l),t.push(o),this.#ut.add(o));const a=n.matchedCSSRules||[];for(let e=a.length-1;e>=0;--e){const n=new dt(this.#_e,a[e].rule);this.addMatchingSelectors(l,n,a[e].matchingSelectors),bt(n.style)&&((n.style.allProperties().some((e=>v().isCustomProperty(e.name)))||!ft(i,n.style)&&!ft(this.#ut,n.style))&&(this.#ht.set(n.style,l),t.push(n.style),this.#ut.add(n.style)))}l=await d(l),s.push(new St(this,t,!0))}return new Rt(s,this.#dt)}buildSplitCustomHighlightCascades(e,t,n,r){const s=new Map;for(let r=e.length-1;r>=0;--r){const i=It(e[r]);for(const[o,a]of i){const i=new dt(this.#_e,e[r].rule);this.#ht.set(i.style,t),n&&this.#ut.add(i.style),this.addMatchingSelectors(t,i,a);const l=s.get(o);l?l.push(i.style):s.set(o,[i.style])}}for(const[e,t]of s){const s=new St(this,t,n,!0),i=r.get(e);i?i.push(s):r.set(e,[s])}}buildPseudoCascades(e,t){const n=new Map,r=new Map;if(!e)return[n,r];const s=new Map,i=new Map;for(let t=0;t<e.length;++t){const n=e[t],r=this.#ot.pseudoElements().get(n.pseudoType)?.at(-1)||null,o=[],a=n.matches||[];if("highlight"===n.pseudoType)this.buildSplitCustomHighlightCascades(a,this.#ot,!1,i);else{for(let e=a.length-1;e>=0;--e){const t=new dt(this.#_e,a[e].rule);o.push(t.style);const s=v().isHighlightPseudoType(n.pseudoType)?this.#ot:r;this.#ht.set(t.style,s),s&&this.addMatchingSelectors(s,t,a[e].matchingSelectors)}const e=v().isHighlightPseudoType(n.pseudoType),t=new St(this,o,!1,e);s.set(n.pseudoType,[t])}}if(t){let e=this.#ot.parentNode;for(let n=0;e&&n<t.length;++n){const r=t[n].pseudoElements;for(let t=0;t<r.length;++t){const n=r[t],o=n.matches||[];if("highlight"===n.pseudoType)this.buildSplitCustomHighlightCascades(o,e,!0,i);else{const t=[];for(let n=o.length-1;n>=0;--n){const r=new dt(this.#_e,o[n].rule);t.push(r.style),this.#ht.set(r.style,e),this.#ut.add(r.style),this.addMatchingSelectors(e,r,o[n].matchingSelectors)}const r=v().isHighlightPseudoType(n.pseudoType),i=new St(this,t,!0,r),a=s.get(n.pseudoType);a?a.push(i):s.set(n.pseudoType,[i])}}e=e.parentNode}}for(const[e,t]of s.entries())n.set(e,new Rt(t,this.#dt));for(const[e,t]of i.entries())r.set(e,new Rt(t,this.#dt));return[n,r]}addMatchingSelectors(e,t,n){for(const r of n){const n=t.selectors[r];n&&this.setSelectorMatches(e,n.text,!0)}}node(){return this.#ot}cssModel(){return this.#_e}hasMatchingSelectors(e){return(0===e.selectors.length||this.getMatchingSelectors(e).length>0)&&function(e){if(!e.parentRule)return!0;const t=e.parentRule,n=[...t.media,...t.containerQueries,...t.supports,...t.scopes];for(const e of n)if(!e.active())return!1;return!0}(e.style)}getParentLayoutNodeId(){return this.#pt}getMatchingSelectors(e){const t=this.nodeForStyle(e.style);if(!t||"number"!=typeof t.id)return[];const n=this.#lt.get(t.id);if(!n)return[];const r=[];for(let t=0;t<e.selectors.length;++t)n.get(e.selectors[t].text)&&r.push(t);return r}async recomputeMatchingSelectors(e){const t=this.nodeForStyle(e.style);if(!t)return;const n=[];for(const s of e.selectors)n.push(r.call(this,t,s.text));async function r(e,t){const n=e.ownerDocument;if(!n)return;if("number"==typeof e.id){const n=this.#lt.get(e.id);if(n&&n.has(t))return}if("number"!=typeof n.id)return;const r=await this.#ot.domModel().querySelectorAll(n.id,t);r&&("number"==typeof e.id?this.setSelectorMatches(e,t,-1!==r.indexOf(e.id)):this.setSelectorMatches(e,t,!1))}await Promise.all(n)}addNewRule(e,t){return this.#at.set(e.style,t),this.recomputeMatchingSelectors(e)}setSelectorMatches(e,t,n){if("number"!=typeof e.id)return;let r=this.#lt.get(e.id);r||(r=new Map,this.#lt.set(e.id,r)),r.set(t,n)}nodeStyles(){return s.assertNotNullOrUndefined(this.#ft),this.#ft.styles()}registeredProperties(){return this.#dt}getRegisteredProperty(e){return this.#ct.get(e)}fontPaletteValuesRule(){return this.#It}keyframes(){return this.#et}positionTryRules(){return this.#mt}pseudoStyles(e){s.assertNotNullOrUndefined(this.#bt);const t=this.#bt.get(e);return t?t.styles():[]}pseudoTypes(){return s.assertNotNullOrUndefined(this.#bt),new Set(this.#bt.keys())}customHighlightPseudoStyles(e){s.assertNotNullOrUndefined(this.#yt);const t=this.#yt.get(e);return t?t.styles():[]}customHighlightPseudoNames(){return s.assertNotNullOrUndefined(this.#yt),new Set(this.#yt.keys())}nodeForStyle(e){return this.#at.get(e)||this.#ht.get(e)||null}availableCSSVariables(e){const t=this.#gt.get(e)||null;return t?t.findAvailableCSSVariables(e):[]}computeCSSVariable(e,t){const n=this.#gt.get(e)||null;return n?n.computeCSSVariable(e,t):null}isInherited(e){return this.#ut.has(e)}propertyState(e){const t=this.#gt.get(e.ownerStyle);return t?t.propertyState(e):null}resetActiveProperties(){s.assertNotNullOrUndefined(this.#ft),s.assertNotNullOrUndefined(this.#bt),s.assertNotNullOrUndefined(this.#yt),this.#ft.reset();for(const e of this.#bt.values())e.reset();for(const e of this.#yt.values())e.reset()}}class St{#vt;styles;#kt;#St;propertiesState;activeProperties;constructor(e,t,n,r=!1){this.#vt=e,this.styles=t,this.#kt=n,this.#St=r,this.propertiesState=new Map,this.activeProperties=new Map}computeActiveProperties(){this.propertiesState.clear(),this.activeProperties.clear();for(let e=this.styles.length-1;e>=0;e--){const t=this.styles[e],n=t.parentRule;if((!n||n instanceof dt)&&(!n||this.#vt.hasMatchingSelectors(n)))for(const e of t.allProperties()){const n=v();if(this.#kt&&!this.#St&&!n.isPropertyInherited(e.name))continue;if(t.range&&!e.range)continue;if(!e.activeInStyle()){this.propertiesState.set(e,"Overloaded");continue}if(this.#kt){const t=this.#vt.getRegisteredProperty(e.name);if(t&&!t.inherits()){this.propertiesState.set(e,"Overloaded");continue}}const r=n.canonicalPropertyName(e.name);this.updatePropertyState(e,r);for(const t of e.getLonghandProperties())n.isCSSPropertyName(t.name)&&this.updatePropertyState(t,t.name)}}}updatePropertyState(e,t){const n=this.activeProperties.get(t);!n?.important||e.important?(n&&this.propertiesState.set(n,"Overloaded"),this.propertiesState.set(e,"Active"),this.activeProperties.set(t,e)):this.propertiesState.set(e,"Overloaded")}}class wt{nodeCascade;name;discoveryTime;rootDiscoveryTime;get isRootEntry(){return this.rootDiscoveryTime===this.discoveryTime}updateRoot(e){this.rootDiscoveryTime=Math.min(this.rootDiscoveryTime,e.rootDiscoveryTime)}constructor(e,t,n){this.nodeCascade=e,this.name=t,this.discoveryTime=n,this.rootDiscoveryTime=n}}class Ct{#wt=0;#Ct=[];#Rt=new Map;get(e,t){return this.#Rt.get(e)?.get(t)}add(e,t){const n=this.get(e,t);if(n)return n;const r=new wt(e,t,this.#wt++);this.#Ct.push(r);let s=this.#Rt.get(e);return s||(s=new Map,this.#Rt.set(e,s)),s.set(t,r),r}isInInProgressSCC(e){return this.#Ct.includes(e)}finishSCC(e){const t=this.#Ct.lastIndexOf(e);return console.assert(t>=0,"Root is not an in-progress scc"),this.#Ct.splice(t)}}class Rt{#xt;#Tt;#Mt;#Pt;#Lt;#Et;#dt;constructor(e,t){this.#xt=e,this.#Tt=new Map,this.#Mt=new Map,this.#Pt=new Map,this.#Lt=!1,this.#dt=t,this.#Et=new Map;for(const t of e)for(const e of t.styles)this.#Et.set(e,t)}findAvailableCSSVariables(e){const t=this.#Et.get(e);if(!t)return[];this.ensureInitialized();const n=this.#Mt.get(t);return n?Array.from(n.keys()):[]}computeCSSVariable(e,t){const n=this.#Et.get(e);return n?(this.ensureInitialized(),this.innerComputeCSSVariable(n,t)):null}innerComputeCSSVariable(e,t,n=new Ct){const r=this.#Mt.get(e),s=this.#Pt.get(e);if(!s||!r?.has(t))return null;if(s?.has(t))return s.get(t)||null;const i=r.get(t);if(null==i)return null;const o=Fe(`--${t}`,i.value);if(!o)return null;const a=n.add(e,t),l=Re.walk(o,[new Ae((e=>{const t="ownerStyle"in i.declaration?i.declaration.ownerStyle:i.declaration.style(),r=this.#Et.get(t);if(!r)return null;const s=n.get(r,e.name);if(s){if(n.isInInProgressSCC(s))return a.updateRoot(s),null}else{const t=this.innerComputeCSSVariable(r,e.name,n),s=n.get(r,e.name);if(s&&a.updateRoot(s),t?.value)return t?.value}return 0===e.fallback.length||e.matching.hasUnresolvedVarsRange(e.fallback[0],e.fallback[e.fallback.length-1])?null:e.matching.getComputedTextRange(e.fallback[0],e.fallback[e.fallback.length-1])}))]),d=Le.siblings(Le.declValue(l.ast.tree)),c=l.getComputedTextRange(d[0],d[d.length-1]);if(a.isRootEntry){const t=n.finishSCC(a);if(t.length>1){for(const n of t)console.assert(n.nodeCascade!==e,"Circles should be within the cascade"),s.set(n.name,null);return null}}if(l.hasUnresolvedVarsRange(d[0],d[d.length-1]))return s.set(t,null),null;const h={value:c,declaration:i.declaration};return s.set(t,h),h}styles(){return Array.from(this.#Et.keys())}propertyState(e){return this.ensureInitialized(),this.#Tt.get(e)||null}reset(){this.#Lt=!1,this.#Tt.clear(),this.#Mt.clear(),this.#Pt.clear()}ensureInitialized(){if(this.#Lt)return;this.#Lt=!0;const e=new Map;for(const t of this.#xt){t.computeActiveProperties();for(const[n,r]of t.propertiesState){if("Overloaded"===r){this.#Tt.set(n,"Overloaded");continue}const t=v().canonicalPropertyName(n.name);e.has(t)?this.#Tt.set(n,"Overloaded"):(e.set(t,n),this.#Tt.set(n,"Active"))}}for(const[t,n]of e){const r=n.ownerStyle,s=n.getLonghandProperties();if(!s.length)continue;let i=!1;for(const t of s){const n=v().canonicalPropertyName(t.name),s=e.get(n);if(s&&s.ownerStyle===r){i=!0;break}}i||(e.delete(t),this.#Tt.set(n,"Overloaded"))}const t=new Map;for(const e of this.#dt){const n=e.initialValue();t.set(e.propertyName(),n?{value:n,declaration:e}:null)}for(let e=this.#xt.length-1;e>=0;--e){const n=this.#xt[e],r=[];for(const e of n.activeProperties.entries()){const n=e[0],s=e[1];n.startsWith("--")&&(t.set(n,{value:s.value,declaration:s}),r.push(n))}const s=new Map(t),i=new Map;this.#Mt.set(n,s),this.#Pt.set(n,i);for(const e of r){const r=t.get(e);t.delete(e);const s=this.innerComputeCSSVariable(n,e);r&&s?.value===r.value&&(s.declaration=r.declaration),t.set(e,s)}}}}var xt=Object.freeze({__proto__:null,CSSRegisteredProperty:vt,CSSMatchedStyles:kt});const Tt={couldNotFindTheOriginalStyle:"Could not find the original style sheet.",thereWasAnErrorRetrievingThe:"There was an error retrieving the source styles."},Mt=r.i18n.registerUIStrings("core/sdk/CSSStyleSheetHeader.ts",Tt),Pt=r.i18n.getLocalizedString.bind(void 0,Mt);class Lt{#_e;id;frameId;sourceURL;hasSourceURL;origin;title;disabled;isInline;isMutable;isConstructed;startLine;startColumn;endLine;endColumn;contentLength;ownerNode;sourceMapURL;loadingFailed;#At;constructor(e,t){this.#_e=e,this.id=t.styleSheetId,this.frameId=t.frameId,this.sourceURL=t.sourceURL,this.hasSourceURL=Boolean(t.hasSourceURL),this.origin=t.origin,this.title=t.title,this.disabled=t.disabled,this.isInline=t.isInline,this.isMutable=t.isMutable,this.isConstructed=t.isConstructed,this.startLine=t.startLine,this.startColumn=t.startColumn,this.endLine=t.endLine,this.endColumn=t.endColumn,this.contentLength=t.length,t.ownerNode&&(this.ownerNode=new Lr(e.target(),t.ownerNode)),this.sourceMapURL=t.sourceMapURL,this.loadingFailed=t.loadingFailed??!1,this.#At=null}originalContentProvider(){if(!this.#At){const e=async()=>{const e=await this.#_e.originalStyleSheetText(this);return null===e?{error:Pt(Tt.couldNotFindTheOriginalStyle)}:new n.ContentData.ContentData(e,!1,"text/css")};this.#At=new n.StaticContentProvider.StaticContentProvider(this.contentURL(),this.contentType(),e)}return this.#At}setSourceMapURL(e){this.sourceMapURL=e}cssModel(){return this.#_e}isAnonymousInlineStyleSheet(){return!this.resourceURL()&&!this.#_e.sourceMapManager().sourceMapForClient(this)}isConstructedByNew(){return this.isConstructed&&0===this.sourceURL.length}resourceURL(){const e=this.isViaInspector()?this.viaInspectorResourceURL():this.sourceURL;return!e&&o.Runtime.experiments.isEnabled("styles-pane-css-changes")?this.dynamicStyleURL():e}getFrameURLPath(){const t=this.#_e.target().model(Gr);if(console.assert(Boolean(t)),!t)return"";const n=t.frameForId(this.frameId);if(!n)return"";console.assert(Boolean(n));const r=new e.ParsedURL.ParsedURL(n.url);let s=r.host+r.folderPathComponents;return s.endsWith("/")||(s+="/"),s}viaInspectorResourceURL(){return`inspector://${this.getFrameURLPath()}inspector-stylesheet`}dynamicStyleURL(){return`stylesheet://${this.getFrameURLPath()}style#${this.id}`}lineNumberInSource(e){return this.startLine+e}columnNumberInSource(e,t){return(e?0:this.startColumn)+t}containsLocation(e,t){const n=e===this.startLine&&t>=this.startColumn||e>this.startLine,r=e<this.endLine||e===this.endLine&&t<=this.endColumn;return n&&r}contentURL(){return this.resourceURL()}contentType(){return e.ResourceType.resourceTypes.Stylesheet}requestContent(){return this.requestContentData().then(n.ContentData.ContentData.asDeferredContent.bind(void 0))}async requestContentData(){const e=await this.#_e.getStyleSheetText(this.id);return null===e?{error:Pt(Tt.thereWasAnErrorRetrievingThe)}:new n.ContentData.ContentData(e,!1,"text/css")}async searchInContent(e,t,r){const s=await this.requestContentData();return n.TextUtils.performSearchInContentData(s,e,t,r)}isViaInspector(){return"inspector"===this.origin}createPageResourceLoadInitiator(){return{target:this.#_e.target(),frameId:this.frameId,initiatorUrl:this.hasSourceURL?s.DevToolsPath.EmptyUrlString:this.sourceURL}}}var Et=Object.freeze({__proto__:null,CSSStyleSheetHeader:Lt});let At=null;class Ot extends e.ObjectWrapper.ObjectWrapper{#Ot=new WeakMap;#Dt=new Map;#Nt=new Map;#Ft=null;#Bt=new Map;#Ht=new Map;constructor(){super(),z.instance().observeModels(Gr,this)}static instance({forceNew:e}={forceNew:!1}){return At&&!e||(At=new Ot),At}modelAdded(e){const t=e.addEventListener(Vr.FrameAdded,this.frameAdded,this),n=e.addEventListener(Vr.FrameDetached,this.frameDetached,this),r=e.addEventListener(Vr.FrameNavigated,this.frameNavigated,this),s=e.addEventListener(Vr.ResourceAdded,this.resourceAdded,this);this.#Ot.set(e,[t,n,r,s]),this.#Nt.set(e.target().id(),new Set)}modelRemoved(t){const n=this.#Ot.get(t);n&&e.EventTarget.removeEventListeners(n);const r=this.#Nt.get(t.target().id());if(r)for(const e of r)this.decreaseOrRemoveFrame(e);this.#Nt.delete(t.target().id())}frameAdded(e){const t=e.data,n=this.#Dt.get(t.id);if(n)t.setCreationStackTrace(n.frame.getCreationStackTraceData()),this.#Dt.set(t.id,{frame:t,count:n.count+1});else{const e=this.#Bt.get(t.id);e?.creationStackTrace&&e?.creationStackTraceTarget&&t.setCreationStackTrace({creationStackTrace:e.creationStackTrace,creationStackTraceTarget:e.creationStackTraceTarget}),this.#Dt.set(t.id,{frame:t,count:1}),this.#Bt.delete(t.id)}this.resetOutermostFrame();const r=this.#Nt.get(t.resourceTreeModel().target().id());r&&r.add(t.id),this.dispatchEventToListeners("FrameAddedToTarget",{frame:t}),this.resolveAwaitedFrame(t)}frameDetached(e){const{frame:t,isSwap:n}=e.data;if(this.decreaseOrRemoveFrame(t.id),n&&!this.#Dt.get(t.id)){const e=t.getCreationStackTraceData(),n={...e.creationStackTrace&&{creationStackTrace:e.creationStackTrace},...e.creationStackTrace&&{creationStackTraceTarget:e.creationStackTraceTarget}};this.#Bt.set(t.id,n)}const r=this.#Nt.get(t.resourceTreeModel().target().id());r&&r.delete(t.id)}frameNavigated(e){const t=e.data;this.dispatchEventToListeners("FrameNavigated",{frame:t}),t.isOutermostFrame()&&this.dispatchEventToListeners("OutermostFrameNavigated",{frame:t})}resourceAdded(e){this.dispatchEventToListeners("ResourceAdded",{resource:e.data})}decreaseOrRemoveFrame(e){const t=this.#Dt.get(e);t&&(1===t.count?(this.#Dt.delete(e),this.resetOutermostFrame(),this.dispatchEventToListeners("FrameRemoved",{frameId:e})):t.count--)}resetOutermostFrame(){const e=this.getAllFrames().filter((e=>e.isOutermostFrame()));this.#Ft=e.length>0?e[0]:null}getFrame(e){const t=this.#Dt.get(e);return t?t.frame:null}getAllFrames(){return Array.from(this.#Dt.values(),(e=>e.frame))}getOutermostFrame(){return this.#Ft}async getOrWaitForFrame(e,t){const n=this.getFrame(e);return!n||t&&t===n.resourceTreeModel().target()?new Promise((n=>{const r=this.#Ht.get(e);r?r.push({notInTarget:t,resolve:n}):this.#Ht.set(e,[{notInTarget:t,resolve:n}])})):n}resolveAwaitedFrame(e){const t=this.#Ht.get(e.id);if(!t)return;const n=t.filter((({notInTarget:t,resolve:n})=>!(!t||t!==e.resourceTreeModel().target())||(n(e),!1)));n.length>0?this.#Ht.set(e.id,n):this.#Ht.delete(e.id)}}var Dt=Object.freeze({__proto__:null,FrameManager:Ot});class Nt{static fromLocalObject(e){return new _t(e)}static type(e){if(null===e)return"null";const t=typeof e;return"object"!==t&&"function"!==t?t:e.type}static isNullOrUndefined(e){if(void 0===e)return!0;switch(e.type){case"object":return"null"===e.subtype;case"undefined":return!0;default:return!1}}static arrayNameFromDescription(e){return e.replace(Vt,"").replace(Wt,"")}static arrayLength(e){if("array"!==e.subtype&&"typedarray"!==e.subtype)return 0;const t=e.description&&e.description.match(Vt),n=e.description&&e.description.match(Wt);return t?parseInt(t[1],10):n?parseInt(n[1],10):0}static arrayBufferByteLength(e){if("arraybuffer"!==e.subtype)return 0;const t=e.description&&e.description.match(Vt);return t?parseInt(t[1],10):0}static unserializableDescription(e){if("number"==typeof e){const t=String(e);if(0===e&&1/e<0)return"-0";if("NaN"===t||"Infinity"===t||"-Infinity"===t)return t}return"bigint"==typeof e?e+"n":null}static toCallArgument(e){const t=typeof e;if("undefined"===t)return{};const n=Nt.unserializableDescription(e);if("number"===t)return null!==n?{unserializableValue:n}:{value:e};if("bigint"===t)return{unserializableValue:n};if("string"===t||"boolean"===t)return{value:e};if(!e)return{value:null};const r=e;if(e instanceof Nt){const t=e.unserializableValue();if(void 0!==t)return{unserializableValue:t}}else if(void 0!==r.unserializableValue)return{unserializableValue:r.unserializableValue};return void 0!==r.objectId?{objectId:r.objectId}:{value:r.value}}static async loadFromObjectPerProto(e,t,n=!1){const r=await Promise.all([e.getAllProperties(!0,t,n),e.getOwnProperties(t,n)]),s=r[0].properties,i=r[1].properties,o=r[1].internalProperties;if(!i||!s)return{properties:null,internalProperties:null};const a=new Map,l=[];for(let e=0;e<s.length;e++){const t=s[e];t.symbol?l.push(t):(t.isOwn||"__proto__"!==t.name)&&a.set(t.name,t)}for(let e=0;e<i.length;e++){const t=i[e];t.isAccessorProperty()||(t.private||t.symbol?l.push(t):a.set(t.name,t))}return{properties:[...a.values()].concat(l),internalProperties:o||null}}customPreview(){return null}get objectId(){return"Not implemented"}get type(){throw"Not implemented"}get subtype(){throw"Not implemented"}get value(){throw"Not implemented"}unserializableValue(){throw"Not implemented"}get description(){throw"Not implemented"}set description(e){throw"Not implemented"}get hasChildren(){throw"Not implemented"}get preview(){}get className(){return null}arrayLength(){throw"Not implemented"}arrayBufferByteLength(){throw"Not implemented"}getOwnProperties(e,t){throw"Not implemented"}getAllProperties(e,t,n){throw"Not implemented"}async deleteProperty(e){throw"Not implemented"}async setPropertyValue(e,t){throw"Not implemented"}callFunction(e,t){throw"Not implemented"}callFunctionJSON(e,t){throw"Not implemented"}release(){}debuggerModel(){throw new Error("DebuggerModel-less object")}runtimeModel(){throw new Error("RuntimeModel-less object")}isNode(){return!1}isLinearMemoryInspectable(){return!1}webIdl}class Ft extends Nt{runtimeModelInternal;#Ut;#g;#_t;#qt;#zt;hasChildrenInternal;#jt;#Vt;#u;#Wt;#Gt;constructor(e,t,n,r,s,i,o,a,l,d){super(),this.runtimeModelInternal=e,this.#Ut=e.target().runtimeAgent(),this.#g=n,this.#_t=r,t?(this.#qt=t,this.#zt=o,this.hasChildrenInternal="symbol"!==n,this.#jt=a):(this.#zt=o,!this.description&&i&&(this.#zt=i),this.#zt||"object"==typeof s&&null!==s||(this.#zt=String(s)),this.hasChildrenInternal=!1,"string"==typeof i?(this.#Vt=i,"Infinity"===i||"-Infinity"===i||"-0"===i||"NaN"===i?this.#u=Number(i):"bigint"===n&&i.endsWith("n")?this.#u=BigInt(i.substring(0,i.length-1)):this.#u=i):this.#u=s),this.#Wt=l||null,this.#Gt="string"==typeof d?d:null}customPreview(){return this.#Wt}get objectId(){return this.#qt}get type(){return this.#g}get subtype(){return this.#_t}get value(){return this.#u}unserializableValue(){return this.#Vt}get description(){return this.#zt}set description(e){this.#zt=e}get hasChildren(){return this.hasChildrenInternal}get preview(){return this.#jt}get className(){return this.#Gt}getOwnProperties(e,t=!1){return this.doGetProperties(!0,!1,t,e)}getAllProperties(e,t,n=!1){return this.doGetProperties(!1,e,n,t)}async createRemoteObject(e){return this.runtimeModelInternal.createRemoteObject(e)}async doGetProperties(e,t,n,r){if(!this.#qt)return{properties:null,internalProperties:null};const s=await this.#Ut.invoke_getProperties({objectId:this.#qt,ownProperties:e,accessorPropertiesOnly:t,nonIndexedPropertiesOnly:n,generatePreview:r});if(s.getError())return{properties:null,internalProperties:null};if(s.exceptionDetails)return this.runtimeModelInternal.exceptionThrown(Date.now(),s.exceptionDetails),{properties:null,internalProperties:null};const{result:i=[],internalProperties:o=[],privateProperties:a=[]}=s,l=[];for(const e of i){const t=e.value?await this.createRemoteObject(e.value):null,n=e.symbol?this.runtimeModelInternal.createRemoteObject(e.symbol):null,r=new Ut(e.name,t,Boolean(e.enumerable),Boolean(e.writable),Boolean(e.isOwn),Boolean(e.wasThrown),n);void 0===e.value&&(e.get&&"undefined"!==e.get.type&&(r.getter=this.runtimeModelInternal.createRemoteObject(e.get)),e.set&&"undefined"!==e.set.type&&(r.setter=this.runtimeModelInternal.createRemoteObject(e.set))),l.push(r)}for(const e of a){const t=e.value?this.runtimeModelInternal.createRemoteObject(e.value):null,n=new Ut(e.name,t,!0,!0,!0,!1,void 0,!1,void 0,!0);void 0===e.value&&(e.get&&"undefined"!==e.get.type&&(n.getter=this.runtimeModelInternal.createRemoteObject(e.get)),e.set&&"undefined"!==e.set.type&&(n.setter=this.runtimeModelInternal.createRemoteObject(e.set))),l.push(n)}const d=[];for(const e of o){if(!e.value)continue;const t=this.runtimeModelInternal.createRemoteObject(e.value);d.push(new Ut(e.name,t,!0,!1,void 0,void 0,void 0,!0))}return{properties:l,internalProperties:d}}async setPropertyValue(e,t){if(!this.#qt)return"Can’t set a property of non-object.";const n=await this.#Ut.invoke_evaluate({expression:t,silent:!0});if(n.getError()||n.exceptionDetails)return n.getError()||("string"!==n.result.type?n.result.description:n.result.value);"string"==typeof e&&(e=Nt.toCallArgument(e));const r=this.doSetObjectPropertyValue(n.result,e);return n.result.objectId&&this.#Ut.invoke_releaseObject({objectId:n.result.objectId}),r}async doSetObjectPropertyValue(e,t){const n=[t,Nt.toCallArgument(e)],r=await this.#Ut.invoke_callFunctionOn({objectId:this.#qt,functionDeclaration:"function(a, b) { this[a] = b; }",arguments:n,silent:!0}),s=r.getError();return s||r.exceptionDetails?s||r.result.description:void 0}async deleteProperty(e){if(!this.#qt)return"Can’t delete a property of non-object.";const t=await this.#Ut.invoke_callFunctionOn({objectId:this.#qt,functionDeclaration:"function(a) { delete this[a]; return !(a in this); }",arguments:[e],silent:!0});return t.getError()||t.exceptionDetails?t.getError()||t.result.description:t.result.value?void 0:"Failed to delete property."}async callFunction(e,t){const n=await this.#Ut.invoke_callFunctionOn({objectId:this.#qt,functionDeclaration:e.toString(),arguments:t,silent:!0});return n.getError()?{object:null,wasThrown:!1}:{object:this.runtimeModelInternal.createRemoteObject(n.result),wasThrown:Boolean(n.exceptionDetails)}}async callFunctionJSON(e,t){const n=await this.#Ut.invoke_callFunctionOn({objectId:this.#qt,functionDeclaration:e.toString(),arguments:t,silent:!0,returnByValue:!0});return n.getError()||n.exceptionDetails?null:n.result.value}release(){this.#qt&&this.#Ut.invoke_releaseObject({objectId:this.#qt})}arrayLength(){return Nt.arrayLength(this)}arrayBufferByteLength(){return Nt.arrayBufferByteLength(this)}debuggerModel(){return this.runtimeModelInternal.debuggerModel()}runtimeModel(){return this.runtimeModelInternal}isNode(){return Boolean(this.#qt)&&"object"===this.type&&"node"===this.subtype}isLinearMemoryInspectable(){return"object"===this.type&&void 0!==this.subtype&&["webassemblymemory","typedarray","dataview","arraybuffer"].includes(this.subtype)}}class Bt extends Ft{#Kt;#Qt;constructor(e,t,n,r,s,i,o,a,l){super(e,t,r,s,i,o,a,l),this.#Kt=n,this.#Qt=void 0}async doGetProperties(e,t,n){if(t)return{properties:[],internalProperties:[]};if(this.#Qt)return{properties:this.#Qt.slice(),internalProperties:null};const r=await super.doGetProperties(e,t,!1,!0);if(this.#Kt&&Array.isArray(r.properties)&&(this.#Qt=r.properties.slice(),!this.#Kt.callFrameId))for(const e of this.#Qt)e.writable=!1;return r}async doSetObjectPropertyValue(e,t){const n=t.value,r=await this.debuggerModel().setVariableValue(this.#Kt.number,n,Nt.toCallArgument(e),this.#Kt.callFrameId);if(r)return r;if(this.#Qt)for(const t of this.#Qt)t.name===n&&(t.value=this.runtimeModel().createRemoteObject(e))}}class Ht{number;callFrameId;constructor(e,t){this.number=e,this.callFrameId=t}}class Ut{name;value;enumerable;writable;isOwn;wasThrown;symbol;synthetic;syntheticSetter;private;getter;setter;webIdl;constructor(e,t,n,r,s,i,o,a,l,d){this.name=e,this.value=null!==t?t:void 0,this.enumerable=void 0===n||n;const c=!a||Boolean(l);this.writable=void 0!==r?r:c,this.isOwn=Boolean(s),this.wasThrown=Boolean(i),o&&(this.symbol=o),this.synthetic=Boolean(a),l&&(this.syntheticSetter=l),this.private=Boolean(d)}async setSyntheticValue(e){if(!this.syntheticSetter)return!1;const t=await this.syntheticSetter(e);return t&&(this.value=t),Boolean(t)}isAccessorProperty(){return Boolean(this.getter||this.setter)}match({includeNullOrUndefinedValues:e,regex:t}){return!(null!==t&&!t.test(this.name)&&!t.test(this.value?.description??""))&&!(!e&&!this.isAccessorProperty()&&Nt.isNullOrUndefined(this.value))}cloneWithNewName(e){const t=new Ut(e,this.value??null,this.enumerable,this.writable,this.isOwn,this.wasThrown,this.symbol,this.synthetic,this.syntheticSetter,this.private);return t.getter=this.getter,t.setter=this.setter,t}}class _t extends Nt{valueInternal;#$t;#Xt;constructor(e){super(),this.valueInternal=e}get objectId(){}get value(){return this.valueInternal}unserializableValue(){return Nt.unserializableDescription(this.valueInternal)||void 0}get description(){if(this.#$t)return this.#$t;if("object"===this.type)switch(this.subtype){case"array":this.#$t=this.concatenate("[","]",function(e){return this.formatValue(e.value||null)}.bind(this));break;case"date":this.#$t=String(this.valueInternal);break;case"null":this.#$t="null";break;default:this.#$t=this.concatenate("{","}",function(e){let t=e.name;return/^\s|\s$|^$|\n/.test(t)&&(t='"'+t.replace(/\n/g,"↵")+'"'),t+": "+this.formatValue(e.value||null)}.bind(this))}else this.#$t=String(this.valueInternal);return this.#$t}formatValue(e){if(!e)return"undefined";const t=e.description||"";return"string"===e.type?'"'+t.replace(/\n/g,"↵")+'"':t}concatenate(e,t,n){let r=e;const s=this.children();for(let e=0;e<s.length;++e){const t=n(s[e]);if(r.length+t.length>100){r+=",…";break}e&&(r+=", "),r+=t}return r+=t,r}get type(){return typeof this.valueInternal}get subtype(){return null===this.valueInternal?"null":Array.isArray(this.valueInternal)?"array":this.valueInternal instanceof Date?"date":void 0}get hasChildren(){return"object"==typeof this.valueInternal&&null!==this.valueInternal&&Boolean(Object.keys(this.valueInternal).length)}async getOwnProperties(e,t=!1){let n=this.children();return t&&(n=n.filter((e=>!function(e){const t=Number(e)>>>0;return String(t)===e}(e.name)))),{properties:n,internalProperties:null}}async getAllProperties(e,t,n=!1){return e?{properties:[],internalProperties:null}:await this.getOwnProperties(t,n)}children(){return this.hasChildren?(this.#Xt||(this.#Xt=Object.entries(this.valueInternal).map((([e,t])=>new Ut(e,t instanceof Nt?t:Nt.fromLocalObject(t))))),this.#Xt):[]}arrayLength(){return Array.isArray(this.valueInternal)?this.valueInternal.length:0}async callFunction(e,t){const n=this.valueInternal,r=t?t.map((e=>e.value)):[];let s,i=!1;try{s=e.apply(n,r)}catch(e){i=!0}return{object:Nt.fromLocalObject(s),wasThrown:i}}async callFunctionJSON(e,t){const n=this.valueInternal,r=t?t.map((e=>e.value)):[];let s;try{s=e.apply(n,r)}catch(e){s=null}return s}}class qt{#Jt;constructor(e){this.#Jt=e}static objectAsArray(e){if(!e||"object"!==e.type||"array"!==e.subtype&&"typedarray"!==e.subtype)throw new Error("Object is empty or not an array");return new qt(e)}static async createFromRemoteObjects(e){if(!e.length)throw new Error("Input array is empty");const t=await e[0].callFunction((function(...e){return e}),e.map(Nt.toCallArgument));if(t.wasThrown||!t.object)throw new Error("Call function throws exceptions or returns empty value");return qt.objectAsArray(t.object)}async at(e){if(e<0||e>this.#Jt.arrayLength())throw new Error("Out of range");const t=await this.#Jt.callFunction((function(e){return this[e]}),[Nt.toCallArgument(e)]);if(t.wasThrown||!t.object)throw new Error("Exception in callFunction or result value is empty");return t.object}length(){return this.#Jt.arrayLength()}map(e){const t=[];for(let n=0;n<this.length();++n)t.push(this.at(n).then(e));return Promise.all(t)}object(){return this.#Jt}}class zt{#Jt;constructor(e){this.#Jt=e}static objectAsFunction(e){if(!e||"function"!==e.type)throw new Error("Object is empty or not a function");return new zt(e)}targetFunction(){return this.#Jt.getOwnProperties(!1).then(function(e){if(!e.internalProperties)return this.#Jt;const t=e.internalProperties;for(const e of t)if("[[TargetFunction]]"===e.name)return e.value;return this.#Jt}.bind(this))}targetFunctionDetails(){return this.targetFunction().then(function(t){const n=e.bind(null,this.#Jt!==t?t:null);return t.debuggerModel().functionDetailsPromise(t).then(n)}.bind(this));function e(e,t){return e&&e.release(),t}}object(){return this.#Jt}}class jt{#Yt;#Zt;#en;constructor(e){this.#Yt=e}static objectAsError(e){if("error"!==e.subtype)throw new Error(`Object of type ${e.subtype} is not an error`);return new jt(e)}get errorStack(){return this.#Yt.description??""}exceptionDetails(){return this.#Zt||(this.#Zt=this.#tn()),this.#Zt}#tn(){return this.#Yt.objectId?this.#Yt.runtimeModel().getExceptionDetails(this.#Yt.objectId):Promise.resolve(void 0)}cause(){return this.#en||(this.#en=this.#nn()),this.#en}async#nn(){const e=await this.#Yt.getAllProperties(!1,!1),t=e.properties?.find((e=>"cause"===e.name));return t?.value}}const Vt=/\(([0-9]+)\)/,Wt=/\[([0-9]+)\]/;var Gt=Object.freeze({__proto__:null,RemoteObject:Nt,RemoteObjectImpl:Ft,ScopeRemoteObject:Bt,ScopeRef:Ht,RemoteObjectProperty:Ut,LocalJSONObject:_t,RemoteArrayBuffer:class{#Jt;constructor(e){if("object"!==e.type||"arraybuffer"!==e.subtype)throw new Error("Object is not an arraybuffer");this.#Jt=e}byteLength(){return this.#Jt.arrayBufferByteLength()}async bytes(e=0,t=this.byteLength()){if(e<0||e>=this.byteLength())throw new RangeError("start is out of range");if(t<e||t>this.byteLength())throw new RangeError("end is out of range");return await this.#Jt.callFunctionJSON((function(e,t){return[...new Uint8Array(this,e,t)]}),[{value:e},{value:t-e}])}object(){return this.#Jt}},RemoteArray:qt,RemoteFunction:zt,RemoteError:jt,LinearMemoryInspectable:class{object;expression;constructor(e,t){if(!e.isLinearMemoryInspectable())throw new Error("object must be linear memory inspectable");this.object=e,this.expression=t}}});class Kt extends h{constructor(e){super(e)}async read(t,n,r){const s=await this.target().ioAgent().invoke_read({handle:t,offset:r,size:n});if(s.getError())throw new Error(s.getError());return s.eof?null:s.base64Encoded?e.Base64.decode(s.data):s.data}async close(e){(await this.target().ioAgent().invoke_close({handle:e})).getError()&&console.error("Could not close stream.")}async resolveBlob(e){const t=e instanceof Nt?e.objectId:e;if(!t)throw new Error("Remote object has undefined objectId");const n=await this.target().ioAgent().invoke_resolveBlob({objectId:t});if(n.getError())throw new Error(n.getError());return`blob:${n.uuid}`}async readToString(e){const t=[],n=new TextDecoder;for(;;){const r=await this.read(e,4194304);if(null===r){t.push(n.decode());break}r instanceof ArrayBuffer?t.push(n.decode(r,{stream:!0})):t.push(r)}return t.join("")}}h.register(Kt,{capabilities:131072,autostart:!0});var Qt=Object.freeze({__proto__:null,IOModel:Kt});const $t={loadCanceledDueToReloadOf:"Load canceled due to reload of inspected page"},Xt=r.i18n.registerUIStrings("core/sdk/PageResourceLoader.ts",$t),Jt=r.i18n.getLocalizedString.bind(void 0,Xt);function Yt(e){return"extensionId"in e}let Zt=null;class en extends e.ObjectWrapper.ObjectWrapper{#rn;#sn;#in;#on;#an;#ln;constructor(e,t){super(),this.#rn=0,this.#sn=new Map,this.#in=t,this.#on=new Map,this.#an=[],z.instance().addModelListener(Gr,Vr.PrimaryPageChanged,this.onPrimaryPageChanged,this),this.#ln=e}static instance({forceNew:e,loadOverride:t,maxConcurrentLoads:n}={forceNew:!1,loadOverride:null,maxConcurrentLoads:500}){return Zt&&!e||(Zt=new en(t,n)),Zt}static removeInstance(){Zt=null}onPrimaryPageChanged(e){const{frame:t,type:n}=e.data;if(!t.isOutermostFrame())return;for(const{reject:e}of this.#an)e(new Error(Jt($t.loadCanceledDueToReloadOf)));this.#an=[];const r=t.resourceTreeModel().target(),s=new Map;for(const[e,t]of this.#on.entries())"Activation"===n&&r===t.initiator.target&&s.set(e,t);this.#on=s,this.dispatchEventToListeners("Update")}getResourcesLoaded(){return this.#on}getScopedResourcesLoaded(){return new Map([...this.#on].filter((([e,t])=>z.instance().isInScope(t.initiator.target)||Yt(t.initiator))))}getNumberOfResources(){return{loading:this.#rn,queued:this.#an.length,resources:this.#on.size}}getScopedNumberOfResources(){const e=z.instance();let t=0;for(const[n,r]of this.#sn){const s=e.targetById(n);e.isInScope(s)&&(t+=r)}return{loading:t,resources:this.getScopedResourcesLoaded().size}}async acquireLoadSlot(e){if(this.#rn++,e){const t=this.#sn.get(e.id())||0;this.#sn.set(e.id(),t+1)}if(this.#rn>this.#in){const e={resolve:()=>{},reject:()=>{}},t=new Promise(((t,n)=>{e.resolve=t,e.reject=n}));this.#an.push(e),await t}}releaseLoadSlot(e){if(this.#rn--,e){const t=this.#sn.get(e.id());t&&this.#sn.set(e.id(),t-1)}const t=this.#an.shift();t&&t.resolve()}static makeExtensionKey(e,t){if(Yt(t)&&t.extensionId)return`${e}-${t.extensionId}`;throw new Error("Invalid initiator")}static makeKey(e,t){if(t.frameId)return`${e}-${t.frameId}`;if(t.target)return`${e}-${t.target.id()}`;throw new Error("Invalid initiator")}resourceLoadedThroughExtension(e){const t=en.makeExtensionKey(e.url,e.initiator);this.#on.set(t,e),this.dispatchEventToListeners("Update")}async loadResource(e,t){if(Yt(t))throw new Error("Invalid initiator");const n=en.makeKey(e,t),r={success:null,size:null,errorMessage:void 0,url:e,initiator:t};this.#on.set(n,r),this.dispatchEventToListeners("Update");try{await this.acquireLoadSlot(t.target);const n=this.dispatchLoad(e,t),s=await n;if(r.errorMessage=s.errorDescription.message,r.success=s.success,s.success)return r.size=s.content.length,{content:s.content};throw new Error(s.errorDescription.message)}catch(e){throw void 0===r.errorMessage&&(r.errorMessage=e.message),null===r.success&&(r.success=!1),e}finally{this.releaseLoadSlot(t.target),this.dispatchEventToListeners("Update")}}async dispatchLoad(t,n){if(Yt(n))throw new Error("Invalid initiator");let r=null;if(this.#ln)return this.#ln(t);const s=new e.ParsedURL.ParsedURL(t),i=tn().get()&&s&&"file"!==s.scheme&&"data"!==s.scheme&&"devtools"!==s.scheme;if(a.userMetrics.developerResourceScheme(this.getDeveloperResourceScheme(s)),i){try{if(n.target){a.userMetrics.developerResourceLoaded(0),a.rnPerfMetrics.developerResourceLoadingStarted(s,0);const e=await this.loadFromTarget(n.target,n.frameId,t);return a.rnPerfMetrics.developerResourceLoadingFinished(s,0,e),e}const e=Ot.instance().getFrame(n.frameId);if(e){a.userMetrics.developerResourceLoaded(1),a.rnPerfMetrics.developerResourceLoadingStarted(s,1);const r=await this.loadFromTarget(e.resourceTreeModel().target(),n.frameId,t);return a.rnPerfMetrics.developerResourceLoadingFinished(s,0,r),r}}catch(e){e instanceof Error&&(a.userMetrics.developerResourceLoaded(2),r=e.message),a.rnPerfMetrics.developerResourceLoadingFinished(s,2,{success:!1,errorDescription:{message:r}})}a.userMetrics.developerResourceLoaded(3),a.rnPerfMetrics.developerResourceLoadingStarted(s,3)}else{const e=tn().get()?6:5;a.userMetrics.developerResourceLoaded(e),a.rnPerfMetrics.developerResourceLoadingStarted(s,e)}const o=await ae.instance().loadResource(t);return i&&!o.success&&a.userMetrics.developerResourceLoaded(7),r&&(o.errorDescription.message=`Fetch through target failed: ${r}; Fallback: ${o.errorDescription.message}`),a.rnPerfMetrics.developerResourceLoadingFinished(s,4,o),o}getDeveloperResourceScheme(e){if(!e||""===e.scheme)return 1;const t="localhost"===e.host||e.host.endsWith(".localhost");switch(e.scheme){case"file":return 7;case"data":return 6;case"blob":return 8;case"http":return t?4:2;case"https":return t?5:3}return 0}async loadFromTarget(t,n,r){const s=t.model(X),i=t.model(Kt),o=e.Settings.Settings.instance().moduleSetting("cache-disabled").get(),l=await s.loadNetworkResource(n,r,{disableCache:o,includeCredentials:!0});try{const e=l.stream?await i.readToString(l.stream):"";return{success:l.success,content:e,errorDescription:{statusCode:l.httpStatusCode||0,netError:l.netError,netErrorName:l.netErrorName,message:a.ResourceLoader.netErrorToMessage(l.netError,l.httpStatusCode,l.netErrorName)||"",urlValid:void 0}}}finally{l.stream&&i.close(l.stream)}}}function tn(){return e.Settings.Settings.instance().createSetting("load-through-target",!0)}var nn=Object.freeze({__proto__:null,ResourceKey:class{key;constructor(e){this.key=e}},PageResourceLoader:en,getLoadThroughTargetSetting:tn});function rn(e,t){return e.map((e=>function(e,t){const n=new Map,r=[];let s=0,i=0;for(const[o,a]of function*(e){const t=new vn(e);let n=0,r=0;for(;t.hasNext();){","===t.peek()&&t.next();const[e,s]=[t.nextVLQ(),t.nextVLQ()];if(0===e&&s<n)throw new Error("Malformed original scope encoding: start/end items must be ordered w.r.t. source positions");if(n=s,!t.hasNext()||","===t.peek()){yield[r++,{line:e,column:s}];continue}const i={line:e,column:s,kind:t.nextVLQ(),flags:t.nextVLQ(),variables:[]};for(1&i.flags&&(i.name=t.nextVLQ());t.hasNext()&&","!==t.peek();)i.variables.push(t.nextVLQ());yield[r++,i]}}(e)){s+=a.line;const{column:e}=a;if(sn(a)){i+=a.kind;const l=dn(i,t);if(void 0===l)throw new Error(`Scope does not have a valid kind '${l}'`);const d={start:{line:s,column:e},end:{line:s,column:e},kind:l,name:dn(a.name,t),variables:a.variables.map((e=>t[e])),children:[]};r.push(d),n.set(o,d)}else{const t=r.pop();if(!t)throw new Error('Scope items not nested properly: encountered "end" item without "start" item');if(t.end={line:s,column:e},0===r.length)return{root:t,scopeForItemIndex:n};r[r.length-1].children.push(t)}}throw new Error("Malformed original scope encoding")}(e,t)))}function sn(e){return"kind"in e}function on(e,t,n){const r=[{start:{line:0,column:0},end:{line:0,column:0},isScope:!1,children:[],values:[]}],s=new Map;for(const i of function*(e){const t=new vn(e);let n=0;const r={line:0,column:0,defSourceIdx:0,defScopeIdx:0,callsiteSourceIdx:0,callsiteLine:0,callsiteColumn:0};for(;t.hasNext();){if(";"===t.peek()){t.next(),++n;continue}if(","===t.peek()){t.next();continue}if(r.column=t.nextVLQ()+(n===r.line?r.column:0),r.line=n,null===t.peekVLQ()){yield{line:n,column:r.column};continue}const e={line:n,column:r.column,flags:t.nextVLQ(),bindings:[]};if(1&e.flags){const n=t.nextVLQ(),s=t.nextVLQ();r.defScopeIdx=s+(0===n?r.defScopeIdx:0),r.defSourceIdx+=n,e.definition={sourceIdx:r.defSourceIdx,scopeIdx:r.defScopeIdx}}if(2&e.flags){const n=t.nextVLQ(),s=t.nextVLQ(),i=t.nextVLQ();r.callsiteColumn=i+(0===s&&0===n?r.callsiteColumn:0),r.callsiteLine=s+(0===n?r.callsiteLine:0),r.callsiteSourceIdx+=n,e.callsite={sourceIdx:r.callsiteSourceIdx,line:r.callsiteLine,column:r.callsiteColumn}}for(;t.hasNext()&&";"!==t.peek()&&","!==t.peek();){const n=[];e.bindings.push(n);const r=t.nextVLQ();if(r>=-1){n.push({line:e.line,column:e.column,nameIdx:r});continue}n.push({line:e.line,column:e.column,nameIdx:t.nextVLQ()});const s=-r;for(let e=0;e<s-1;++e){const e=t.nextVLQ(),r=t.nextVLQ(),s=t.nextVLQ(),i=n.at(-1)?.line??0,o=n.at(-1)?.column??0;n.push({line:e+i,column:r+(0===e?o:0),nameIdx:s})}}yield e}}(e))if(ln(i)){const e={start:{line:i.line,column:i.column},end:{line:i.line,column:i.column},isScope:Boolean(4&i.flags),values:[],children:[]};if(i.definition){const{scopeIdx:n,sourceIdx:r}=i.definition;if(!t[r])throw new Error("Invalid source index!");const s=t[r].scopeForItemIndex.get(n);if(!s)throw new Error("Invalid original scope index!");e.originalScope=s}if(i.callsite){const{sourceIdx:n,line:r,column:s}=i.callsite;if(!t[n])throw new Error("Invalid source index!");e.callsite={sourceIndex:n,line:r,column:s}}s.set(e,i),r.push(e)}else{const e=r.pop();if(!e)throw new Error('Range items not nested properly: encountered "end" item without "start" item');e.end={line:i.line,column:i.column},an(e,n,s.get(e)?.bindings),r[r.length-1].children.push(e)}if(1!==r.length)throw new Error("Malformed generated range encoding");return r[0].children}function an(e,t,n){void 0!==n&&(e.values=n.map((n=>{if(1===n.length)return dn(n[0].nameIdx,t);const r=n.map((e=>({from:{line:e.line,column:e.column},to:{line:e.line,column:e.column},value:dn(e.nameIdx,t)})));for(let e=1;e<r.length;++e)r[e-1].to={...r[e].from};return r[r.length-1].to={...e.end},r})))}function ln(e){return"flags"in e}function dn(e,t){if(!(void 0===e||e<0))return t[e]}var cn=Object.freeze({__proto__:null,decodeOriginalScopes:rn,decodeGeneratedRanges:on});class hn{#dn;#cn;constructor(e,t){this.#dn=e,this.#cn=t}static parseFromMap(e){if(!e.originalScopes||!e.generatedRanges)throw new Error("Cant create SourceMapScopesInfo without encoded scopes");const t=rn(e.originalScopes,e.names??[]),n=t.map((e=>e.root)),r=on(e.generatedRanges,t,e.names??[]);return new hn(n,r)}findInlinedFunctions(e,t){const n=[],r=this.#hn(e,t);for(let e=r.length-1;e>=0;--e){const t=r[e],s=t.originalScope;if("function"===s?.kind&&(t.isScope||t.callsite)&&(n.push({name:s.name??"",callsite:t.callsite}),t.isScope))break}return n}#hn(e,t){const n=[];return function r(s){for(const i of s)un(i,e,t)&&(n.push(i),r(i.children))}(this.#cn),n}}function un(e,t,n){return!(e.start.line>t||e.start.line===t&&e.start.column>n)&&!(e.end.line<t||e.end.line===t&&e.end.column<=n)}var gn=Object.freeze({__proto__:null,SourceMapScopesInfo:hn});function pn(e){return e.startsWith(")]}")&&(e=e.substring(e.indexOf("\n"))),65279===e.charCodeAt(0)&&(e=e.slice(1)),JSON.parse(e)}class mn{lineNumber;columnNumber;sourceURL;sourceLineNumber;sourceColumnNumber;name;constructor(e,t,n,r,s,i){this.lineNumber=e,this.columnNumber=t,this.sourceURL=n,this.sourceLineNumber=r,this.sourceColumnNumber=s,this.name=i}static compare(e,t){return e.lineNumber!==t.lineNumber?e.lineNumber-t.lineNumber:e.columnNumber-t.columnNumber}}function fn(e,t){return e.lineNumber-t.lineNumber||e.columnNumber-t.columnNumber}class bn{startLineNumber;startColumnNumber;endLineNumber;endColumnNumber;name;children=[];constructor(e,t,n,r,s){this.startLineNumber=e,this.startColumnNumber=t,this.endLineNumber=n,this.endColumnNumber=r,this.name=s}scopeName(){return this.name}start(){return{lineNumber:this.startLineNumber,columnNumber:this.startColumnNumber}}end(){return{lineNumber:this.endLineNumber,columnNumber:this.endColumnNumber}}}const yn=new WeakMap;class In{#un;#gn;#pn;#mn;#fn;#bn;#yn=null;constructor(t,n,r){this.#un=r,this.#gn=t,this.#pn=n,this.#mn=e.ParsedURL.schemeIs(n,"data:")?t:n,this.#fn=null,this.#bn=new Map,"sections"in this.#un&&this.#un.sections.find((e=>"url"in e))&&e.Console.Console.instance().warn(`SourceMap "${n}" contains unsupported "URL" field in one of its sections.`),this.eachSection(this.parseSources.bind(this))}compiledURL(){return this.#gn}url(){return this.#pn}sourceURLs(){return[...this.#bn.keys()]}embeddedContentByURL(e){const t=this.#bn.get(e);return t?t.content:null}hasScopeInfo(){return this.#In(),null!==this.#yn}findEntry(e,t,n){if(this.#In(),n&&null!==this.#yn){const r=this.#yn.findInlinedFunctions(e,t),{callsite:s}=r[n-1];return s?{lineNumber:e,columnNumber:t,sourceURL:this.sourceURLs()[s.sourceIndex],sourceLineNumber:s.line,sourceColumnNumber:s.column,name:void 0}:(console.error("Malformed source map. Expected to have a callsite info for index",n),null)}const r=this.mappings(),i=s.ArrayUtilities.upperBound(r,void 0,((n,r)=>e-r.lineNumber||t-r.columnNumber));return i?r[i-1]:null}findEntryRanges(e,t){const r=this.mappings(),i=s.ArrayUtilities.upperBound(r,void 0,((n,r)=>e-r.lineNumber||t-r.columnNumber));if(!i)return null;const o=i-1,a=r[o].sourceURL;if(!a)return null;const l=i<r.length?r[i].lineNumber:2**31-1,d=i<r.length?r[i].columnNumber:2**31-1,c=new n.TextRange.TextRange(r[o].lineNumber,r[o].columnNumber,l,d),h=this.reversedMappings(a),u=r[o].sourceLineNumber,g=r[o].sourceColumnNumber,p=s.ArrayUtilities.upperBound(h,void 0,((e,t)=>u-r[t].sourceLineNumber||g-r[t].sourceColumnNumber));if(!p)return null;const m=p<h.length?r[h[p]].sourceLineNumber:2**31-1,f=p<h.length?r[h[p]].sourceColumnNumber:2**31-1;return{range:c,sourceRange:new n.TextRange.TextRange(u,g,m,f),sourceURL:a}}sourceLineMapping(e,t,n){const r=this.mappings(),i=this.reversedMappings(e),o=s.ArrayUtilities.lowerBound(i,t,c),a=s.ArrayUtilities.upperBound(i,t,c);if(o>=i.length||r[i[o]].sourceLineNumber!==t)return null;const l=i.slice(o,a);if(!l.length)return null;const d=s.ArrayUtilities.lowerBound(l,n,((e,t)=>e-r[t].sourceColumnNumber));return d>=l.length?r[l[l.length-1]]:r[l[d]];function c(e,t){return e-r[t].sourceLineNumber}}findReverseIndices(e,t,n){const r=this.mappings(),i=this.reversedMappings(e),o=s.ArrayUtilities.upperBound(i,void 0,((e,s)=>t-r[s].sourceLineNumber||n-r[s].sourceColumnNumber));let a=o;for(;a>0&&r[i[a-1]].sourceLineNumber===r[i[o-1]].sourceLineNumber&&r[i[a-1]].sourceColumnNumber===r[i[o-1]].sourceColumnNumber;)--a;return i.slice(a,o)}findReverseEntries(e,t,n){const r=this.mappings();return this.findReverseIndices(e,t,n).map((e=>r[e]))}findReverseRanges(e,t,r){const s=this.mappings(),i=this.findReverseIndices(e,t,r),o=[];for(let e=0;e<i.length;++e){const t=i[e];let r=t+1;for(;e+1<i.length&&r===i[e+1];)++r,++e;const a=s[t].lineNumber,l=s[t].columnNumber,d=r<s.length?s[r].lineNumber:2**31-1,c=r<s.length?s[r].columnNumber:2**31-1;o.push(new n.TextRange.TextRange(a,l,d,c))}return o}mappings(){return this.#In(),this.#fn??[]}reversedMappings(e){return this.#In(),this.#bn.get(e)?.reverseMappings??[]}#In(){if(null===this.#fn){this.#fn=[];try{this.eachSection(this.parseMap.bind(this))}catch(e){console.error("Failed to parse source map",e),this.#fn=[]}this.mappings().sort(mn.compare),this.#vn(this.#fn),this.#un=null}}#vn(e){const t=new Map;for(let n=0;n<e.length;n++){const r=e[n].sourceURL;if(!r)continue;let s=t.get(r);s||(s=[],t.set(r,s)),s.push(n)}for(const[e,r]of t.entries()){const t=this.#bn.get(e);t&&(r.sort(n),t.reverseMappings=r)}function n(t,n){const r=e[t],s=e[n];return r.sourceLineNumber-s.sourceLineNumber||r.sourceColumnNumber-s.sourceColumnNumber||r.lineNumber-s.lineNumber||r.columnNumber-s.columnNumber}}eachSection(e){if(this.#un)if("sections"in this.#un)for(const t of this.#un.sections)"map"in t&&e(t.map,t.offset.line,t.offset.column);else e(this.#un,0,0)}parseSources(t){const n=[],r=t.sourceRoot??"",s=new Set(t.ignoreList??t.x_google_ignoreList);for(let i=0;i<t.sources.length;++i){let o=t.sources[i];e.ParsedURL.ParsedURL.isRelativeURL(o)&&(o=r&&!r.endsWith("/")&&o&&!o.startsWith("/")?r.concat("/",o):r.concat(o));const a=e.ParsedURL.ParsedURL.completeURL(this.#mn,o)||o,l=t.sourcesContent&&t.sourcesContent[i];if(n.push(a),!this.#bn.has(a)){const e=l??null,t=s.has(i);this.#bn.set(a,{content:e,ignoreListHint:t,reverseMappings:null,scopeTree:null})}}yn.set(t,n)}parseMap(e,t,n){let r=0,s=0,i=0,a=0;const l=yn.get(e),d=e.names??[],c=new vn(e.mappings);let h=l&&l[r];for(;;){if(","===c.peek())c.next();else{for(;";"===c.peek();)t+=1,n=0,c.next();if(!c.hasNext())break}if(n+=c.nextVLQ(),!c.hasNext()||this.isSeparator(c.peek())){this.mappings().push(new mn(t,n));continue}const e=c.nextVLQ();e&&(r+=e,l&&(h=l[r])),s+=c.nextVLQ(),i+=c.nextVLQ(),c.hasNext()&&!this.isSeparator(c.peek())?(a+=c.nextVLQ(),this.mappings().push(new mn(t,n,h,s,i,d[a]))):this.mappings().push(new mn(t,n,h,s,i))}o.Runtime.experiments.isEnabled("use-source-map-scopes")&&(this.parseBloombergScopes(e),this.#kn(e))}parseBloombergScopes(e){if(!e.x_com_bloomberg_sourcesFunctionMappings)return;const t=yn.get(e);if(!t)return;const n=e.names??[],r=e.x_com_bloomberg_sourcesFunctionMappings;for(let e=0;e<t?.length;e++){if(!r[e]||!t[e])continue;const s=this.#bn.get(t[e]);if(!s)continue;const i=r[e];let o=0,a=0,l=0,d=0,c=0;const h=new vn(i),u=[];let g=!0;for(;h.hasNext();){if(g)g=!1;else{if(","!==h.peek())return;h.next()}o+=h.nextVLQ(),a+=h.nextVLQ(),l+=h.nextVLQ(),d+=h.nextVLQ(),c+=h.nextVLQ(),u.push(new bn(a,l,d,c,n[o]??"<invalid>"))}s.scopeTree=this.buildScopeTree(u)}}buildScopeTree(e){const t=[];e.sort(((e,t)=>fn(e.start(),t.start())));const n=[];for(const r of e){const e=r.start();for(;n.length>0;){if(!(fn(n[n.length-1].end(),e)<0))break;n.pop()}n.length>0?n[n.length-1].children.push(r):t.push(r),n.push(r)}return t}#kn(e){e.originalScopes&&e.generatedRanges&&(this.#yn=hn.parseFromMap(e))}findScopeEntry(e,t,n){const r=this.#bn.get(e);if(!r||!r.scopeTree)return null;const s={lineNumber:t,columnNumber:n};let i=null;for(;;){const e=(i?.children??r.scopeTree).find((e=>fn(e.start(),s)<=0&&fn(s,e.end())<=0));if(!e)return i;i=e}}isSeparator(e){return","===e||";"===e}reverseMapTextRanges(e,t){const r=this.reversedMappings(e),i=this.mappings();if(0===r.length)return[];let o=s.ArrayUtilities.lowerBound(r,t,(({startLine:e,startColumn:t},n)=>{const{sourceLineNumber:r,sourceColumnNumber:s}=i[n];return e-r||t-s}));for(;o===r.length||o>0&&(i[r[o]].sourceLineNumber>t.startLine||i[r[o]].sourceColumnNumber>t.startColumn);)o--;let a=o+1;for(;a<r.length;++a){const{sourceLineNumber:e,sourceColumnNumber:n}=i[r[a]];if(!(e<t.endLine||e===t.endLine&&n<t.endColumn))break}const l=[];for(let e=o;e<a;++e){const t=r[e],s=t+1,o=n.TextRange.TextRange.createUnboundedFromLocation(i[t].lineNumber,i[t].columnNumber);s<i.length&&(o.endLine=i[s].lineNumber,o.endColumn=i[s].columnNumber),l.push(o)}l.sort(n.TextRange.TextRange.comparator);let d=0;for(let e=1;e<l.length;++e)l[d].immediatelyPrecedes(l[e])?(l[d].endLine=l[e].endLine,l[d].endColumn=l[e].endColumn):l[++d]=l[e];return l.length=d+1,l}mapsOrigin(){const e=this.mappings();if(e.length>0){const t=e[0];return 0===t?.lineNumber||0===t.columnNumber}return!1}hasIgnoreListHint(e){return this.#bn.get(e)?.ignoreListHint??!1}findRanges(e,t){const r=this.mappings(),s=[];if(!r.length)return[];let i=null;0===r[0].lineNumber&&0===r[0].columnNumber||!t?.isStartMatching||(i=n.TextRange.TextRange.createUnboundedFromLocation(0,0),s.push(i));for(const{sourceURL:t,lineNumber:o,columnNumber:a}of r){const r=t&&e(t);i||!r?i&&!r&&(i.endLine=o,i.endColumn=a,i=null):(i=n.TextRange.TextRange.createUnboundedFromLocation(o,a),s.push(i))}return s}compatibleForURL(e,t){return this.embeddedContentByURL(e)===t.embeddedContentByURL(e)&&this.hasIgnoreListHint(e)===t.hasIgnoreListHint(e)}expandCallFrame(e){if(this.#In(),null===this.#yn)return[e];const t=this.#yn.findInlinedFunctions(e.location().lineNumber,e.location().columnNumber),n=[];for(const[r,s]of t.entries())n.push(e.createVirtualCallFrame(r,s.name));return n}}class vn{#Sn;#wn;constructor(e){this.#Sn=e,this.#wn=0}next(){return this.#Sn.charAt(this.#wn++)}nextCharCode(){return this.#Sn.charCodeAt(this.#wn++)}peek(){return this.#Sn.charAt(this.#wn)}hasNext(){return this.#wn<this.#Sn.length}nextVLQ(){let t=0,n=0,r=32;for(;32&r;){if(!this.hasNext())throw new Error("Unexpected end of input while decodling VLQ number!");const s=this.nextCharCode();if(r=e.Base64.BASE64_CODES[s],65!==s&&0===r)throw new Error(`Unexpected char '${String.fromCharCode(s)}' encountered while decoding`);t+=(31&r)<<n,n+=5}const s=1&t;return t>>=1,s?-t:t}peekVLQ(){const e=this.#wn;try{return this.nextVLQ()}catch{return null}finally{this.#wn=e}}}var kn,Sn=Object.freeze({__proto__:null,parseSourceMap:pn,SourceMapEntry:mn,SourceMap:In,TokenIterator:vn});class wn extends e.ObjectWrapper.ObjectWrapper{#Cn;#Rn;#xn;#Tn;#Mn;constructor(e){super(),this.#Cn=e,this.#Rn=!0,this.#Mn=null,this.#xn=new Map,this.#Tn=new Map,z.instance().addEventListener("InspectedURLChanged",this.inspectedURLChanged,this)}setEnabled(e){if(e===this.#Rn)return;const t=[...this.#xn.entries()];for(const[e]of t)this.detachSourceMap(e);this.#Rn=e;for(const[e,{relativeSourceURL:n,relativeSourceMapURL:r}]of t)this.attachSourceMap(e,n,r)}static getBaseUrl(e){for(;e&&e.type()!==B.Frame;)e=e.parentTarget();return e?.inspectedURL()??s.DevToolsPath.EmptyUrlString}static resolveRelativeSourceURL(t,n){return n=e.ParsedURL.ParsedURL.completeURL(wn.getBaseUrl(t),n)??n}inspectedURLChanged(e){if(e.data!==this.#Cn)return;const t=[...this.#xn.entries()];for(const[e,{relativeSourceURL:n,relativeSourceMapURL:r}]of t)this.detachSourceMap(e),this.attachSourceMap(e,n,r)}sourceMapForClient(e){return this.#xn.get(e)?.sourceMap}sourceMapForClientPromise(e){const t=this.#xn.get(e);return t?t.sourceMapPromise:Promise.resolve(void 0)}clientForSourceMap(e){return this.#Tn.get(e)}attachSourceMap(t,n,r){if(this.#xn.has(t))throw new Error("SourceMap is already attached or being attached to client");if(!r)return;let s={relativeSourceURL:n,relativeSourceMapURL:r,sourceMap:void 0,sourceMapPromise:Promise.resolve(void 0)};if(this.#Rn){const i=wn.resolveRelativeSourceURL(this.#Cn,n),o=e.ParsedURL.ParsedURL.completeURL(i,r);if(o)if(this.#Mn&&console.error("Attaching source map may cancel previously attaching source map"),this.#Mn=t,this.dispatchEventToListeners(kn.SourceMapWillAttach,{client:t}),this.#Mn===t){this.#Mn=null;const e=t.createPageResourceLoadInitiator();s.sourceMapPromise=async function(e,t){try{const{content:n}=await en.instance().loadResource(e,t);return pn(n)}catch(t){throw new Error(`Could not load content for ${e}: ${t.message}`,{cause:t})}}(o,e).then((e=>{const n=new In(i,o,e);return this.#xn.get(t)===s&&(s.sourceMap=n,this.#Tn.set(n,t),this.dispatchEventToListeners(kn.SourceMapAttached,{client:t,sourceMap:n})),n}),(()=>{this.#xn.get(t)===s&&this.dispatchEventToListeners(kn.SourceMapFailedToAttach,{client:t})}))}else this.#Mn&&console.error("Cancelling source map attach because another source map is attaching"),s=null,this.dispatchEventToListeners(kn.SourceMapFailedToAttach,{client:t})}s&&this.#xn.set(t,s)}cancelAttachSourceMap(e){e===this.#Mn?this.#Mn=null:this.#Mn?console.error("cancel attach source map requested but a different source map was being attached"):console.error("cancel attach source map requested but no source map was being attached")}detachSourceMap(e){const t=this.#xn.get(e);if(!t)return;if(this.#xn.delete(e),!this.#Rn)return;const{sourceMap:n}=t;n?(this.#Tn.delete(n),this.dispatchEventToListeners(kn.SourceMapDetached,{client:e,sourceMap:n})):this.dispatchEventToListeners(kn.SourceMapFailedToAttach,{client:e})}dispose(){z.instance().removeEventListener("InspectedURLChanged",this.inspectedURLChanged,this)}}!function(e){e.SourceMapWillAttach="SourceMapWillAttach",e.SourceMapFailedToAttach="SourceMapFailedToAttach",e.SourceMapAttached="SourceMapAttached",e.SourceMapDetached="SourceMapDetached"}(kn||(kn={}));var Cn,Rn=Object.freeze({__proto__:null,SourceMapManager:wn,get Events(){return kn}});class xn extends h{agent;#Pn;#Ln;#En;#An;#On;#Dn;#Nn;#Fn;#Bn;#Hn;#Un;#_n;#qn;#Rn;#zn;#jn;#Vn;constructor(t){super(t),this.#Rn=!1,this.#Hn=null,this.#Un=null,this.#Pn=t.model(Or),this.#On=new wn(t),this.agent=t.cssAgent(),this.#Dn=new En(this),this.#An=t.model(Gr),this.#An&&this.#An.addEventListener(Vr.PrimaryPageChanged,this.onPrimaryPageChanged,this),t.registerCSSDispatcher(new Ln(this)),t.suspended()||this.enable(),this.#Bn=new Map,this.#Fn=new Map,this.#En=new Map,this.#zn=!1,this.#Ln=new Map,this.#_n=null,this.#qn=!1,this.#jn=!1,this.#Nn=new e.Throttler.Throttler(Dn),this.#On.setEnabled(e.Settings.Settings.instance().moduleSetting("css-source-maps-enabled").get()),e.Settings.Settings.instance().moduleSetting("css-source-maps-enabled").addChangeListener((e=>this.#On.setEnabled(e.data)))}async colorScheme(){if(!this.#Vn){const e=await(this.domModel()?.target().runtimeAgent().invoke_evaluate({expression:'window.matchMedia && window.matchMedia("(prefers-color-scheme: dark)").matches'}));!e||e.exceptionDetails||e.getError()||(this.#Vn=e.result.value?"dark":"light")}return this.#Vn}headersForSourceURL(e){const t=[];for(const n of this.getStyleSheetIdsForURL(e)){const e=this.styleSheetHeaderForId(n);e&&t.push(e)}return t}createRawLocationsByURL(e,t,n=0){const r=this.headersForSourceURL(e);r.sort((function(e,t){return e.startLine-t.startLine||e.startColumn-t.startColumn||e.id.localeCompare(t.id)}));const i=s.ArrayUtilities.upperBound(r,void 0,((e,r)=>t-r.startLine||n-r.startColumn));if(!i)return[];const o=[],a=r[i-1];for(let e=i-1;e>=0&&r[e].startLine===a.startLine&&r[e].startColumn===a.startColumn;--e)r[e].containsLocation(t,n)&&o.push(new Pn(r[e],t,n));return o}sourceMapManager(){return this.#On}static readableLayerName(e){return e||"<anonymous>"}static trimSourceURL(e){let t=e.lastIndexOf("/*# sourceURL=");if(-1===t&&(t=e.lastIndexOf("/*@ sourceURL="),-1===t))return e;const n=e.lastIndexOf("\n",t);if(-1===n)return e;const r=e.substr(n+1).split("\n",1)[0];return-1===r.search(/[\x20\t]*\/\*[#@] sourceURL=[\x20\t]*([^\s]*)[\x20\t]*\*\/[\x20\t]*$/)?e:e.substr(0,n)+e.substr(n+r.length+1)}domModel(){return this.#Pn}async setStyleText(e,t,n,r){try{await this.ensureOriginalStyleSheetText(e);const{styles:s}=await this.agent.invoke_setStyleTexts({edits:[{styleSheetId:e,range:t.serializeToObject(),text:n}]});if(!s||1!==s.length)return!1;this.#Pn.markUndoableState(!r);const i=new Mn(e,t,n,s[0]);return this.fireStyleSheetChanged(e,i),!0}catch(e){return console.error(e),!1}}async setSelectorText(e,t,n){a.userMetrics.actionTaken(a.UserMetrics.Action.StyleRuleEdited);try{await this.ensureOriginalStyleSheetText(e);const{selectorList:r}=await this.agent.invoke_setRuleSelector({styleSheetId:e,range:t,selector:n});if(!r)return!1;this.#Pn.markUndoableState();const s=new Mn(e,t,n,r);return this.fireStyleSheetChanged(e,s),!0}catch(e){return console.error(e),!1}}async setPropertyRulePropertyName(e,t,n){a.userMetrics.actionTaken(a.UserMetrics.Action.StyleRuleEdited);try{await this.ensureOriginalStyleSheetText(e);const{propertyName:r}=await this.agent.invoke_setPropertyRulePropertyName({styleSheetId:e,range:t,propertyName:n});if(!r)return!1;this.#Pn.markUndoableState();const s=new Mn(e,t,n,r);return this.fireStyleSheetChanged(e,s),!0}catch(e){return console.error(e),!1}}async setKeyframeKey(e,t,n){a.userMetrics.actionTaken(a.UserMetrics.Action.StyleRuleEdited);try{await this.ensureOriginalStyleSheetText(e);const{keyText:r}=await this.agent.invoke_setKeyframeKey({styleSheetId:e,range:t,keyText:n});if(!r)return!1;this.#Pn.markUndoableState();const s=new Mn(e,t,n,r);return this.fireStyleSheetChanged(e,s),!0}catch(e){return console.error(e),!1}}startCoverage(){return this.#zn=!0,this.agent.invoke_startRuleUsageTracking()}async takeCoverageDelta(){const e=await this.agent.invoke_takeCoverageDelta();return{timestamp:e&&e.timestamp||0,coverage:e&&e.coverage||[]}}setLocalFontsEnabled(e){return this.agent.invoke_setLocalFontsEnabled({enabled:e})}async stopCoverage(){this.#zn=!1,await this.agent.invoke_stopRuleUsageTracking()}async getMediaQueries(){const{medias:e}=await this.agent.invoke_getMediaQueries();return e?Xe.parseMediaArrayPayload(this,e):[]}async getRootLayer(e){const{rootLayer:t}=await this.agent.invoke_getLayersForNode({nodeId:e});return t}isEnabled(){return this.#Rn}async enable(){await this.agent.invoke_enable(),this.#Rn=!0,this.#zn&&await this.startCoverage(),this.dispatchEventToListeners(Cn.ModelWasEnabled)}async getMatchedStyles(e){const t=await this.agent.invoke_getMatchedStylesForNode({nodeId:e});if(t.getError())return null;const n=this.#Pn.nodeForId(e);return n?await kt.create({cssModel:this,node:n,inlinePayload:t.inlineStyle||null,attributesPayload:t.attributesStyle||null,matchedPayload:t.matchedCSSRules||[],pseudoPayload:t.pseudoElements||[],inheritedPayload:t.inherited||[],inheritedPseudoPayload:t.inheritedPseudoElements||[],animationsPayload:t.cssKeyframesRules||[],parentLayoutNodeId:t.parentLayoutNodeId,positionTryRules:t.cssPositionTryRules||[],propertyRules:t.cssPropertyRules??[],cssPropertyRegistrations:t.cssPropertyRegistrations??[],fontPaletteValuesRule:t.cssFontPaletteValuesRule}):null}async getClassNames(e){const{classNames:t}=await this.agent.invoke_collectClassNames({styleSheetId:e});return t||[]}async getComputedStyle(e){return this.isEnabled()||await this.enable(),this.#Dn.computedStylePromise(e)}async getBackgroundColors(e){const t=await this.agent.invoke_getBackgroundColors({nodeId:e});return t.getError()?null:{backgroundColors:t.backgroundColors||null,computedFontSize:t.computedFontSize||"",computedFontWeight:t.computedFontWeight||""}}async getPlatformFonts(e){const{fonts:t}=await this.agent.invoke_getPlatformFontsForNode({nodeId:e});return t}allStyleSheets(){const e=[...this.#Bn.values()];return e.sort((function(e,t){return e.sourceURL<t.sourceURL?-1:e.sourceURL>t.sourceURL?1:e.startLine-t.startLine||e.startColumn-t.startColumn})),e}async getInlineStyles(e){const t=await this.agent.invoke_getInlineStylesForNode({nodeId:e});if(t.getError()||!t.inlineStyle)return null;const n=new rt(this,null,t.inlineStyle,tt.Inline),r=t.attributesStyle?new rt(this,null,t.attributesStyle,tt.Attributes):null;return new An(n,r)}forcePseudoState(e,t,n){const r=e.marker(Tn)||[],i=r.includes(t);if(n){if(i)return!1;r.push(t),e.setMarker(Tn,r)}else{if(!i)return!1;s.ArrayUtilities.removeElement(r,t),r.length?e.setMarker(Tn,r):e.setMarker(Tn,null)}return void 0!==e.id&&(this.agent.invoke_forcePseudoState({nodeId:e.id,forcedPseudoClasses:r}),this.dispatchEventToListeners(Cn.PseudoStateForced,{node:e,pseudoClass:t,enable:n}),!0)}pseudoState(e){return e.marker(Tn)||[]}async setMediaText(e,t,n){a.userMetrics.actionTaken(a.UserMetrics.Action.StyleRuleEdited);try{await this.ensureOriginalStyleSheetText(e);const{media:r}=await this.agent.invoke_setMediaText({styleSheetId:e,range:t,text:n});if(!r)return!1;this.#Pn.markUndoableState();const s=new Mn(e,t,n,r);return this.fireStyleSheetChanged(e,s),!0}catch(e){return console.error(e),!1}}async setContainerQueryText(e,t,n){a.userMetrics.actionTaken(a.UserMetrics.Action.StyleRuleEdited);try{await this.ensureOriginalStyleSheetText(e);const{containerQuery:r}=await this.agent.invoke_setContainerQueryText({styleSheetId:e,range:t,text:n});if(!r)return!1;this.#Pn.markUndoableState();const s=new Mn(e,t,n,r);return this.fireStyleSheetChanged(e,s),!0}catch(e){return console.error(e),!1}}async setSupportsText(e,t,n){a.userMetrics.actionTaken(a.UserMetrics.Action.StyleRuleEdited);try{await this.ensureOriginalStyleSheetText(e);const{supports:r}=await this.agent.invoke_setSupportsText({styleSheetId:e,range:t,text:n});if(!r)return!1;this.#Pn.markUndoableState();const s=new Mn(e,t,n,r);return this.fireStyleSheetChanged(e,s),!0}catch(e){return console.error(e),!1}}async setScopeText(e,t,n){a.userMetrics.actionTaken(a.UserMetrics.Action.StyleRuleEdited);try{await this.ensureOriginalStyleSheetText(e);const{scope:r}=await this.agent.invoke_setScopeText({styleSheetId:e,range:t,text:n});if(!r)return!1;this.#Pn.markUndoableState();const s=new Mn(e,t,n,r);return this.fireStyleSheetChanged(e,s),!0}catch(e){return console.error(e),!1}}async addRule(e,t,n){try{await this.ensureOriginalStyleSheetText(e);const{rule:r}=await this.agent.invoke_addRule({styleSheetId:e,ruleText:t,location:n});if(!r)return null;this.#Pn.markUndoableState();const s=new Mn(e,n,t,r);return this.fireStyleSheetChanged(e,s),new dt(this,r)}catch(e){return console.error(e),null}}async requestViaInspectorStylesheet(e){const t=e.frameId()||(this.#An&&this.#An.mainFrame?this.#An.mainFrame.id:null),n=[...this.#Bn.values()].find((e=>e.frameId===t&&e.isViaInspector()));if(n)return n;if(!t)return null;try{const{styleSheetId:e}=await this.agent.invoke_createStyleSheet({frameId:t});return e&&this.#Bn.get(e)||null}catch(e){return console.error(e),null}}mediaQueryResultChanged(){this.#Vn=void 0,this.dispatchEventToListeners(Cn.MediaQueryResultChanged)}fontsUpdated(e){e&&this.#Ln.set(e.src,new he(e)),this.dispatchEventToListeners(Cn.FontsUpdated)}fontFaces(){return[...this.#Ln.values()]}fontFaceForSource(e){return this.#Ln.get(e)}styleSheetHeaderForId(e){return this.#Bn.get(e)||null}styleSheetHeaders(){return[...this.#Bn.values()]}fireStyleSheetChanged(e,t){this.dispatchEventToListeners(Cn.StyleSheetChanged,{styleSheetId:e,edit:t})}ensureOriginalStyleSheetText(e){const t=this.styleSheetHeaderForId(e);if(!t)return Promise.resolve(null);let n=this.#En.get(t);return n||(n=this.getStyleSheetText(t.id),this.#En.set(t,n),this.originalContentRequestedForTest(t)),n}originalContentRequestedForTest(e){}originalStyleSheetText(e){return this.ensureOriginalStyleSheetText(e.id)}getAllStyleSheetHeaders(){return this.#Bn.values()}styleSheetAdded(e){console.assert(!this.#Bn.get(e.styleSheetId)),e.loadingFailed&&(e.hasSourceURL=!1,e.isConstructed=!0,e.isInline=!1,e.isMutable=!1,e.sourceURL="",e.sourceMapURL=void 0);const t=new Lt(this,e);this.#Bn.set(e.styleSheetId,t);const n=t.resourceURL();let r=this.#Fn.get(n);if(r||(r=new Map,this.#Fn.set(n,r)),r){let e=r.get(t.frameId);e||(e=new Set,r.set(t.frameId,e)),e.add(t.id)}this.#On.attachSourceMap(t,t.sourceURL,t.sourceMapURL),this.dispatchEventToListeners(Cn.StyleSheetAdded,t)}styleSheetRemoved(e){const t=this.#Bn.get(e);if(console.assert(Boolean(t)),!t)return;this.#Bn.delete(e);const n=t.resourceURL(),r=this.#Fn.get(n);if(console.assert(Boolean(r),"No frameId to styleSheetId map is available for given style sheet URL."),r){const s=r.get(t.frameId);s&&(s.delete(e),s.size||(r.delete(t.frameId),r.size||this.#Fn.delete(n)))}this.#En.delete(t),this.#On.detachSourceMap(t),this.dispatchEventToListeners(Cn.StyleSheetRemoved,t)}getStyleSheetIdsForURL(e){const t=this.#Fn.get(e);if(!t)return[];const n=[];for(const e of t.values())n.push(...e);return n}async setStyleSheetText(e,t,n){const r=this.#Bn.get(e);if(!r)return"Unknown stylesheet in CSS.setStyleSheetText";t=xn.trimSourceURL(t),r.hasSourceURL&&(t+="\n/*# sourceURL="+r.sourceURL+" */"),await this.ensureOriginalStyleSheetText(e);const s=(await this.agent.invoke_setStyleSheetText({styleSheetId:r.id,text:t})).sourceMapURL;return this.#On.detachSourceMap(r),r.setSourceMapURL(s),this.#On.attachSourceMap(r,r.sourceURL,r.sourceMapURL),null===s?"Error in CSS.setStyleSheetText":(this.#Pn.markUndoableState(!n),this.fireStyleSheetChanged(e),null)}async getStyleSheetText(e){try{const{text:t}=await this.agent.invoke_getStyleSheetText({styleSheetId:e});return t&&xn.trimSourceURL(t)}catch(e){return null}}async onPrimaryPageChanged(e){e.data.frame.backForwardCacheDetails.restoredFromCache?(await this.suspendModel(),await this.resumeModel()):"Activation"!==e.data.type&&(this.resetStyleSheets(),this.resetFontFaces())}resetStyleSheets(){const e=[...this.#Bn.values()];this.#Fn.clear(),this.#Bn.clear();for(const t of e)this.#On.detachSourceMap(t),this.dispatchEventToListeners(Cn.StyleSheetRemoved,t)}resetFontFaces(){this.#Ln.clear()}async suspendModel(){this.#Rn=!1,await this.agent.invoke_disable(),this.resetStyleSheets(),this.resetFontFaces()}async resumeModel(){return this.enable()}setEffectivePropertyValueForNode(e,t,n){this.agent.invoke_setEffectivePropertyValueForNode({nodeId:e,propertyName:t,value:n})}cachedMatchedCascadeForNode(e){if(this.#Hn!==e&&this.discardCachedMatchedCascade(),this.#Hn=e,!this.#Un){if(!e.id)return Promise.resolve(null);this.#Un=this.getMatchedStyles(e.id)}return this.#Un}discardCachedMatchedCascade(){this.#Hn=null,this.#Un=null}createCSSPropertyTracker(e){return new On(this,e)}enableCSSPropertyTracker(e){const t=e.getTrackedProperties();0!==t.length&&(this.agent.invoke_trackComputedStyleUpdates({propertiesToTrack:t}),this.#qn=!0,this.#_n=e,this.pollComputedStyleUpdates())}disableCSSPropertyTracker(){this.#qn=!1,this.#_n=null,this.agent.invoke_trackComputedStyleUpdates({propertiesToTrack:[]})}async pollComputedStyleUpdates(){if(!this.#jn){if(this.#qn){this.#jn=!0;const e=await this.agent.invoke_takeComputedStyleUpdates();if(this.#jn=!1,e.getError()||!e.nodeIds||!this.#qn)return;this.#_n&&this.#_n.dispatchEventToListeners("TrackedCSSPropertiesUpdated",e.nodeIds.map((e=>this.#Pn.nodeForId(e))))}this.#qn&&this.#Nn.schedule(this.pollComputedStyleUpdates.bind(this))}}dispose(){this.disableCSSPropertyTracker(),super.dispose(),this.#On.dispose()}getAgent(){return this.agent}}!function(e){e.FontsUpdated="FontsUpdated",e.MediaQueryResultChanged="MediaQueryResultChanged",e.ModelWasEnabled="ModelWasEnabled",e.PseudoStateForced="PseudoStateForced",e.StyleSheetAdded="StyleSheetAdded",e.StyleSheetChanged="StyleSheetChanged",e.StyleSheetRemoved="StyleSheetRemoved"}(Cn||(Cn={}));const Tn="pseudo-state-marker";class Mn{styleSheetId;oldRange;newRange;newText;payload;constructor(e,t,r,s){this.styleSheetId=e,this.oldRange=t,this.newRange=n.TextRange.TextRange.fromEdit(t,r),this.newText=r,this.payload=s}}class Pn{#_e;styleSheetId;url;lineNumber;columnNumber;constructor(e,t,n){this.#_e=e.cssModel(),this.styleSheetId=e.id,this.url=e.resourceURL(),this.lineNumber=t,this.columnNumber=n||0}cssModel(){return this.#_e}header(){return this.#_e.styleSheetHeaderForId(this.styleSheetId)}}class Ln{#rt;constructor(e){this.#rt=e}mediaQueryResultChanged(){this.#rt.mediaQueryResultChanged()}fontsUpdated({font:e}){this.#rt.fontsUpdated(e)}styleSheetChanged({styleSheetId:e}){this.#rt.fireStyleSheetChanged(e)}styleSheetAdded({header:e}){this.#rt.styleSheetAdded(e)}styleSheetRemoved({styleSheetId:e}){this.#rt.styleSheetRemoved(e)}}class En{#rt;#Wn;constructor(e){this.#rt=e,this.#Wn=new Map}computedStylePromise(e){let t=this.#Wn.get(e);return t||(t=this.#rt.getAgent().invoke_getComputedStyleForNode({nodeId:e}).then((({computedStyle:t})=>{if(this.#Wn.delete(e),!t||!t.length)return null;const n=new Map;for(const e of t)n.set(e.name,e.value);return n})),this.#Wn.set(e,t),t)}}class An{inlineStyle;attributesStyle;constructor(e,t){this.inlineStyle=e,this.attributesStyle=t}}class On extends e.ObjectWrapper.ObjectWrapper{#rt;#Gn;constructor(e,t){super(),this.#rt=e,this.#Gn=t}start(){this.#rt.enableCSSPropertyTracker(this)}stop(){this.#rt.disableCSSPropertyTracker()}getTrackedProperties(){return this.#Gn}}const Dn=1e3;h.register(xn,{capabilities:2,autostart:!0});var Nn=Object.freeze({__proto__:null,CSSModel:xn,get Events(){return Cn},Edit:Mn,CSSLocation:Pn,InlineStyleResult:An,CSSPropertyTracker:On});class Fn extends h{#Kn;#Qn;#$n;#Xn;constructor(e){super(e),e.registerHeapProfilerDispatcher(new Bn(this)),this.#Kn=!1,this.#Qn=e.heapProfilerAgent(),this.#$n=e.model(_n),this.#Xn=0}debuggerModel(){return this.#$n.debuggerModel()}runtimeModel(){return this.#$n}async enable(){this.#Kn||(this.#Kn=!0,await this.#Qn.invoke_enable())}async startSampling(e){if(this.#Xn++)return!1;const t=await this.#Qn.invoke_startSampling({samplingInterval:e||16384});return Boolean(t.getError())}async stopSampling(){if(!this.#Xn)throw new Error("Sampling profiler is not running.");if(--this.#Xn)return this.getSamplingProfile();const e=await this.#Qn.invoke_stopSampling();return e.getError()?null:e.profile}async getSamplingProfile(){const e=await this.#Qn.invoke_getSamplingProfile();return e.getError()?null:e.profile}async collectGarbage(){const e=await this.#Qn.invoke_collectGarbage();return Boolean(e.getError())}async snapshotObjectIdForObjectId(e){const t=await this.#Qn.invoke_getHeapObjectId({objectId:e});return t.getError()?null:t.heapSnapshotObjectId}async objectForSnapshotObjectId(e,t){const n=await this.#Qn.invoke_getObjectByHeapObjectId({objectId:e,objectGroup:t});return n.getError()?null:this.#$n.createRemoteObject(n.result)}async addInspectedHeapObject(e){const t=await this.#Qn.invoke_addInspectedHeapObject({heapObjectId:e});return Boolean(t.getError())}async takeHeapSnapshot(e){const t=await this.#Qn.invoke_takeHeapSnapshot(e);return Boolean(t.getError())}async startTrackingHeapObjects(e){const t=await this.#Qn.invoke_startTrackingHeapObjects({trackAllocations:e});return Boolean(t.getError())}async stopTrackingHeapObjects(e){const t=await this.#Qn.invoke_stopTrackingHeapObjects({reportProgress:e});return Boolean(t.getError())}heapStatsUpdate(e){this.dispatchEventToListeners("HeapStatsUpdate",e)}lastSeenObjectId(e,t){this.dispatchEventToListeners("LastSeenObjectId",{lastSeenObjectId:e,timestamp:t})}addHeapSnapshotChunk(e){this.dispatchEventToListeners("AddHeapSnapshotChunk",e)}reportHeapSnapshotProgress(e,t,n){this.dispatchEventToListeners("ReportHeapSnapshotProgress",{done:e,total:t,finished:n})}resetProfiles(){this.dispatchEventToListeners("ResetProfiles",this)}}class Bn{#Jn;constructor(e){this.#Jn=e}heapStatsUpdate({statsUpdate:e}){this.#Jn.heapStatsUpdate(e)}lastSeenObjectId({lastSeenObjectId:e,timestamp:t}){this.#Jn.lastSeenObjectId(e,t)}addHeapSnapshotChunk({chunk:e}){this.#Jn.addHeapSnapshotChunk(e)}reportHeapSnapshotProgress({done:e,total:t,finished:n}){this.#Jn.reportHeapSnapshotProgress(e,t,n)}resetProfiles(){this.#Jn.resetProfiles()}}h.register(Fn,{capabilities:4,autostart:!1});var Hn,Un=Object.freeze({__proto__:null,HeapProfilerModel:Fn});class _n extends h{agent;#Yn;#Zn;constructor(t){super(t),this.agent=t.runtimeAgent(),this.target().registerRuntimeDispatcher(new qn(this)),this.agent.invoke_enable(),this.#Yn=new Map,this.#Zn=zn.comparator,e.Settings.Settings.instance().moduleSetting("custom-formatters").get()&&this.agent.invoke_setCustomObjectFormatterEnabled({enabled:!0}),e.Settings.Settings.instance().moduleSetting("custom-formatters").addChangeListener(this.customFormattersStateChanged.bind(this))}static isSideEffectFailure(e){const t="exceptionDetails"in e&&e.exceptionDetails;return Boolean(t&&t.exception&&t.exception.description&&t.exception.description.startsWith("EvalError: Possible side-effect in debug-evaluate"))}debuggerModel(){return this.target().model(nr)}heapProfilerModel(){return this.target().model(Fn)}executionContexts(){return[...this.#Yn.values()].sort(this.executionContextComparator())}setExecutionContextComparator(e){this.#Zn=e}executionContextComparator(){return this.#Zn}defaultExecutionContext(){for(const e of this.executionContexts())if(e.isDefault)return e;return null}executionContext(e){return this.#Yn.get(e)||null}executionContextCreated(e){const t=e.auxData||{isDefault:!0},n=new zn(this,e.id,e.uniqueId,e.name,e.origin,t.isDefault,t.frameId);this.#Yn.set(n.id,n),this.dispatchEventToListeners(Hn.ExecutionContextCreated,n)}executionContextDestroyed(e){const t=this.#Yn.get(e);t&&(this.debuggerModel().executionContextDestroyed(t),this.#Yn.delete(e),this.dispatchEventToListeners(Hn.ExecutionContextDestroyed,t))}fireExecutionContextOrderChanged(){this.dispatchEventToListeners(Hn.ExecutionContextOrderChanged,this)}executionContextsCleared(){this.debuggerModel().globalObjectCleared();const e=this.executionContexts();this.#Yn.clear();for(let t=0;t<e.length;++t)this.dispatchEventToListeners(Hn.ExecutionContextDestroyed,e[t])}createRemoteObject(e){return console.assert("object"==typeof e,"Remote object payload should only be an object"),new Ft(this,e.objectId,e.type,e.subtype,e.value,e.unserializableValue,e.description,e.preview,e.customPreview,e.className)}createScopeRemoteObject(e,t){return new Bt(this,e.objectId,t,e.type,e.subtype,e.value,e.unserializableValue,e.description,e.preview)}createRemoteObjectFromPrimitiveValue(e){const t=typeof e;let n;const r=Nt.unserializableDescription(e);return null!==r&&(n=r),void 0!==n&&(e=void 0),new Ft(this,void 0,t,void 0,e,n)}createRemotePropertyFromPrimitiveValue(e,t){return new Ut(e,this.createRemoteObjectFromPrimitiveValue(t))}discardConsoleEntries(){this.agent.invoke_discardConsoleEntries()}releaseObjectGroup(e){this.agent.invoke_releaseObjectGroup({objectGroup:e})}releaseEvaluationResult(e){if("object"in e&&e.object&&e.object.release(),"exceptionDetails"in e&&e.exceptionDetails&&e.exceptionDetails.exception){const t=e.exceptionDetails.exception;this.createRemoteObject({type:t.type,objectId:t.objectId}).release()}}runIfWaitingForDebugger(){this.agent.invoke_runIfWaitingForDebugger()}customFormattersStateChanged({data:e}){this.agent.invoke_setCustomObjectFormatterEnabled({enabled:e})}async compileScript(e,t,n,r){const s=await this.agent.invoke_compileScript({expression:e,sourceURL:t,persistScript:n,executionContextId:r});return s.getError()?(console.error(s.getError()),null):{scriptId:s.scriptId,exceptionDetails:s.exceptionDetails}}async runScript(e,t,n,r,s,i,o,a){const l=await this.agent.invoke_runScript({scriptId:e,executionContextId:t,objectGroup:n,silent:r,includeCommandLineAPI:s,returnByValue:i,generatePreview:o,awaitPromise:a}),d=l.getError();return d?(console.error(d),{error:d}):{object:this.createRemoteObject(l.result),exceptionDetails:l.exceptionDetails}}async queryObjects(e){if(!e.objectId)return{error:"Prototype should be an Object."};const t=await this.agent.invoke_queryObjects({prototypeObjectId:e.objectId,objectGroup:"console"}),n=t.getError();return n?(console.error(n),{error:n}):{objects:this.createRemoteObject(t.objects)}}async isolateId(){const e=await this.agent.invoke_getIsolateId();return e.getError()||!e.id?this.target().id():e.id}async heapUsage(){const e=await this.agent.invoke_getHeapUsage();return e.getError()?null:e}inspectRequested(t,n,r){const s=this.createRemoteObject(t);if(null!==n&&"object"==typeof n){if("copyToClipboard"in n&&Boolean(n.copyToClipboard))return void this.copyRequested(s);if("queryObjects"in n&&n.queryObjects)return void this.queryObjectsRequested(s,r)}s.isNode()?e.Revealer.reveal(s).then(s.release.bind(s)):"function"!==s.type?s.release():zt.objectAsFunction(s).targetFunctionDetails().then((function(t){if(s.release(),!t||!t.location)return;e.Revealer.reveal(t.location)}))}async addBinding(e){return await this.agent.invoke_addBinding(e)}async removeBinding(e){return await this.agent.invoke_removeBinding(e)}bindingCalled(e){this.dispatchEventToListeners(Hn.BindingCalled,e)}copyRequested(t){if(!t.objectId)return void a.InspectorFrontendHost.InspectorFrontendHostInstance.copyText(t.unserializableValue()||t.value);const n=e.Settings.Settings.instance().moduleSetting("text-editor-indent").get();t.callFunctionJSON((function(e){const t=e.subtype,n=e.indent;if("node"===t)return this instanceof Element?this.outerHTML:void 0;if(t&&void 0===this)return String(t);try{return JSON.stringify(this,null,n)}catch(e){return String(this)}}),[{value:{subtype:t.subtype,indent:n}}]).then(a.InspectorFrontendHost.InspectorFrontendHostInstance.copyText.bind(a.InspectorFrontendHost.InspectorFrontendHostInstance))}async queryObjectsRequested(t,n){const r=await this.queryObjects(t);t.release(),"error"in r?e.Console.Console.instance().error(r.error):this.dispatchEventToListeners(Hn.QueryObjectRequested,{objects:r.objects,executionContextId:n})}static simpleTextFromException(e){let t=e.text;if(e.exception&&e.exception.description){let n=e.exception.description;-1!==n.indexOf("\n")&&(n=n.substring(0,n.indexOf("\n"))),t+=" "+n}return t}exceptionThrown(e,t){const n={timestamp:e,details:t};this.dispatchEventToListeners(Hn.ExceptionThrown,n)}exceptionRevoked(e){this.dispatchEventToListeners(Hn.ExceptionRevoked,e)}consoleAPICalled(e,t,n,r,s,i){const o={type:e,args:t,executionContextId:n,timestamp:r,stackTrace:s,context:i};this.dispatchEventToListeners(Hn.ConsoleAPICalled,o)}executionContextIdForScriptId(e){const t=this.debuggerModel().scriptForId(e);return t?t.executionContextId:0}executionContextForStackTrace(e){let t=e;for(;t&&!t.callFrames.length;)t=t.parent||null;return t&&t.callFrames.length?this.executionContextIdForScriptId(t.callFrames[0].scriptId):0}terminateExecution(){return this.agent.invoke_terminateExecution()}async getExceptionDetails(e){const t=await this.agent.invoke_getExceptionDetails({errorObjectId:e});if(!t.getError())return t.exceptionDetails}}!function(e){e.BindingCalled="BindingCalled",e.ExecutionContextCreated="ExecutionContextCreated",e.ExecutionContextDestroyed="ExecutionContextDestroyed",e.ExecutionContextChanged="ExecutionContextChanged",e.ExecutionContextOrderChanged="ExecutionContextOrderChanged",e.ExceptionThrown="ExceptionThrown",e.ExceptionRevoked="ExceptionRevoked",e.ConsoleAPICalled="ConsoleAPICalled",e.QueryObjectRequested="QueryObjectRequested"}(Hn||(Hn={}));class qn{#er;constructor(e){this.#er=e}executionContextCreated({context:e}){this.#er.executionContextCreated(e)}executionContextDestroyed({executionContextId:e}){this.#er.executionContextDestroyed(e)}executionContextsCleared(){this.#er.executionContextsCleared()}exceptionThrown({timestamp:e,exceptionDetails:t}){this.#er.exceptionThrown(e,t)}exceptionRevoked({exceptionId:e}){this.#er.exceptionRevoked(e)}consoleAPICalled({type:e,args:t,executionContextId:n,timestamp:r,stackTrace:s,context:i}){this.#er.consoleAPICalled(e,t,n,r,s,i)}inspectRequested({object:e,hints:t,executionContextId:n}){this.#er.inspectRequested(e,t,n)}bindingCalled(e){this.#er.bindingCalled(e)}}class zn{id;uniqueId;name;#tr;origin;isDefault;runtimeModel;debuggerModel;frameId;constructor(e,t,n,r,s,i,o){this.id=t,this.uniqueId=n,this.name=r,this.#tr=null,this.origin=s,this.isDefault=i,this.runtimeModel=e,this.debuggerModel=e.debuggerModel(),this.frameId=o,this.setLabelInternal("")}target(){return this.runtimeModel.target()}static comparator(e,t){function n(e){return e.parentTarget()?.type()!==B.Frame?5:e.type()===B.Frame?4:e.type()===B.ServiceWorker?3:e.type()===B.Worker||e.type()===B.SharedWorker?2:1}function r(e){let t=e;const n=[];for(;t;)n.push(t),t=t.parentTarget();return n.reverse()}const s=r(e.target()),i=r(t.target());let o,a;for(let e=0;;e++)if(!s[e]||!i[e]||s[e]!==i[e]){o=s[e],a=i[e];break}if(!o&&a)return-1;if(!a&&o)return 1;if(o&&a){const e=n(o)-n(a);return e?-e:o.id().localeCompare(a.id())}return e.isDefault?-1:t.isDefault?1:e.name.localeCompare(t.name)}async evaluate(e,t,n){return this.debuggerModel.selectedCallFrame()?this.debuggerModel.evaluateOnSelectedCallFrame(e):this.evaluateGlobal(e,t,n)}globalObject(e,t){const n={expression:"this",objectGroup:e,includeCommandLineAPI:!1,silent:!0,returnByValue:!1,generatePreview:t};return this.evaluateGlobal(n,!1,!1)}async evaluateGlobal(e,t,n){e.expression||(e.expression="this");const r=await this.runtimeModel.agent.invoke_evaluate({expression:e.expression,objectGroup:e.objectGroup,includeCommandLineAPI:e.includeCommandLineAPI,silent:e.silent,returnByValue:e.returnByValue,generatePreview:e.generatePreview,userGesture:t,awaitPromise:n,throwOnSideEffect:e.throwOnSideEffect,timeout:e.timeout,disableBreaks:e.disableBreaks,replMode:e.replMode,allowUnsafeEvalBlockedByCSP:e.allowUnsafeEvalBlockedByCSP,...this.uniqueId?{uniqueContextId:this.uniqueId}:{contextId:this.id}}),s=r.getError();return s?(console.error(s),{error:s}):{object:this.runtimeModel.createRemoteObject(r.result),exceptionDetails:r.exceptionDetails}}async globalLexicalScopeNames(){const e=await this.runtimeModel.agent.invoke_globalLexicalScopeNames({executionContextId:this.id});return e.getError()?[]:e.names}label(){return this.#tr}setLabel(e){this.setLabelInternal(e),this.runtimeModel.dispatchEventToListeners(Hn.ExecutionContextChanged,this)}setLabelInternal(t){if(t)return void(this.#tr=t);if(this.name)return void(this.#tr=this.name);const n=e.ParsedURL.ParsedURL.fromString(this.origin);this.#tr=n?n.lastPathComponentWithFragment():""}}h.register(_n,{capabilities:4,autostart:!0});var jn=Object.freeze({__proto__:null,RuntimeModel:_n,get Events(){return Hn},ExecutionContext:zn});const Vn={scriptRemovedOrDeleted:"Script removed or deleted.",unableToFetchScriptSource:"Unable to fetch script source."},Wn=r.i18n.registerUIStrings("core/sdk/Script.ts",Vn),Gn=r.i18n.getLocalizedString.bind(void 0,Wn);let Kn=null;class Qn{debuggerModel;scriptId;sourceURL;lineOffset;columnOffset;endLine;endColumn;executionContextId;hash;#nr;#rr;sourceMapURL;debugSymbols;hasSourceURL;contentLength;originStackTrace;#sr;#ir;#or;#ar;isModule;constructor(e,t,n,r,s,i,o,a,l,d,c,h,u,g,p,m,f,b,y,I){this.debuggerModel=e,this.scriptId=t,this.sourceURL=n,this.lineOffset=r,this.columnOffset=s,this.endLine=i,this.endColumn=o,this.isModule=p,this.executionContextId=a,this.hash=l,this.#nr=d,this.#rr=c,this.sourceMapURL=h,this.debugSymbols=y,this.hasSourceURL=u,this.contentLength=g,this.originStackTrace=m,this.#sr=f,this.#ir=b,this.#or=null,this.#ar=I}embedderName(){return this.#ar}target(){return this.debuggerModel.target()}static trimSourceURLComment(e){let t=e.lastIndexOf("//# sourceURL=");if(-1===t&&(t=e.lastIndexOf("//@ sourceURL="),-1===t))return e;const n=e.lastIndexOf("\n",t);if(-1===n)return e;return e.substr(n+1).match(Xn)?e.substr(0,n):e}isContentScript(){return this.#nr}codeOffset(){return this.#sr}isJavaScript(){return"JavaScript"===this.#ir}isWasm(){return"WebAssembly"===this.#ir}scriptLanguage(){return this.#ir}executionContext(){return this.debuggerModel.runtimeModel().executionContext(this.executionContextId)}isLiveEdit(){return this.#rr}contentURL(){return this.sourceURL}contentType(){return e.ResourceType.resourceTypes.Script}async loadTextContent(){const t=await this.debuggerModel.target().debuggerAgent().invoke_getScriptSource({scriptId:this.scriptId});if(t.getError())throw new Error(t.getError());const{scriptSource:r,bytecode:s}=t;if(s)return new n.ContentData.ContentData(s,!0,"application/wasm");let i=r||"";return this.hasSourceURL&&e.ParsedURL.schemeIs(this.sourceURL,"snippet:")&&(i=Qn.trimSourceURLComment(i)),new n.ContentData.ContentData(i,!1,"text/javascript")}async loadWasmContent(){if(!this.isWasm())throw new Error("Not a wasm script");const t=await this.debuggerModel.target().debuggerAgent().invoke_disassembleWasmModule({scriptId:this.scriptId});if(t.getError()){const t=await this.loadTextContent();return await async function(t){const r=e.Worker.WorkerWrapper.fromURL(new URL("../../entrypoints/wasmparser_worker/wasmparser_worker-entrypoint.js",import.meta.url)),s=new Promise(((e,t)=>{r.onmessage=({data:r})=>{if("method"in r&&"disassemble"===r.method)if("error"in r)t(r.error);else if("result"in r){const{lines:t,offsets:s,functionBodyOffsets:i}=r.result;e(new n.WasmDisassembly.WasmDisassembly(t,s,i))}},r.onerror=t}));r.postMessage({method:"disassemble",params:{content:t}});try{return await s}finally{r.terminate()}}(t.base64)}const{streamId:r,functionBodyOffsets:s,chunk:{lines:i,bytecodeOffsets:o}}=t,a=[],l=[];let d=i.reduce(((e,t)=>e+t.length+1),0);const c="<truncated>";if(r)for(;;){const e=await this.debuggerModel.target().debuggerAgent().invoke_nextWasmDisassemblyChunk({streamId:r});if(e.getError())throw new Error(e.getError());const{chunk:{lines:t,bytecodeOffsets:n}}=e;if(d+=t.reduce(((e,t)=>e+t.length+1),0),0===t.length)break;if(d>=999999989){a.push([c]),l.push([0]);break}a.push(t),l.push(n)}const h=[];for(let e=0;e<s.length;e+=2)h.push({start:s[e],end:s[e+1]});return new n.WasmDisassembly.WasmDisassembly(i.concat(...a),o.concat(...l),h)}requestContentData(){if(!this.#or){const e=65535;if(this.hash&&!this.#rr&&this.contentLength>e){Kn||(Kn={cache:new Map,registry:new FinalizationRegistry((e=>Kn?.cache.delete(e)))});const e=[this.#ir,this.contentLength,this.lineOffset,this.columnOffset,this.endLine,this.endColumn,this.#sr,this.hash].join(":"),t=Kn.cache.get(e)?.deref();t?this.#or=t:(this.#or=this.requestContentInternal(),Kn.cache.set(e,new WeakRef(this.#or)),Kn.registry.register(this.#or,e))}else this.#or=this.requestContentInternal()}return this.#or}async requestContent(){const e=await this.requestContentData();return n.ContentData.ContentData.asDeferredContent(e)}async requestContentInternal(){if(!this.scriptId)return{error:Gn(Vn.scriptRemovedOrDeleted)};try{return this.isWasm()?await this.loadWasmContent():await this.loadTextContent()}catch(e){return{error:Gn(Vn.unableToFetchScriptSource)}}}async getWasmBytecode(){const e=await this.debuggerModel.target().debuggerAgent().invoke_getWasmBytecode({scriptId:this.scriptId});return(await fetch(`data:application/wasm;base64,${e.bytecode}`)).arrayBuffer()}originalContentProvider(){return new n.StaticContentProvider.StaticContentProvider(this.contentURL(),this.contentType(),(()=>this.requestContentData()))}async searchInContent(e,t,r){if(!this.scriptId)return[];const s=await this.debuggerModel.target().debuggerAgent().invoke_searchInContent({scriptId:this.scriptId,query:e,caseSensitive:t,isRegex:r});return n.TextUtils.performSearchInSearchMatches(s.result||[],e,t,r)}appendSourceURLCommentIfNeeded(e){return this.hasSourceURL?e+"\n //# sourceURL="+this.sourceURL:e}async editSource(e){e=Qn.trimSourceURLComment(e),e=this.appendSourceURLCommentIfNeeded(e);if(n.ContentData.ContentData.textOr(await this.requestContentData(),null)===e)return{changed:!1,status:"Ok"};const t=await this.debuggerModel.target().debuggerAgent().invoke_setScriptSource({scriptId:this.scriptId,scriptSource:e,allowTopFrameEditing:!0});if(t.getError())throw new Error(`Script#editSource failed for script with id ${this.scriptId}: ${t.getError()}`);return t.getError()||"Ok"!==t.status||(this.#or=Promise.resolve(new n.ContentData.ContentData(e,!1,"text/javascript"))),this.debuggerModel.dispatchEventToListeners(ir.ScriptSourceWasEdited,{script:this,status:t.status}),{changed:!0,status:t.status,exceptionDetails:t.exceptionDetails}}rawLocation(e,t){return this.containsLocation(e,t)?new ar(this.debuggerModel,this.scriptId,e,t):null}isInlineScript(){const e=!this.lineOffset&&!this.columnOffset;return!this.isWasm()&&Boolean(this.sourceURL)&&!e}isAnonymousScript(){return!this.sourceURL}async setBlackboxedRanges(e){return!(await this.debuggerModel.target().debuggerAgent().invoke_setBlackboxedRanges({scriptId:this.scriptId,positions:e})).getError()}containsLocation(e,t){const n=e===this.lineOffset&&t>=this.columnOffset||e>this.lineOffset,r=e<this.endLine||e===this.endLine&&t<=this.endColumn;return n&&r}get frameId(){return"string"!=typeof this[$n]&&(this[$n]=function(e){const t=e.executionContext();if(t)return t.frameId||null;const n=e.debuggerModel.target().model(Gr);if(!n||!n.mainFrame)return null;return n.mainFrame.id}(this)),this[$n]}get isBreakpointCondition(){return[gr,ur].includes(this.sourceURL)}sourceMap(){return this.debuggerModel.sourceMapManager().sourceMapForClient(this)}createPageResourceLoadInitiator(){return{target:this.target(),frameId:this.frameId,initiatorUrl:this.embedderName()}}rawLocationToRelativeLocation(e){let{lineNumber:t,columnNumber:n}=e;return!this.hasSourceURL&&this.isInlineScript()&&(t-=this.lineOffset,0===t&&void 0!==n&&(n-=this.columnOffset)),{lineNumber:t,columnNumber:n}}relativeLocationToRawLocation(e){let{lineNumber:t,columnNumber:n}=e;return!this.hasSourceURL&&this.isInlineScript()&&(0===t&&void 0!==n&&(n+=this.columnOffset),t+=this.lineOffset),{lineNumber:t,columnNumber:n}}}const $n=Symbol("frameid");const Xn=/^[\x20\t]*\/\/[@#] sourceURL=\s*(\S*?)\s*$/;var Jn=Object.freeze({__proto__:null,Script:Qn,sourceURLRegex:Xn});const Yn={local:"Local",closure:"Closure",block:"Block",script:"Script",withBlock:"`With` block",catchBlock:"`Catch` block",global:"Global",module:"Module",expression:"Expression",exception:"Exception",returnValue:"Return value"},Zn=r.i18n.registerUIStrings("core/sdk/DebuggerModel.ts",Yn),er=r.i18n.getLocalizedString.bind(void 0,Zn);function tr(e){function t(e,t){return e.lineNumber-t.lineNumber||e.columnNumber-t.columnNumber}function n(e,n){if(e.scriptId!==n.scriptId)return!1;const r=t(e.start,n.start);return r<0?t(e.end,n.start)>=0:!(r>0)||t(e.start,n.end)<=0}if(0===e.length)return[];e.sort(((e,n)=>e.scriptId<n.scriptId?-1:e.scriptId>n.scriptId?1:t(e.start,n.start)||t(e.end,n.end)));let r=e[0];const s=[];for(let i=1;i<e.length;++i){const o=e[i];n(r,o)?t(r.end,o.end)<=0&&(r={...r,end:o.end}):(s.push(r),r=o)}return s.push(r),s}class nr extends h{agent;runtimeModelInternal;#lr;#dr;#cr;#hr;#ur;continueToLocationCallback;#gr;#pr;#mr;#fr;#br;#yr;#Ir;evaluateOnCallFrameCallback;#vr;#kr=new e.ObjectWrapper.ObjectWrapper;#Sr;#wr;constructor(t){super(t),t.registerDebuggerDispatcher(new or(this)),this.agent=t.debuggerAgent(),this.runtimeModelInternal=t.model(_n),this.#lr=new wn(t),this.#dr=null,this.#cr=new Map,this.#hr=new Map,this.#ur=[],this.continueToLocationCallback=null,this.#gr=null,this.#pr=!1,this.#mr=null,this.#fr=0,this.#br=null,this.#yr=null,this.#Ir=null,this.evaluateOnCallFrameCallback=null,this.#vr=null,this.#Sr=null,this.#wr=!1,e.Settings.Settings.instance().moduleSetting("pause-on-exception-enabled").addChangeListener(this.pauseOnExceptionStateChanged,this),e.Settings.Settings.instance().moduleSetting("pause-on-caught-exception").addChangeListener(this.pauseOnExceptionStateChanged,this),e.Settings.Settings.instance().moduleSetting("pause-on-uncaught-exception").addChangeListener(this.pauseOnExceptionStateChanged,this),e.Settings.Settings.instance().moduleSetting("disable-async-stack-traces").addChangeListener(this.asyncStackTracesStateChanged,this),e.Settings.Settings.instance().moduleSetting("breakpoints-active").addChangeListener(this.breakpointsActiveChanged,this),t.suspended()||this.enableDebugger(),this.#lr.setEnabled(e.Settings.Settings.instance().moduleSetting("js-source-maps-enabled").get()),e.Settings.Settings.instance().moduleSetting("js-source-maps-enabled").addChangeListener((e=>this.#lr.setEnabled(e.data)));const n=t.model(Gr);n&&n.addEventListener(Vr.FrameNavigated,this.onFrameNavigated,this)}sourceMapManager(){return this.#lr}runtimeModel(){return this.runtimeModelInternal}debuggerEnabled(){return Boolean(this.#pr)}debuggerId(){return this.#mr}async enableDebugger(){if(this.#pr)return;this.#pr=!0;const t=o.Runtime.Runtime.queryParam("remoteFrontend")||o.Runtime.Runtime.queryParam("ws")?1e7:1e8,n=this.agent.invoke_enable({maxScriptsCacheSize:t});let r;o.Runtime.experiments.isEnabled("instrumentation-breakpoints")&&(r=this.agent.invoke_setInstrumentationBreakpoint({instrumentation:"beforeScriptExecution"})),this.pauseOnExceptionStateChanged(),this.asyncStackTracesStateChanged(),e.Settings.Settings.instance().moduleSetting("breakpoints-active").get()||this.breakpointsActiveChanged(),this.dispatchEventToListeners(ir.DebuggerWasEnabled,this);const[s]=await Promise.all([n,r]);this.registerDebugger(s)}async syncDebuggerId(){const e=o.Runtime.Runtime.queryParam("remoteFrontend")||o.Runtime.Runtime.queryParam("ws")?1e7:1e8,t=this.agent.invoke_enable({maxScriptsCacheSize:e});return t.then(this.registerDebugger.bind(this)),t}onFrameNavigated(){nr.shouldResyncDebuggerId||(nr.shouldResyncDebuggerId=!0)}registerDebugger(e){if(e.getError())return void(this.#pr=!1);const{debuggerId:t}=e;rr.set(t,this),this.#mr=t,this.dispatchEventToListeners(ir.DebuggerIsReadyToPause,this)}isReadyToPause(){return Boolean(this.#mr)}static async modelForDebuggerId(e){return nr.shouldResyncDebuggerId&&(await nr.resyncDebuggerIdForModels(),nr.shouldResyncDebuggerId=!1),rr.get(e)||null}static async resyncDebuggerIdForModels(){const e=rr.values();for(const t of e)t.debuggerEnabled()&&await t.syncDebuggerId()}async disableDebugger(){this.#pr&&(this.#pr=!1,await this.asyncStackTracesStateChanged(),await this.agent.invoke_disable(),this.#wr=!1,this.globalObjectCleared(),this.dispatchEventToListeners(ir.DebuggerWasDisabled,this),"string"==typeof this.#mr&&rr.delete(this.#mr),this.#mr=null)}skipAllPauses(e){this.#fr&&(clearTimeout(this.#fr),this.#fr=0),this.agent.invoke_setSkipAllPauses({skip:e})}skipAllPausesUntilReloadOrTimeout(e){this.#fr&&clearTimeout(this.#fr),this.agent.invoke_setSkipAllPauses({skip:!0}),this.#fr=window.setTimeout(this.skipAllPauses.bind(this,!1),e)}pauseOnExceptionStateChanged(){const t=e.Settings.Settings.instance().moduleSetting("pause-on-caught-exception").get();let n;const r=e.Settings.Settings.instance().moduleSetting("pause-on-uncaught-exception").get();n=t&&r?"all":t?"caught":r?"uncaught":"none",this.agent.invoke_setPauseOnExceptions({state:n})}asyncStackTracesStateChanged(){const t=!e.Settings.Settings.instance().moduleSetting("disable-async-stack-traces").get()&&this.#pr?32:0;return this.agent.invoke_setAsyncCallStackDepth({maxDepth:t})}breakpointsActiveChanged(){this.agent.invoke_setBreakpointsActive({active:e.Settings.Settings.instance().moduleSetting("breakpoints-active").get()})}setComputeAutoStepRangesCallback(e){this.#yr=e}async computeAutoStepSkipList(e){let t=[];if(this.#yr&&this.#dr&&this.#dr.callFrames.length>0){const[n]=this.#dr.callFrames;t=await this.#yr.call(null,e,n)}return tr(t.map((({start:e,end:t})=>({scriptId:e.scriptId,start:{lineNumber:e.lineNumber,columnNumber:e.columnNumber},end:{lineNumber:t.lineNumber,columnNumber:t.columnNumber}}))))}async stepInto(){const e=await this.computeAutoStepSkipList("StepInto");this.agent.invoke_stepInto({breakOnAsyncCall:!1,skipList:e})}async stepOver(){this.#Sr=this.#dr?.callFrames[0]?.functionLocation()??null;const e=await this.computeAutoStepSkipList("StepOver");this.agent.invoke_stepOver({skipList:e})}async stepOut(){const e=await this.computeAutoStepSkipList("StepOut");0!==e.length?this.agent.invoke_stepOver({skipList:e}):this.agent.invoke_stepOut()}scheduleStepIntoAsync(){this.computeAutoStepSkipList("StepInto").then((e=>{this.agent.invoke_stepInto({breakOnAsyncCall:!0,skipList:e})}))}resume(){this.agent.invoke_resume({terminateOnResume:!1}),this.#wr=!1}pause(){this.#wr=!0,this.skipAllPauses(!1),this.agent.invoke_pause()}async setBreakpointByURL(t,n,r,i){let o;if(this.target().type()===B.Node&&e.ParsedURL.schemeIs(t,"file:")){const n=e.ParsedURL.ParsedURL.urlToRawPathString(t,a.Platform.isWin());o=`${s.StringUtilities.escapeForRegExp(n)}|${s.StringUtilities.escapeForRegExp(t)}`,a.Platform.isWin()&&n.match(/^.:\\/)&&(o=`[${n[0].toUpperCase()}${n[0].toLowerCase()}]`+o.substr(1))}let l=0;const d=this.#hr.get(t)||[];for(let e=0,t=d.length;e<t;++e){const t=d[e];n===t.lineOffset&&(l=l?Math.min(l,t.columnOffset):t.columnOffset)}r=Math.max(r||0,l);const c=await this.agent.invoke_setBreakpointByUrl({lineNumber:n,url:o?void 0:t,urlRegex:o,columnNumber:r,condition:i});if(c.getError())return{locations:[],breakpointId:null};let h=[];return c.locations&&(h=c.locations.map((e=>ar.fromPayload(this,e)))),{locations:h,breakpointId:c.breakpointId}}async setBreakpointInAnonymousScript(e,t,n,r){const s=await this.agent.invoke_setBreakpointByUrl({lineNumber:t,scriptHash:e,columnNumber:n,condition:r});if(s.getError())return{locations:[],breakpointId:null};let i=[];return s.locations&&(i=s.locations.map((e=>ar.fromPayload(this,e)))),{locations:i,breakpointId:s.breakpointId}}async removeBreakpoint(e){await this.agent.invoke_removeBreakpoint({breakpointId:e})}async getPossibleBreakpoints(e,t,n){const r=await this.agent.invoke_getPossibleBreakpoints({start:e.payload(),end:t?t.payload():void 0,restrictToFunction:n});return r.getError()||!r.locations?[]:r.locations.map((e=>lr.fromPayload(this,e)))}async fetchAsyncStackTrace(e){const t=await this.agent.invoke_getStackTrace({stackTraceId:e});return t.getError()?null:t.stackTrace}breakpointResolved(e,t){this.#kr.dispatchEventToListeners(e,ar.fromPayload(this,t))}globalObjectCleared(){this.resetDebuggerPausedDetails(),this.reset(),this.dispatchEventToListeners(ir.GlobalObjectCleared,this)}reset(){for(const e of this.#cr.values())this.#lr.detachSourceMap(e);this.#cr.clear(),this.#hr.clear(),this.#ur=[],this.#Sr=null}scripts(){return Array.from(this.#cr.values())}scriptForId(e){return this.#cr.get(e)||null}scriptsForSourceURL(e){return this.#hr.get(e)||[]}scriptsForExecutionContext(e){const t=[];for(const n of this.#cr.values())n.executionContextId===e.id&&t.push(n);return t}get callFrames(){return this.#dr?this.#dr.callFrames:null}debuggerPausedDetails(){return this.#dr}async setDebuggerPausedDetails(e){return this.#wr=!1,this.#dr=e,!(this.#br&&!await this.#br.call(null,e,this.#Sr))&&(this.#Sr=null,this.dispatchEventToListeners(ir.DebuggerPaused,this),this.setSelectedCallFrame(e.callFrames[0]),!0)}resetDebuggerPausedDetails(){this.#wr=!1,this.#dr=null,this.setSelectedCallFrame(null)}setBeforePausedCallback(e){this.#br=e}setExpandCallFramesCallback(e){this.#Ir=e}setEvaluateOnCallFrameCallback(e){this.evaluateOnCallFrameCallback=e}setSynchronizeBreakpointsCallback(e){this.#vr=e}async pausedScript(t,n,r,s,i,o){if("instrumentation"===n){const e=this.scriptForId(r.scriptId);return this.#vr&&e&&await this.#vr(e),void this.resume()}const a=new hr(this,t,n,r,s,i,o);if(await this.#Cr(a),this.continueToLocationCallback){const e=this.continueToLocationCallback;if(this.continueToLocationCallback=null,e(a))return}await this.setDebuggerPausedDetails(a)?e.EventTarget.fireEvent("DevTools.DebuggerPaused"):this.#Sr?this.stepOver():this.stepInto()}async#Cr(e){if(this.#Ir&&(e.callFrames=await this.#Ir.call(null,e.callFrames)),!o.Runtime.experiments.isEnabled("use-source-map-scopes"))return;const t=[];for(const n of e.callFrames){const e=await this.sourceMapManager().sourceMapForClientPromise(n.script);e?.hasScopeInfo()?t.push(...e.expandCallFrame(n)):t.push(n)}e.callFrames=t}resumedScript(){this.resetDebuggerPausedDetails(),this.dispatchEventToListeners(ir.DebuggerResumed,this)}parsedScriptSource(e,t,n,r,s,i,o,a,l,d,c,h,u,g,p,m,f,b,y,I){const v=this.#cr.get(e);if(v)return v;let k=!1;l&&"isDefault"in l&&(k=!l.isDefault);const S=new Qn(this,e,t,n,r,s,i,o,a,k,d,c,h,g,p,m,f,b,y,I);this.registerScript(S),this.dispatchEventToListeners(ir.ParsedScriptSource,S),S.sourceMapURL&&!u&&this.#lr.attachSourceMap(S,S.sourceURL,S.sourceMapURL);return u&&S.isAnonymousScript()&&(this.#ur.push(S),this.collectDiscardedScripts()),S}setSourceMapURL(e,t){this.#lr.detachSourceMap(e),e.sourceMapURL=t,this.#lr.attachSourceMap(e,e.sourceURL,e.sourceMapURL)}async setDebugInfoURL(e,t){this.#Ir&&this.#dr&&(this.#dr.callFrames=await this.#Ir.call(null,this.#dr.callFrames)),this.dispatchEventToListeners(ir.DebugInfoAttached,e)}executionContextDestroyed(e){for(const t of this.#cr.values())t.executionContextId===e.id&&this.#lr.detachSourceMap(t)}registerScript(e){if(this.#cr.set(e.scriptId,e),e.isAnonymousScript())return;let t=this.#hr.get(e.sourceURL);t||(t=[],this.#hr.set(e.sourceURL,t)),t.unshift(e)}unregisterScript(e){console.assert(e.isAnonymousScript()),this.#cr.delete(e.scriptId)}collectDiscardedScripts(){if(this.#ur.length<1e3)return;const e=this.#ur.splice(0,100);for(const t of e)this.unregisterScript(t),this.dispatchEventToListeners(ir.DiscardedAnonymousScriptSource,t)}createRawLocation(e,t,n,r){return this.createRawLocationByScriptId(e.scriptId,t,n,r)}createRawLocationByURL(e,t,n,r,s){for(const i of this.#hr.get(e)||[]){if(!s){if(i.lineOffset>t||i.lineOffset===t&&void 0!==n&&i.columnOffset>n)continue;if(i.endLine<t||i.endLine===t&&void 0!==n&&i.endColumn<=n)continue}return new ar(this,i.scriptId,t,n,r)}return null}createRawLocationByScriptId(e,t,n,r){return new ar(this,e,t,n,r)}createRawLocationsByStackTrace(e){const t=[];for(let n=e;n;n=n.parent)for(const{scriptId:e,lineNumber:r,columnNumber:s}of n.callFrames)t.push(this.createRawLocationByScriptId(e,r,s));return t}isPaused(){return Boolean(this.debuggerPausedDetails())}isPausing(){return this.#wr}setSelectedCallFrame(e){this.#gr!==e&&(this.#gr=e,this.dispatchEventToListeners(ir.CallFrameSelected,this))}selectedCallFrame(){return this.#gr}async evaluateOnSelectedCallFrame(e){const t=this.selectedCallFrame();if(!t)throw new Error("No call frame selected");return t.evaluate(e)}functionDetailsPromise(e){return e.getAllProperties(!1,!1).then(function(e){if(!e)return null;let t=null;if(e.internalProperties)for(const n of e.internalProperties)"[[FunctionLocation]]"===n.name&&(t=n.value);let n=null;if(e.properties)for(const t of e.properties)"name"===t.name&&t.value&&"string"===t.value.type&&(n=t.value);let r=null;t&&(r=this.createRawLocationByScriptId(t.value.scriptId,t.value.lineNumber,t.value.columnNumber));return{location:r,functionName:n?n.value:""}}.bind(this))}async setVariableValue(e,t,n,r){return(await this.agent.invoke_setVariableValue({scopeNumber:e,variableName:t,newValue:n,callFrameId:r})).getError()}addBreakpointListener(e,t,n){this.#kr.addEventListener(e,t,n)}removeBreakpointListener(e,t,n){this.#kr.removeEventListener(e,t,n)}async setBlackboxPatterns(e){return!(await this.agent.invoke_setBlackboxPatterns({patterns:e})).getError()}dispose(){this.#lr.dispose(),this.#mr&&rr.delete(this.#mr),e.Settings.Settings.instance().moduleSetting("pause-on-exception-enabled").removeChangeListener(this.pauseOnExceptionStateChanged,this),e.Settings.Settings.instance().moduleSetting("pause-on-caught-exception").removeChangeListener(this.pauseOnExceptionStateChanged,this),e.Settings.Settings.instance().moduleSetting("disable-async-stack-traces").removeChangeListener(this.asyncStackTracesStateChanged,this)}async suspendModel(){await this.disableDebugger()}async resumeModel(){await this.enableDebugger()}static shouldResyncDebuggerId=!1;getContinueToLocationCallback(){return this.continueToLocationCallback}getEvaluateOnCallFrameCallback(){return this.evaluateOnCallFrameCallback}}const rr=new Map;var sr,ir;!function(e){e.DontPauseOnExceptions="none",e.PauseOnAllExceptions="all",e.PauseOnCaughtExceptions="caught",e.PauseOnUncaughtExceptions="uncaught"}(sr||(sr={})),function(e){e.DebuggerWasEnabled="DebuggerWasEnabled",e.DebuggerWasDisabled="DebuggerWasDisabled",e.DebuggerPaused="DebuggerPaused",e.DebuggerResumed="DebuggerResumed",e.DebugInfoAttached="DebugInfoAttached",e.ParsedScriptSource="ParsedScriptSource",e.DiscardedAnonymousScriptSource="DiscardedAnonymousScriptSource",e.GlobalObjectCleared="GlobalObjectCleared",e.CallFrameSelected="CallFrameSelected",e.DebuggerIsReadyToPause="DebuggerIsReadyToPause",e.ScriptSourceWasEdited="ScriptSourceWasEdited"}(ir||(ir={}));class or{#Rr;constructor(e){this.#Rr=e}paused({callFrames:e,reason:t,data:n,hitBreakpoints:r,asyncStackTrace:s,asyncStackTraceId:i}){this.#Rr.debuggerEnabled()&&this.#Rr.pausedScript(e,t,n,r||[],s,i)}resumed(){this.#Rr.debuggerEnabled()&&this.#Rr.resumedScript()}scriptParsed({scriptId:e,url:t,startLine:n,startColumn:r,endLine:s,endColumn:i,executionContextId:o,hash:a,executionContextAuxData:l,isLiveEdit:d,sourceMapURL:c,hasSourceURL:h,length:u,isModule:g,stackTrace:p,codeOffset:m,scriptLanguage:f,debugSymbols:b,embedderName:y}){this.#Rr.debuggerEnabled()&&this.#Rr.parsedScriptSource(e,t,n,r,s,i,o,a,l,Boolean(d),c,Boolean(h),!1,u||0,g||null,p||null,m||null,f||null,b||null,y||null)}scriptFailedToParse({scriptId:e,url:t,startLine:n,startColumn:r,endLine:s,endColumn:i,executionContextId:o,hash:a,executionContextAuxData:l,sourceMapURL:d,hasSourceURL:c,length:h,isModule:u,stackTrace:g,codeOffset:p,scriptLanguage:m,embedderName:f}){this.#Rr.debuggerEnabled()&&this.#Rr.parsedScriptSource(e,t,n,r,s,i,o,a,l,!1,d,Boolean(c),!0,h||0,u||null,g||null,p||null,m||null,null,f||null)}breakpointResolved({breakpointId:e,location:t}){this.#Rr.debuggerEnabled()&&this.#Rr.breakpointResolved(e,t)}}class ar{debuggerModel;scriptId;lineNumber;columnNumber;inlineFrameIndex;constructor(e,t,n,r,s){this.debuggerModel=e,this.scriptId=t,this.lineNumber=n,this.columnNumber=r||0,this.inlineFrameIndex=s||0}static fromPayload(e,t,n){return new ar(e,t.scriptId,t.lineNumber,t.columnNumber,n)}payload(){return{scriptId:this.scriptId,lineNumber:this.lineNumber,columnNumber:this.columnNumber}}script(){return this.debuggerModel.scriptForId(this.scriptId)}continueToLocation(e){e&&(this.debuggerModel.continueToLocationCallback=this.paused.bind(this,e)),this.debuggerModel.agent.invoke_continueToLocation({location:this.payload(),targetCallFrames:"current"})}paused(e,t){const n=t.callFrames[0].location();return n.scriptId===this.scriptId&&n.lineNumber===this.lineNumber&&n.columnNumber===this.columnNumber&&(e(),!0)}id(){return this.debuggerModel.target().id()+":"+this.scriptId+":"+this.lineNumber+":"+this.columnNumber}}class lr extends ar{type;constructor(e,t,n,r,s){super(e,t,n,r),s&&(this.type=s)}static fromPayload(e,t){return new lr(e,t.scriptId,t.lineNumber,t.columnNumber,t.type)}}class dr{debuggerModel;script;payload;#xr;#Tr;#Mr;inlineFrameIndex;functionName;#Pr;#Lr;missingDebugInfoDetails;exception;canBeRestarted;constructor(e,t,n,r,s,i=null){this.debuggerModel=e,this.script=t,this.payload=n,this.#xr=ar.fromPayload(e,n.location,r),this.#Tr=[],this.#Mr=null,this.inlineFrameIndex=r||0,this.functionName=s||n.functionName,this.missingDebugInfoDetails=null,this.canBeRestarted=Boolean(n.canBeRestarted),this.exception=i;for(let e=0;e<n.scopeChain.length;++e){const t=new cr(this,e);this.#Tr.push(t),"local"===t.type()&&(this.#Mr=t)}n.functionLocation&&(this.#Pr=ar.fromPayload(e,n.functionLocation)),this.#Lr=n.returnValue?this.debuggerModel.runtimeModel().createRemoteObject(n.returnValue):null}static fromPayloadArray(e,t,n){const r=[];for(let s=0;s<t.length;++s){const i=t[s],o=e.scriptForId(i.location.scriptId);if(o){const t=0===s?n:null;r.push(new dr(e,o,i,void 0,void 0,t))}}return r}createVirtualCallFrame(e,t){return new dr(this.debuggerModel,this.script,this.payload,e,t)}get id(){return this.payload.callFrameId}scopeChain(){return this.#Tr}localScope(){return this.#Mr}thisObject(){return this.payload.this?this.debuggerModel.runtimeModel().createRemoteObject(this.payload.this):null}returnValue(){return this.#Lr}async setReturnValue(e){if(!this.#Lr)return null;const t=await this.debuggerModel.agent.invoke_evaluateOnCallFrame({callFrameId:this.id,expression:e,silent:!0,objectGroup:"backtrace"});if(t.getError()||t.exceptionDetails)return null;return(await this.debuggerModel.agent.invoke_setReturnValue({newValue:t.result})).getError()?null:(this.#Lr=this.debuggerModel.runtimeModel().createRemoteObject(t.result),this.#Lr)}location(){return this.#xr}functionLocation(){return this.#Pr||null}async evaluate(e){const t=this.debuggerModel,n=t.runtimeModel(),r=t.getEvaluateOnCallFrameCallback();if(r){const t=await r(this,e);if(t)return t}const s=await this.debuggerModel.agent.invoke_evaluateOnCallFrame({callFrameId:this.id,expression:e.expression,objectGroup:e.objectGroup,includeCommandLineAPI:e.includeCommandLineAPI,silent:e.silent,returnByValue:e.returnByValue,generatePreview:e.generatePreview,throwOnSideEffect:e.throwOnSideEffect,timeout:e.timeout}),i=s.getError();return i?{error:i}:{object:n.createRemoteObject(s.result),exceptionDetails:s.exceptionDetails}}async restart(){console.assert(this.canBeRestarted,"This frame can not be restarted."),await this.debuggerModel.agent.invoke_restartFrame({callFrameId:this.id,mode:"StepInto"})}getPayload(){return this.payload}}class cr{#Er;#Ar;#g;#h;#Or;#Dr;#Jt;constructor(e,t){this.#Er=e,this.#Ar=e.getPayload().scopeChain[t],this.#g=this.#Ar.type,this.#h=this.#Ar.name,this.#Or=t,this.#Jt=null;const n=this.#Ar.startLocation?ar.fromPayload(e.debuggerModel,this.#Ar.startLocation):null,r=this.#Ar.endLocation?ar.fromPayload(e.debuggerModel,this.#Ar.endLocation):null;n&&r&&n.scriptId===r.scriptId?this.#Dr={start:n,end:r}:this.#Dr=null}callFrame(){return this.#Er}type(){return this.#g}typeName(){switch(this.#g){case"local":return er(Yn.local);case"closure":return er(Yn.closure);case"catch":return er(Yn.catchBlock);case"eval":return r.i18n.lockedString("Eval");case"block":return er(Yn.block);case"script":return er(Yn.script);case"with":return er(Yn.withBlock);case"global":return er(Yn.global);case"module":return er(Yn.module);case"wasm-expression-stack":return er(Yn.expression)}return""}name(){return this.#h}range(){return this.#Dr}object(){if(this.#Jt)return this.#Jt;const e=this.#Er.debuggerModel.runtimeModel(),t="with"!==this.#g&&"global"!==this.#g;return this.#Jt=t?e.createScopeRemoteObject(this.#Ar.object,new Ht(this.#Or,this.#Er.id)):e.createRemoteObject(this.#Ar.object),this.#Jt}description(){return"with"!==this.#g&&"global"!==this.#g?"":this.#Ar.object.description||""}icon(){}extraProperties(){if(0!==this.#Or||"local"!==this.#g||this.#Er.script.isWasm())return[];const e=[],t=this.#Er.exception;t&&e.push(new Ut(er(Yn.exception),t,void 0,void 0,void 0,void 0,void 0,!0));const n=this.#Er.returnValue();return n&&e.push(new Ut(er(Yn.returnValue),n,void 0,void 0,void 0,void 0,void 0,!0,this.#Er.setReturnValue.bind(this.#Er))),e}}class hr{debuggerModel;callFrames;reason;auxData;breakpointIds;asyncStackTrace;asyncStackTraceId;constructor(e,t,n,r,s,i,o){this.debuggerModel=e,this.reason=n,this.auxData=r,this.breakpointIds=s,i&&(this.asyncStackTrace=this.cleanRedundantFrames(i)),this.asyncStackTraceId=o,this.callFrames=dr.fromPayloadArray(e,t,this.exception())}exception(){return"exception"!==this.reason&&"promiseRejection"!==this.reason?null:this.debuggerModel.runtimeModel().createRemoteObject(this.auxData)}cleanRedundantFrames(e){let t=e,n=null;for(;t;)n&&!t.callFrames.length?n.parent=t.parent:n=t,t=t.parent;return e}}h.register(nr,{capabilities:4,autostart:!0});const ur="debugger://logpoint",gr="debugger://breakpoint";var pr=Object.freeze({__proto__:null,sortAndMergeRanges:tr,DebuggerModel:nr,get PauseOnExceptionsState(){return sr},get Events(){return ir},Location:ar,BreakLocation:lr,CallFrame:dr,Scope:cr,DebuggerPausedDetails:hr,LOGPOINT_SOURCE_URL:ur,COND_BREAKPOINT_SOURCE_URL:gr});class mr{#Nr;#Fr;constructor(){const t="rgba";this.#Nr=[new e.Color.Legacy([.9607843137254902,.592156862745098,.5803921568627451,1],t),new e.Color.Legacy([.9411764705882353,.7490196078431373,.2980392156862745,1],t),new e.Color.Legacy([.8313725490196079,.9294117647058824,.19215686274509805,1],t),new e.Color.Legacy([.6196078431372549,.9215686274509803,.2784313725490196,1],t),new e.Color.Legacy([.3568627450980392,.8196078431372549,.8431372549019608,1],t),new e.Color.Legacy([.7372549019607844,.807843137254902,.984313725490196,1],t),new e.Color.Legacy([.7764705882352941,.7450980392156863,.9333333333333333,1],t),new e.Color.Legacy([.8156862745098039,.5803921568627451,.9176470588235294,1],t),new e.Color.Legacy([.9215686274509803,.5803921568627451,.8117647058823529,1],t)],this.#Fr=0}next(){const e=this.#Nr[this.#Fr];return this.#Fr++,this.#Fr>=this.#Nr.length&&(this.#Fr=0),e}}var fr=Object.freeze({__proto__:null,OverlayColorGenerator:mr});class br{#Br;#Nr;#Hr;#Ur;#_r;#qr;#zr;#jr;#Vr;#Wr;#Gr;#Kr;#Qr;#$r;#Xr;constructor(t,n){this.#Br=t,this.#Xr=n,this.#Hr=e.Settings.Settings.instance().createLocalSetting("persistent-highlight-setting",[]),this.#Ur=new Map,this.#_r=new Map,this.#qr=new Map,this.#zr=new Map,this.#jr=new Map,this.#Nr=new Map,this.#Vr=new mr,this.#Wr=new mr,this.#Gr=e.Settings.Settings.instance().moduleSetting("show-grid-line-labels"),this.#Gr.addChangeListener(this.onSettingChange,this),this.#Kr=e.Settings.Settings.instance().moduleSetting("extend-grid-lines"),this.#Kr.addChangeListener(this.onSettingChange,this),this.#Qr=e.Settings.Settings.instance().moduleSetting("show-grid-areas"),this.#Qr.addChangeListener(this.onSettingChange,this),this.#$r=e.Settings.Settings.instance().moduleSetting("show-grid-track-sizes"),this.#$r.addChangeListener(this.onSettingChange,this)}onSettingChange(){this.resetOverlay()}buildGridHighlightConfig(e){const t=this.colorOfGrid(e).asLegacyColor(),n=t.setAlpha(.1).asLegacyColor(),r=t.setAlpha(.3).asLegacyColor(),s=t.setAlpha(.8).asLegacyColor(),i=this.#Kr.get(),o="lineNumbers"===this.#Gr.get(),a=o,l="lineNames"===this.#Gr.get();return{rowGapColor:r.toProtocolRGBA(),rowHatchColor:s.toProtocolRGBA(),columnGapColor:r.toProtocolRGBA(),columnHatchColor:s.toProtocolRGBA(),gridBorderColor:t.toProtocolRGBA(),gridBorderDash:!1,rowLineColor:t.toProtocolRGBA(),columnLineColor:t.toProtocolRGBA(),rowLineDash:!0,columnLineDash:!0,showGridExtensionLines:i,showPositiveLineNumbers:o,showNegativeLineNumbers:a,showLineNames:l,showAreaNames:this.#Qr.get(),showTrackSizes:this.#$r.get(),areaBorderColor:t.toProtocolRGBA(),gridBackgroundColor:n.toProtocolRGBA()}}buildFlexContainerHighlightConfig(e){const t=this.colorOfFlex(e).asLegacyColor();return{containerBorder:{color:t.toProtocolRGBA(),pattern:"dashed"},itemSeparator:{color:t.toProtocolRGBA(),pattern:"dotted"},lineSeparator:{color:t.toProtocolRGBA(),pattern:"dashed"},mainDistributedSpace:{hatchColor:t.toProtocolRGBA()},crossDistributedSpace:{hatchColor:t.toProtocolRGBA()}}}buildScrollSnapContainerHighlightConfig(t){return{snapAreaBorder:{color:e.Color.PageHighlight.GridBorder.toProtocolRGBA(),pattern:"dashed"},snapportBorder:{color:e.Color.PageHighlight.GridBorder.toProtocolRGBA()},scrollMarginColor:e.Color.PageHighlight.Margin.toProtocolRGBA(),scrollPaddingColor:e.Color.PageHighlight.Padding.toProtocolRGBA()}}highlightGridInOverlay(e){this.#Ur.set(e,this.buildGridHighlightConfig(e)),this.updateHighlightsInOverlay(),this.savePersistentHighlightSetting(),this.#Xr.onGridOverlayStateChanged({nodeId:e,enabled:!0})}isGridHighlighted(e){return this.#Ur.has(e)}colorOfGrid(e){let t=this.#Nr.get(e);return t||(t=this.#Vr.next(),this.#Nr.set(e,t)),t}setColorOfGrid(e,t){this.#Nr.set(e,t)}hideGridInOverlay(e){this.#Ur.has(e)&&(this.#Ur.delete(e),this.updateHighlightsInOverlay(),this.savePersistentHighlightSetting(),this.#Xr.onGridOverlayStateChanged({nodeId:e,enabled:!1}))}highlightScrollSnapInOverlay(e){this.#_r.set(e,this.buildScrollSnapContainerHighlightConfig(e)),this.updateHighlightsInOverlay(),this.#Xr.onScrollSnapOverlayStateChanged({nodeId:e,enabled:!0}),this.savePersistentHighlightSetting()}isScrollSnapHighlighted(e){return this.#_r.has(e)}hideScrollSnapInOverlay(e){this.#_r.has(e)&&(this.#_r.delete(e),this.updateHighlightsInOverlay(),this.#Xr.onScrollSnapOverlayStateChanged({nodeId:e,enabled:!1}),this.savePersistentHighlightSetting())}highlightFlexInOverlay(e){this.#qr.set(e,this.buildFlexContainerHighlightConfig(e)),this.updateHighlightsInOverlay(),this.savePersistentHighlightSetting(),this.#Xr.onFlexOverlayStateChanged({nodeId:e,enabled:!0})}isFlexHighlighted(e){return this.#qr.has(e)}colorOfFlex(e){let t=this.#Nr.get(e);return t||(t=this.#Wr.next(),this.#Nr.set(e,t)),t}setColorOfFlex(e,t){this.#Nr.set(e,t)}hideFlexInOverlay(e){this.#qr.has(e)&&(this.#qr.delete(e),this.updateHighlightsInOverlay(),this.savePersistentHighlightSetting(),this.#Xr.onFlexOverlayStateChanged({nodeId:e,enabled:!1}))}highlightContainerQueryInOverlay(e){this.#zr.set(e,this.buildContainerQueryContainerHighlightConfig()),this.updateHighlightsInOverlay(),this.savePersistentHighlightSetting(),this.#Xr.onContainerQueryOverlayStateChanged({nodeId:e,enabled:!0})}hideContainerQueryInOverlay(e){this.#zr.has(e)&&(this.#zr.delete(e),this.updateHighlightsInOverlay(),this.savePersistentHighlightSetting(),this.#Xr.onContainerQueryOverlayStateChanged({nodeId:e,enabled:!1}))}isContainerQueryHighlighted(e){return this.#zr.has(e)}buildContainerQueryContainerHighlightConfig(){return{containerBorder:{color:e.Color.PageHighlight.LayoutLine.toProtocolRGBA(),pattern:"dashed"},descendantBorder:{color:e.Color.PageHighlight.LayoutLine.toProtocolRGBA(),pattern:"dashed"}}}highlightIsolatedElementInOverlay(e){this.#jr.set(e,this.buildIsolationModeHighlightConfig()),this.updateHighlightsInOverlay(),this.savePersistentHighlightSetting()}hideIsolatedElementInOverlay(e){this.#jr.has(e)&&(this.#jr.delete(e),this.updateHighlightsInOverlay(),this.savePersistentHighlightSetting())}isIsolatedElementHighlighted(e){return this.#jr.has(e)}buildIsolationModeHighlightConfig(){return{resizerColor:e.Color.IsolationModeHighlight.Resizer.toProtocolRGBA(),resizerHandleColor:e.Color.IsolationModeHighlight.ResizerHandle.toProtocolRGBA(),maskColor:e.Color.IsolationModeHighlight.Mask.toProtocolRGBA()}}hideAllInOverlayWithoutSave(){this.#qr.clear(),this.#Ur.clear(),this.#_r.clear(),this.#zr.clear(),this.#jr.clear(),this.updateHighlightsInOverlay()}refreshHighlights(){const e=this.updateHighlightsForDeletedNodes(this.#Ur),t=this.updateHighlightsForDeletedNodes(this.#qr),n=this.updateHighlightsForDeletedNodes(this.#_r),r=this.updateHighlightsForDeletedNodes(this.#zr),s=this.updateHighlightsForDeletedNodes(this.#jr);(t||e||n||r||s)&&(this.updateHighlightsInOverlay(),this.savePersistentHighlightSetting())}updateHighlightsForDeletedNodes(e){let t=!1;for(const n of e.keys())null===this.#Br.getDOMModel().nodeForId(n)&&(e.delete(n),t=!0);return t}resetOverlay(){for(const e of this.#Ur.keys())this.#Ur.set(e,this.buildGridHighlightConfig(e));for(const e of this.#qr.keys())this.#qr.set(e,this.buildFlexContainerHighlightConfig(e));for(const e of this.#_r.keys())this.#_r.set(e,this.buildScrollSnapContainerHighlightConfig(e));for(const e of this.#zr.keys())this.#zr.set(e,this.buildContainerQueryContainerHighlightConfig());for(const e of this.#jr.keys())this.#jr.set(e,this.buildIsolationModeHighlightConfig());this.updateHighlightsInOverlay()}updateHighlightsInOverlay(){const e=this.#Ur.size>0||this.#qr.size>0||this.#zr.size>0||this.#jr.size>0;this.#Br.setShowViewportSizeOnResize(!e),this.updateGridHighlightsInOverlay(),this.updateFlexHighlightsInOverlay(),this.updateScrollSnapHighlightsInOverlay(),this.updateContainerQueryHighlightsInOverlay(),this.updateIsolatedElementHighlightsInOverlay()}updateGridHighlightsInOverlay(){const e=this.#Br,t=[];for(const[e,n]of this.#Ur.entries())t.push({nodeId:e,gridHighlightConfig:n});e.target().overlayAgent().invoke_setShowGridOverlays({gridNodeHighlightConfigs:t})}updateFlexHighlightsInOverlay(){const e=this.#Br,t=[];for(const[e,n]of this.#qr.entries())t.push({nodeId:e,flexContainerHighlightConfig:n});e.target().overlayAgent().invoke_setShowFlexOverlays({flexNodeHighlightConfigs:t})}updateScrollSnapHighlightsInOverlay(){const e=this.#Br,t=[];for(const[e,n]of this.#_r.entries())t.push({nodeId:e,scrollSnapContainerHighlightConfig:n});e.target().overlayAgent().invoke_setShowScrollSnapOverlays({scrollSnapHighlightConfigs:t})}updateContainerQueryHighlightsInOverlay(){const e=this.#Br,t=[];for(const[e,n]of this.#zr.entries())t.push({nodeId:e,containerQueryContainerHighlightConfig:n});e.target().overlayAgent().invoke_setShowContainerQueryOverlays({containerQueryHighlightConfigs:t})}updateIsolatedElementHighlightsInOverlay(){const e=this.#Br,t=[];for(const[e,n]of this.#jr.entries())t.push({nodeId:e,isolationModeHighlightConfig:n});e.target().overlayAgent().invoke_setShowIsolatedElements({isolatedElementHighlightConfigs:t})}async restoreHighlightsForDocument(){this.#qr=new Map,this.#Ur=new Map,this.#_r=new Map,this.#zr=new Map,this.#jr=new Map;const e=await this.#Br.getDOMModel().requestDocument(),t=e?e.documentURL:s.DevToolsPath.EmptyUrlString;await Promise.all(this.#Hr.get().map((async e=>{if(e.url===t)return this.#Br.getDOMModel().pushNodeByPathToFrontend(e.path).then((t=>{const n=this.#Br.getDOMModel().nodeForId(t);if(n)switch(e.type){case"GRID":this.#Ur.set(n.id,this.buildGridHighlightConfig(n.id)),this.#Xr.onGridOverlayStateChanged({nodeId:n.id,enabled:!0});break;case"FLEX":this.#qr.set(n.id,this.buildFlexContainerHighlightConfig(n.id)),this.#Xr.onFlexOverlayStateChanged({nodeId:n.id,enabled:!0});break;case"CONTAINER_QUERY":this.#zr.set(n.id,this.buildContainerQueryContainerHighlightConfig()),this.#Xr.onContainerQueryOverlayStateChanged({nodeId:n.id,enabled:!0});break;case"SCROLL_SNAP":this.#_r.set(n.id,this.buildScrollSnapContainerHighlightConfig(n.id)),this.#Xr.onScrollSnapOverlayStateChanged({nodeId:n.id,enabled:!0});break;case"ISOLATED_ELEMENT":this.#jr.set(n.id,this.buildIsolationModeHighlightConfig())}}))}))),this.updateHighlightsInOverlay()}currentUrl(){const e=this.#Br.getDOMModel().existingDocument();return e?e.documentURL:s.DevToolsPath.EmptyUrlString}getPersistentHighlightSettingForOneType(e,t){const n=[];for(const r of e.keys()){const e=this.#Br.getDOMModel().nodeForId(r);e&&n.push({url:this.currentUrl(),path:e.path(),type:t})}return n}savePersistentHighlightSetting(){const e=this.currentUrl(),t=[...this.#Hr.get().filter((t=>t.url!==e)),...this.getPersistentHighlightSettingForOneType(this.#Ur,"GRID"),...this.getPersistentHighlightSettingForOneType(this.#qr,"FLEX"),...this.getPersistentHighlightSettingForOneType(this.#zr,"CONTAINER_QUERY"),...this.getPersistentHighlightSettingForOneType(this.#_r,"SCROLL_SNAP"),...this.getPersistentHighlightSettingForOneType(this.#jr,"ISOLATED_ELEMENT")];this.#Hr.set(t)}}var yr=Object.freeze({__proto__:null,OverlayPersistentHighlighter:br});const Ir={pausedInDebugger:"Paused in debugger"},vr=r.i18n.registerUIStrings("core/sdk/OverlayModel.ts",Ir),kr=r.i18n.getLocalizedString.bind(void 0,vr),Sr={mac:{x:85,y:0,width:185,height:40},linux:{x:0,y:0,width:196,height:34},windows:{x:0,y:0,width:238,height:33}};class wr extends h{#Pn;overlayAgent;#Rr;#Jr;#Yr;#Zr;#es;#ts;#ns;#rs;#ss;#is;#os;#as;#ls;#ds;#cs;#hs;#us;#gs;constructor(t){super(t),this.#Pn=t.model(Or),t.registerOverlayDispatcher(this),this.overlayAgent=t.overlayAgent(),this.#Rr=t.model(nr),this.#Rr&&(e.Settings.Settings.instance().moduleSetting("disable-paused-state-overlay").addChangeListener(this.updatePausedInDebuggerMessage,this),this.#Rr.addEventListener(ir.DebuggerPaused,this.updatePausedInDebuggerMessage,this),this.#Rr.addEventListener(ir.DebuggerResumed,this.updatePausedInDebuggerMessage,this),this.#Rr.addEventListener(ir.GlobalObjectCleared,this.updatePausedInDebuggerMessage,this)),this.#Jr=!1,this.#Yr=null,this.#Zr=new Rr(this),this.#es=this.#Zr,this.#ts=e.Settings.Settings.instance().moduleSetting("show-paint-rects"),this.#ns=e.Settings.Settings.instance().moduleSetting("show-layout-shift-regions"),this.#rs=e.Settings.Settings.instance().moduleSetting("show-ad-highlights"),this.#ss=e.Settings.Settings.instance().moduleSetting("show-debug-borders"),this.#is=e.Settings.Settings.instance().moduleSetting("show-fps-counter"),this.#os=e.Settings.Settings.instance().moduleSetting("show-scroll-bottleneck-rects"),this.#as=e.Settings.Settings.instance().moduleSetting("show-web-vitals"),this.#ls=[],this.#ds=!0,t.suspended()||(this.overlayAgent.invoke_enable(),this.wireAgentToSettings()),this.#cs=new br(this,{onGridOverlayStateChanged:({nodeId:e,enabled:t})=>this.dispatchEventToListeners("PersistentGridOverlayStateChanged",{nodeId:e,enabled:t}),onFlexOverlayStateChanged:({nodeId:e,enabled:t})=>this.dispatchEventToListeners("PersistentFlexContainerOverlayStateChanged",{nodeId:e,enabled:t}),onContainerQueryOverlayStateChanged:({nodeId:e,enabled:t})=>this.dispatchEventToListeners("PersistentContainerQueryOverlayStateChanged",{nodeId:e,enabled:t}),onScrollSnapOverlayStateChanged:({nodeId:e,enabled:t})=>this.dispatchEventToListeners("PersistentScrollSnapOverlayStateChanged",{nodeId:e,enabled:t})}),this.#Pn.addEventListener(Tr.NodeRemoved,(()=>{this.#cs&&this.#cs.refreshHighlights()})),this.#Pn.addEventListener(Tr.DocumentUpdated,(()=>{this.#cs&&(this.#cs.hideAllInOverlayWithoutSave(),t.suspended()||this.#cs.restoreHighlightsForDocument())})),this.#hs=new xr(this),this.#us=!1,this.#gs=new Cr(this.#Pn.cssModel())}static highlightObjectAsDOMNode(e){const t=e.runtimeModel().target().model(Or);t&&t.overlayModel().highlightInOverlay({object:e,selectorList:void 0})}static hideDOMNodeHighlight(){for(const e of z.instance().models(wr))e.delayedHideHighlight(0)}static async muteHighlight(){return Promise.all(z.instance().models(wr).map((e=>e.suspendModel())))}static async unmuteHighlight(){return Promise.all(z.instance().models(wr).map((e=>e.resumeModel())))}static highlightRect(e){for(const t of z.instance().models(wr))t.highlightRect(e)}static clearHighlight(){for(const e of z.instance().models(wr))e.clearHighlight()}getDOMModel(){return this.#Pn}highlightRect({x:e,y:t,width:n,height:r,color:s,outlineColor:i}){const o=s||{r:255,g:0,b:255,a:.3},a=i||{r:255,g:0,b:255,a:.5};return this.overlayAgent.invoke_highlightRect({x:e,y:t,width:n,height:r,color:o,outlineColor:a})}clearHighlight(){return this.overlayAgent.invoke_hideHighlight()}async wireAgentToSettings(){this.#ls=[this.#ts.addChangeListener((()=>this.overlayAgent.invoke_setShowPaintRects({result:this.#ts.get()}))),this.#ns.addChangeListener((()=>this.overlayAgent.invoke_setShowLayoutShiftRegions({result:this.#ns.get()}))),this.#rs.addChangeListener((()=>this.overlayAgent.invoke_setShowAdHighlights({show:this.#rs.get()}))),this.#ss.addChangeListener((()=>this.overlayAgent.invoke_setShowDebugBorders({show:this.#ss.get()}))),this.#is.addChangeListener((()=>this.overlayAgent.invoke_setShowFPSCounter({show:this.#is.get()}))),this.#os.addChangeListener((()=>this.overlayAgent.invoke_setShowScrollBottleneckRects({show:this.#os.get()}))),this.#as.addChangeListener((()=>this.overlayAgent.invoke_setShowWebVitals({show:this.#as.get()})))],this.#ts.get()&&this.overlayAgent.invoke_setShowPaintRects({result:!0}),this.#ns.get()&&this.overlayAgent.invoke_setShowLayoutShiftRegions({result:!0}),this.#rs.get()&&this.overlayAgent.invoke_setShowAdHighlights({show:!0}),this.#ss.get()&&this.overlayAgent.invoke_setShowDebugBorders({show:!0}),this.#is.get()&&this.overlayAgent.invoke_setShowFPSCounter({show:!0}),this.#os.get()&&this.overlayAgent.invoke_setShowScrollBottleneckRects({show:!0}),this.#as.get()&&this.overlayAgent.invoke_setShowWebVitals({show:!0}),this.#Rr&&this.#Rr.isPaused()&&this.updatePausedInDebuggerMessage(),await this.overlayAgent.invoke_setShowViewportSizeOnResize({show:this.#ds}),this.#cs?.resetOverlay()}async suspendModel(){e.EventTarget.removeEventListeners(this.#ls),await this.overlayAgent.invoke_disable()}async resumeModel(){await Promise.all([this.overlayAgent.invoke_enable(),this.wireAgentToSettings()])}setShowViewportSizeOnResize(e){this.#ds!==e&&(this.#ds=e,this.target().suspended()||this.overlayAgent.invoke_setShowViewportSizeOnResize({show:e}))}updatePausedInDebuggerMessage(){if(this.target().suspended())return;const t=this.#Rr&&this.#Rr.isPaused()&&!e.Settings.Settings.instance().moduleSetting("disable-paused-state-overlay").get()?kr(Ir.pausedInDebugger):void 0;this.overlayAgent.invoke_setPausedInDebuggerMessage({message:t})}setHighlighter(e){this.#es=e||this.#Zr}async setInspectMode(e,t=!0){await this.#Pn.requestDocument(),this.#Jr="none"!==e,this.dispatchEventToListeners("InspectModeWillBeToggled",this),this.#es.setInspectMode(e,this.buildHighlightConfig("all",t))}inspectModeEnabled(){return this.#Jr}highlightInOverlay(e,t,n){if(this.#us)return;this.#Yr&&(clearTimeout(this.#Yr),this.#Yr=null);const r=this.buildHighlightConfig(t);void 0!==n&&(r.showInfo=n),this.#es.highlightInOverlay(e,r)}highlightInOverlayForTwoSeconds(e){this.highlightInOverlay(e),this.delayedHideHighlight(2e3)}highlightGridInPersistentOverlay(e){this.#cs&&this.#cs.highlightGridInOverlay(e)}isHighlightedGridInPersistentOverlay(e){return!!this.#cs&&this.#cs.isGridHighlighted(e)}hideGridInPersistentOverlay(e){this.#cs&&this.#cs.hideGridInOverlay(e)}highlightScrollSnapInPersistentOverlay(e){this.#cs&&this.#cs.highlightScrollSnapInOverlay(e)}isHighlightedScrollSnapInPersistentOverlay(e){return!!this.#cs&&this.#cs.isScrollSnapHighlighted(e)}hideScrollSnapInPersistentOverlay(e){this.#cs&&this.#cs.hideScrollSnapInOverlay(e)}highlightFlexContainerInPersistentOverlay(e){this.#cs&&this.#cs.highlightFlexInOverlay(e)}isHighlightedFlexContainerInPersistentOverlay(e){return!!this.#cs&&this.#cs.isFlexHighlighted(e)}hideFlexContainerInPersistentOverlay(e){this.#cs&&this.#cs.hideFlexInOverlay(e)}highlightContainerQueryInPersistentOverlay(e){this.#cs&&this.#cs.highlightContainerQueryInOverlay(e)}isHighlightedContainerQueryInPersistentOverlay(e){return!!this.#cs&&this.#cs.isContainerQueryHighlighted(e)}hideContainerQueryInPersistentOverlay(e){this.#cs&&this.#cs.hideContainerQueryInOverlay(e)}highlightSourceOrderInOverlay(t){const n={parentOutlineColor:e.Color.SourceOrderHighlight.ParentOutline.toProtocolRGBA(),childOutlineColor:e.Color.SourceOrderHighlight.ChildOutline.toProtocolRGBA()};this.#hs.highlightSourceOrderInOverlay(t,n)}colorOfGridInPersistentOverlay(e){return this.#cs?this.#cs.colorOfGrid(e).asString("hex"):null}setColorOfGridInPersistentOverlay(t,n){if(!this.#cs)return;const r=e.Color.parse(n);r&&(this.#cs.setColorOfGrid(t,r),this.#cs.resetOverlay())}colorOfFlexInPersistentOverlay(e){return this.#cs?this.#cs.colorOfFlex(e).asString("hex"):null}setColorOfFlexInPersistentOverlay(t,n){if(!this.#cs)return;const r=e.Color.parse(n);r&&(this.#cs.setColorOfFlex(t,r),this.#cs.resetOverlay())}hideSourceOrderInOverlay(){this.#hs.hideSourceOrderHighlight()}setSourceOrderActive(e){this.#us=e}sourceOrderModeActive(){return this.#us}highlightIsolatedElementInPersistentOverlay(e){this.#cs&&this.#cs.highlightIsolatedElementInOverlay(e)}hideIsolatedElementInPersistentOverlay(e){this.#cs&&this.#cs.hideIsolatedElementInOverlay(e)}isHighlightedIsolatedElementInPersistentOverlay(e){return!!this.#cs&&this.#cs.isIsolatedElementHighlighted(e)}delayedHideHighlight(e){null===this.#Yr&&(this.#Yr=window.setTimeout((()=>this.highlightInOverlay({clear:!0})),e))}highlightFrame(e){this.#Yr&&(clearTimeout(this.#Yr),this.#Yr=null),this.#es.highlightFrame(e)}showHingeForDualScreen(e){if(e){const{x:t,y:n,width:r,height:s,contentColor:i,outlineColor:o}=e;this.overlayAgent.invoke_setShowHinge({hingeConfig:{rect:{x:t,y:n,width:r,height:s},contentColor:i,outlineColor:o}})}else this.overlayAgent.invoke_setShowHinge({})}setWindowControlsPlatform(e){this.#gs.selectedPlatform=e}setWindowControlsThemeColor(e){this.#gs.themeColor=e}getWindowControlsConfig(){return this.#gs.config}async toggleWindowControlsToolbar(e){const t=e?{windowControlsOverlayConfig:this.#gs.config}:{},n=this.overlayAgent.invoke_setShowWindowControlsOverlay(t),r=this.#gs.toggleEmulatedOverlay(e);await Promise.all([n,r]),this.setShowViewportSizeOnResize(!e)}buildHighlightConfig(t="all",n=!1){const r=e.Settings.Settings.instance().moduleSetting("show-metrics-rulers").get(),s={showInfo:"all"===t||"container-outline"===t,showRulers:r,showStyles:n,showAccessibilityInfo:n,showExtensionLines:r,gridHighlightConfig:{},flexContainerHighlightConfig:{},flexItemHighlightConfig:{},contrastAlgorithm:o.Runtime.experiments.isEnabled("apca")?"apca":"aa"};return"all"!==t&&"content"!==t||(s.contentColor=e.Color.PageHighlight.Content.toProtocolRGBA()),"all"!==t&&"padding"!==t||(s.paddingColor=e.Color.PageHighlight.Padding.toProtocolRGBA()),"all"!==t&&"border"!==t||(s.borderColor=e.Color.PageHighlight.Border.toProtocolRGBA()),"all"!==t&&"margin"!==t||(s.marginColor=e.Color.PageHighlight.Margin.toProtocolRGBA()),"all"===t&&(s.eventTargetColor=e.Color.PageHighlight.EventTarget.toProtocolRGBA(),s.shapeColor=e.Color.PageHighlight.Shape.toProtocolRGBA(),s.shapeMarginColor=e.Color.PageHighlight.ShapeMargin.toProtocolRGBA(),s.gridHighlightConfig={rowGapColor:e.Color.PageHighlight.GapBackground.toProtocolRGBA(),rowHatchColor:e.Color.PageHighlight.GapHatch.toProtocolRGBA(),columnGapColor:e.Color.PageHighlight.GapBackground.toProtocolRGBA(),columnHatchColor:e.Color.PageHighlight.GapHatch.toProtocolRGBA(),rowLineColor:e.Color.PageHighlight.LayoutLine.toProtocolRGBA(),columnLineColor:e.Color.PageHighlight.LayoutLine.toProtocolRGBA(),rowLineDash:!0,columnLineDash:!0},s.flexContainerHighlightConfig={containerBorder:{color:e.Color.PageHighlight.LayoutLine.toProtocolRGBA(),pattern:"dashed"},itemSeparator:{color:e.Color.PageHighlight.LayoutLine.toProtocolRGBA(),pattern:"dotted"},lineSeparator:{color:e.Color.PageHighlight.LayoutLine.toProtocolRGBA(),pattern:"dashed"},mainDistributedSpace:{hatchColor:e.Color.PageHighlight.GapHatch.toProtocolRGBA(),fillColor:e.Color.PageHighlight.GapBackground.toProtocolRGBA()},crossDistributedSpace:{hatchColor:e.Color.PageHighlight.GapHatch.toProtocolRGBA(),fillColor:e.Color.PageHighlight.GapBackground.toProtocolRGBA()},rowGapSpace:{hatchColor:e.Color.PageHighlight.GapHatch.toProtocolRGBA(),fillColor:e.Color.PageHighlight.GapBackground.toProtocolRGBA()},columnGapSpace:{hatchColor:e.Color.PageHighlight.GapHatch.toProtocolRGBA(),fillColor:e.Color.PageHighlight.GapBackground.toProtocolRGBA()}},s.flexItemHighlightConfig={baseSizeBox:{hatchColor:e.Color.PageHighlight.GapHatch.toProtocolRGBA()},baseSizeBorder:{color:e.Color.PageHighlight.LayoutLine.toProtocolRGBA(),pattern:"dotted"},flexibilityArrow:{color:e.Color.PageHighlight.LayoutLine.toProtocolRGBA()}}),t.endsWith("gap")&&(s.gridHighlightConfig={gridBorderColor:e.Color.PageHighlight.GridBorder.toProtocolRGBA(),gridBorderDash:!0},"gap"!==t&&"row-gap"!==t||(s.gridHighlightConfig.rowGapColor=e.Color.PageHighlight.GapBackground.toProtocolRGBA(),s.gridHighlightConfig.rowHatchColor=e.Color.PageHighlight.GapHatch.toProtocolRGBA()),"gap"!==t&&"column-gap"!==t||(s.gridHighlightConfig.columnGapColor=e.Color.PageHighlight.GapBackground.toProtocolRGBA(),s.gridHighlightConfig.columnHatchColor=e.Color.PageHighlight.GapHatch.toProtocolRGBA())),t.endsWith("gap")&&(s.flexContainerHighlightConfig={containerBorder:{color:e.Color.PageHighlight.LayoutLine.toProtocolRGBA(),pattern:"dashed"}},"gap"!==t&&"row-gap"!==t||(s.flexContainerHighlightConfig.rowGapSpace={hatchColor:e.Color.PageHighlight.GapHatch.toProtocolRGBA(),fillColor:e.Color.PageHighlight.GapBackground.toProtocolRGBA()}),"gap"!==t&&"column-gap"!==t||(s.flexContainerHighlightConfig.columnGapSpace={hatchColor:e.Color.PageHighlight.GapHatch.toProtocolRGBA(),fillColor:e.Color.PageHighlight.GapBackground.toProtocolRGBA()})),"grid-areas"===t&&(s.gridHighlightConfig={rowLineColor:e.Color.PageHighlight.LayoutLine.toProtocolRGBA(),columnLineColor:e.Color.PageHighlight.LayoutLine.toProtocolRGBA(),rowLineDash:!0,columnLineDash:!0,showAreaNames:!0,areaBorderColor:e.Color.PageHighlight.GridAreaBorder.toProtocolRGBA()}),"grid-template-columns"===t&&(s.contentColor=e.Color.PageHighlight.Content.toProtocolRGBA(),s.gridHighlightConfig={columnLineColor:e.Color.PageHighlight.LayoutLine.toProtocolRGBA(),columnLineDash:!0}),"grid-template-rows"===t&&(s.contentColor=e.Color.PageHighlight.Content.toProtocolRGBA(),s.gridHighlightConfig={rowLineColor:e.Color.PageHighlight.LayoutLine.toProtocolRGBA(),rowLineDash:!0}),"justify-content"===t&&(s.flexContainerHighlightConfig={containerBorder:{color:e.Color.PageHighlight.LayoutLine.toProtocolRGBA(),pattern:"dashed"},mainDistributedSpace:{hatchColor:e.Color.PageHighlight.GapHatch.toProtocolRGBA(),fillColor:e.Color.PageHighlight.GapBackground.toProtocolRGBA()}}),"align-content"===t&&(s.flexContainerHighlightConfig={containerBorder:{color:e.Color.PageHighlight.LayoutLine.toProtocolRGBA(),pattern:"dashed"},crossDistributedSpace:{hatchColor:e.Color.PageHighlight.GapHatch.toProtocolRGBA(),fillColor:e.Color.PageHighlight.GapBackground.toProtocolRGBA()}}),"align-items"===t&&(s.flexContainerHighlightConfig={containerBorder:{color:e.Color.PageHighlight.LayoutLine.toProtocolRGBA(),pattern:"dashed"},lineSeparator:{color:e.Color.PageHighlight.LayoutLine.toProtocolRGBA(),pattern:"dashed"},crossAlignment:{color:e.Color.PageHighlight.LayoutLine.toProtocolRGBA()}}),"flexibility"===t&&(s.flexItemHighlightConfig={baseSizeBox:{hatchColor:e.Color.PageHighlight.GapHatch.toProtocolRGBA()},baseSizeBorder:{color:e.Color.PageHighlight.LayoutLine.toProtocolRGBA(),pattern:"dotted"},flexibilityArrow:{color:e.Color.PageHighlight.LayoutLine.toProtocolRGBA()}}),"container-outline"===t&&(s.containerQueryContainerHighlightConfig={containerBorder:{color:e.Color.PageHighlight.LayoutLine.toProtocolRGBA(),pattern:"dashed"}}),s}nodeHighlightRequested({nodeId:e}){const t=this.#Pn.nodeForId(e);t&&this.dispatchEventToListeners("HighlightNodeRequested",t)}static setInspectNodeHandler(e){wr.inspectNodeHandler=e}inspectNodeRequested({backendNodeId:t}){const n=new Lr(this.target(),t);wr.inspectNodeHandler?n.resolvePromise().then((e=>{e&&wr.inspectNodeHandler&&wr.inspectNodeHandler(e)})):e.Revealer.reveal(n),this.dispatchEventToListeners("InspectModeExited")}screenshotRequested({viewport:e}){this.dispatchEventToListeners("ScreenshotRequested",e),this.dispatchEventToListeners("InspectModeExited")}inspectModeCanceled(){this.dispatchEventToListeners("InspectModeExited")}static inspectNodeHandler=null;getOverlayAgent(){return this.overlayAgent}async hasStyleSheetText(e){return this.#gs.initializeStyleSheetText(e)}}class Cr{#rt;#ps;#ms;#fs;#bs={showCSS:!1,selectedPlatform:"Windows",themeColor:"#ffffff"};constructor(e){this.#rt=e}get selectedPlatform(){return this.#bs.selectedPlatform}set selectedPlatform(e){this.#bs.selectedPlatform=e}get themeColor(){return this.#bs.themeColor}set themeColor(e){this.#bs.themeColor=e}get config(){return this.#bs}async initializeStyleSheetText(e){if(this.#ps&&e===this.#fs)return!0;const t=this.#ys(e);if(!t)return!1;if(this.#ms=this.#Is(t),!this.#ms)return!1;const n=await this.#rt.getStyleSheetText(this.#ms);return!!n&&(this.#ps=n,this.#fs=e,!0)}async toggleEmulatedOverlay(e){if(this.#ms&&this.#ps)if(e){const e=Cr.#vs(this.#bs.selectedPlatform.toLowerCase(),this.#ps);e&&await this.#rt.setStyleSheetText(this.#ms,e,!1)}else await this.#rt.setStyleSheetText(this.#ms,this.#ps,!1)}static#vs(e,t){const n=Sr[e];return Cr.#ks(n.x,n.y,n.width,n.height,t)}#ys(t){const n=e.ParsedURL.ParsedURL.extractOrigin(t),r=this.#rt.styleSheetHeaders().find((e=>e.sourceURL&&e.sourceURL.includes(n)));return r?.sourceURL}#Is(e){const t=this.#rt.getStyleSheetIdsForURL(e);return t.length>0?t[0]:void 0}static#ks(e,t,n,r,s){if(!s)return;return s.replace(/: env\(titlebar-area-x(?:,[^)]*)?\);/g,`: env(titlebar-area-x, ${e}px);`).replace(/: env\(titlebar-area-y(?:,[^)]*)?\);/g,`: env(titlebar-area-y, ${t}px);`).replace(/: env\(titlebar-area-width(?:,[^)]*)?\);/g,`: env(titlebar-area-width, calc(100% - ${n}px));`).replace(/: env\(titlebar-area-height(?:,[^)]*)?\);/g,`: env(titlebar-area-height, ${r}px);`)}transformStyleSheetforTesting(e,t,n,r,s){return Cr.#ks(e,t,n,r,s)}}class Rr{#Br;constructor(e){this.#Br=e}highlightInOverlay(e,t){const{node:n,deferredNode:r,object:s,selectorList:i}={node:void 0,deferredNode:void 0,object:void 0,selectorList:void 0,...e},o=n?n.id:void 0,a=r?r.backendNodeId():void 0,l=s?s.objectId:void 0;o||a||l?this.#Br.target().overlayAgent().invoke_highlightNode({highlightConfig:t,nodeId:o,backendNodeId:a,objectId:l,selector:i}):this.#Br.target().overlayAgent().invoke_hideHighlight()}async setInspectMode(e,t){await this.#Br.target().overlayAgent().invoke_setInspectMode({mode:e,highlightConfig:t})}highlightFrame(t){this.#Br.target().overlayAgent().invoke_highlightFrame({frameId:t,contentColor:e.Color.PageHighlight.Content.toProtocolRGBA(),contentOutlineColor:e.Color.PageHighlight.ContentOutline.toProtocolRGBA()})}}class xr{#Br;constructor(e){this.#Br=e}highlightSourceOrderInOverlay(e,t){this.#Br.setSourceOrderActive(!0),this.#Br.setShowViewportSizeOnResize(!1),this.#Br.getOverlayAgent().invoke_highlightSourceOrder({sourceOrderConfig:t,nodeId:e.id})}hideSourceOrderHighlight(){this.#Br.setSourceOrderActive(!1),this.#Br.setShowViewportSizeOnResize(!0),this.#Br.clearHighlight()}}h.register(wr,{capabilities:2,autostart:!0});var Tr,Mr=Object.freeze({__proto__:null,OverlayModel:wr,WindowControls:Cr,SourceOrderHighlighter:xr});class Pr{#Ss;#ws;ownerDocument;#Cs;id;index;#Rs;#xs;#Ts;#Ms;nodeValueInternal;#Ps;#Ls;#Es;#As;#Os;#Ds;#Ns;#Fs;#Bs;assignedSlot;shadowRootsInternal;#Hs;#Us;#_s;childNodeCountInternal;childrenInternal;nextSibling;previousSibling;firstChild;lastChild;parentNode;templateContentInternal;contentDocumentInternal;childDocumentPromiseForTesting;#qs;publicId;systemId;internalSubset;name;value;constructor(e){this.#Ss=e,this.#ws=this.#Ss.getAgent(),this.index=void 0,this.#Ns=null,this.#Fs=new Map,this.#Bs=[],this.assignedSlot=null,this.shadowRootsInternal=[],this.#Hs=new Map,this.#Us=new Map,this.#_s=0,this.childrenInternal=null,this.nextSibling=null,this.previousSibling=null,this.firstChild=null,this.lastChild=null,this.parentNode=null}static create(e,t,n,r){const s=new Pr(e);return s.init(t,n,r),s}init(e,t,n){if(this.#ws=this.#Ss.getAgent(),this.ownerDocument=e,this.#Cs=t,this.id=n.nodeId,this.#Rs=n.backendNodeId,this.#Ss.registerNode(this),this.#xs=n.nodeType,this.#Ts=n.nodeName,this.#Ms=n.localName,this.nodeValueInternal=n.nodeValue,this.#Ps=n.pseudoType,this.#Ls=n.pseudoIdentifier,this.#Es=n.shadowRootType,this.#As=n.frameId||null,this.#Os=n.xmlVersion,this.#Ds=Boolean(n.isSVG),n.attributes&&this.setAttributesPayload(n.attributes),this.childNodeCountInternal=n.childNodeCount||0,n.shadowRoots)for(let e=0;e<n.shadowRoots.length;++e){const t=n.shadowRoots[e],r=Pr.create(this.#Ss,this.ownerDocument,!0,t);this.shadowRootsInternal.push(r),r.parentNode=this}n.templateContent&&(this.templateContentInternal=Pr.create(this.#Ss,this.ownerDocument,!0,n.templateContent),this.templateContentInternal.parentNode=this,this.childrenInternal=[]);const r=new Set(["EMBED","IFRAME","OBJECT","FENCEDFRAME"]);n.contentDocument?(this.contentDocumentInternal=new Ar(this.#Ss,n.contentDocument),this.contentDocumentInternal.parentNode=this,this.childrenInternal=[]):n.frameId&&r.has(n.nodeName)&&(this.childDocumentPromiseForTesting=this.requestChildDocument(n.frameId,this.#Ss.target()),this.childrenInternal=[]),n.importedDocument&&(this.#qs=Pr.create(this.#Ss,this.ownerDocument,!0,n.importedDocument),this.#qs.parentNode=this,this.childrenInternal=[]),n.distributedNodes&&this.setDistributedNodePayloads(n.distributedNodes),n.assignedSlot&&this.setAssignedSlot(n.assignedSlot),n.children&&this.setChildrenPayload(n.children),this.setPseudoElements(n.pseudoElements),this.#xs===Node.ELEMENT_NODE?(this.ownerDocument&&!this.ownerDocument.documentElement&&"HTML"===this.#Ts&&(this.ownerDocument.documentElement=this),this.ownerDocument&&!this.ownerDocument.body&&"BODY"===this.#Ts&&(this.ownerDocument.body=this)):this.#xs===Node.DOCUMENT_TYPE_NODE?(this.publicId=n.publicId,this.systemId=n.systemId,this.internalSubset=n.internalSubset):this.#xs===Node.ATTRIBUTE_NODE&&(this.name=n.name,this.value=n.value)}async requestChildDocument(e,t){const n=await Ot.instance().getOrWaitForFrame(e,t),r=n.resourceTreeModel()?.target().model(Or);return r?.requestDocument()||null}isAdFrameNode(){if(this.isIframe()&&this.#As){const e=Ot.instance().getFrame(this.#As);return!!e&&"none"!==e.adFrameType()}return!1}isSVGNode(){return this.#Ds}isMediaNode(){return"AUDIO"===this.#Ts||"VIDEO"===this.#Ts}isViewTransitionPseudoNode(){return!!this.#Ps&&["view-transition","view-transition-group","view-transition-image-pair","view-transition-old","view-transition-new"].includes(this.#Ps)}creationStackTrace(){if(this.#Ns)return this.#Ns;const e=this.#ws.invoke_getNodeStackTraces({nodeId:this.id});return this.#Ns=e.then((e=>e.creation||null)),this.#Ns}get subtreeMarkerCount(){return this.#_s}domModel(){return this.#Ss}backendNodeId(){return this.#Rs}children(){return this.childrenInternal?this.childrenInternal.slice():null}setChildren(e){this.childrenInternal=e}hasAttributes(){return this.#Hs.size>0}childNodeCount(){return this.childNodeCountInternal}setChildNodeCount(e){this.childNodeCountInternal=e}hasShadowRoots(){return Boolean(this.shadowRootsInternal.length)}shadowRoots(){return this.shadowRootsInternal.slice()}templateContent(){return this.templateContentInternal||null}contentDocument(){return this.contentDocumentInternal||null}setContentDocument(e){this.contentDocumentInternal=e}isIframe(){return"IFRAME"===this.#Ts}importedDocument(){return this.#qs||null}nodeType(){return this.#xs}nodeName(){return this.#Ts}pseudoType(){return this.#Ps}pseudoIdentifier(){return this.#Ls}hasPseudoElements(){return this.#Fs.size>0}pseudoElements(){return this.#Fs}beforePseudoElement(){return this.#Fs.get("before")?.at(-1)}afterPseudoElement(){return this.#Fs.get("after")?.at(-1)}markerPseudoElement(){return this.#Fs.get("marker")?.at(-1)}backdropPseudoElement(){return this.#Fs.get("backdrop")?.at(-1)}viewTransitionPseudoElements(){return[...this.#Fs.get("view-transition")||[],...this.#Fs.get("view-transition-group")||[],...this.#Fs.get("view-transition-image-pair")||[],...this.#Fs.get("view-transition-old")||[],...this.#Fs.get("view-transition-new")||[]]}hasAssignedSlot(){return null!==this.assignedSlot}isInsertionPoint(){return!this.isXMLNode()&&("SHADOW"===this.#Ts||"CONTENT"===this.#Ts||"SLOT"===this.#Ts)}distributedNodes(){return this.#Bs}isInShadowTree(){return this.#Cs}ancestorShadowHost(){const e=this.ancestorShadowRoot();return e?e.parentNode:null}ancestorShadowRoot(){if(!this.#Cs)return null;let e=this;for(;e&&!e.isShadowRoot();)e=e.parentNode;return e}ancestorUserAgentShadowRoot(){const e=this.ancestorShadowRoot();return e&&e.shadowRootType()===Pr.ShadowRootTypes.UserAgent?e:null}isShadowRoot(){return Boolean(this.#Es)}shadowRootType(){return this.#Es||null}nodeNameInCorrectCase(){const e=this.shadowRootType();return e?"#shadow-root ("+e+")":this.localName()?this.localName().length!==this.nodeName().length?this.nodeName():this.localName():this.nodeName()}setNodeName(e,t){this.#ws.invoke_setNodeName({nodeId:this.id,name:e}).then((e=>{e.getError()||this.#Ss.markUndoableState(),t&&t(e.getError()||null,this.#Ss.nodeForId(e.nodeId))}))}localName(){return this.#Ms}nodeValue(){return this.nodeValueInternal}setNodeValueInternal(e){this.nodeValueInternal=e}setNodeValue(e,t){this.#ws.invoke_setNodeValue({nodeId:this.id,value:e}).then((e=>{e.getError()||this.#Ss.markUndoableState(),t&&t(e.getError()||null)}))}getAttribute(e){const t=this.#Hs.get(e);return t?t.value:void 0}setAttribute(e,t,n){this.#ws.invoke_setAttributesAsText({nodeId:this.id,text:t,name:e}).then((e=>{e.getError()||this.#Ss.markUndoableState(),n&&n(e.getError()||null)}))}setAttributeValue(e,t,n){this.#ws.invoke_setAttributeValue({nodeId:this.id,name:e,value:t}).then((e=>{e.getError()||this.#Ss.markUndoableState(),n&&n(e.getError()||null)}))}setAttributeValuePromise(e,t){return new Promise((n=>this.setAttributeValue(e,t,n)))}attributes(){return[...this.#Hs.values()]}async removeAttribute(e){(await this.#ws.invoke_removeAttribute({nodeId:this.id,name:e})).getError()||(this.#Hs.delete(e),this.#Ss.markUndoableState())}getChildNodes(e){this.childrenInternal?e(this.children()):this.#ws.invoke_requestChildNodes({nodeId:this.id}).then((t=>{e(t.getError()?null:this.children())}))}async getSubtree(e,t){return(await this.#ws.invoke_requestChildNodes({nodeId:this.id,depth:e,pierce:t})).getError()?null:this.childrenInternal}async getOuterHTML(){const{outerHTML:e}=await this.#ws.invoke_getOuterHTML({nodeId:this.id});return e}setOuterHTML(e,t){this.#ws.invoke_setOuterHTML({nodeId:this.id,outerHTML:e}).then((e=>{e.getError()||this.#Ss.markUndoableState(),t&&t(e.getError()||null)}))}removeNode(e){return this.#ws.invoke_removeNode({nodeId:this.id}).then((t=>{t.getError()||this.#Ss.markUndoableState(),e&&e(t.getError()||null)}))}async copyNode(){const{outerHTML:e}=await this.#ws.invoke_getOuterHTML({nodeId:this.id});return null!==e&&a.InspectorFrontendHost.InspectorFrontendHostInstance.copyText(e),e}path(){function e(e){return(void 0!==e.index||e.isShadowRoot()&&e.parentNode)&&e.#Ts.length}const t=[];let n=this;for(;n&&e(n);){const e="number"==typeof n.index?n.index:n.shadowRootType()===Pr.ShadowRootTypes.UserAgent?"u":"a";t.push([e,n.#Ts]),n=n.parentNode}return t.reverse(),t.join(",")}isAncestor(e){if(!e)return!1;let t=e.parentNode;for(;t;){if(this===t)return!0;t=t.parentNode}return!1}isDescendant(e){return null!==e&&e.isAncestor(this)}frameOwnerFrameId(){return this.#As}frameId(){let e=this.parentNode||this;for(;!e.#As&&e.parentNode;)e=e.parentNode;return e.#As}setAttributesPayload(e){let t=!this.#Hs||e.length!==2*this.#Hs.size;const n=this.#Hs||new Map;this.#Hs=new Map;for(let r=0;r<e.length;r+=2){const s=e[r],i=e[r+1];if(this.addAttribute(s,i),t)continue;const o=n.get(s);o&&o.value===i||(t=!0)}return t}insertChild(e,t){if(!this.childrenInternal)throw new Error("DOMNode._children is expected to not be null.");const n=Pr.create(this.#Ss,this.ownerDocument,this.#Cs,t);return this.childrenInternal.splice(e?this.childrenInternal.indexOf(e)+1:0,0,n),this.renumber(),n}removeChild(e){const t=e.pseudoType();if(t){const n=this.#Fs.get(t)?.filter((t=>t!==e));n&&n.length>0?this.#Fs.set(t,n):this.#Fs.delete(t)}else{const t=this.shadowRootsInternal.indexOf(e);if(-1!==t)this.shadowRootsInternal.splice(t,1);else{if(!this.childrenInternal)throw new Error("DOMNode._children is expected to not be null.");if(-1===this.childrenInternal.indexOf(e))throw new Error("DOMNode._children is expected to contain the node to be removed.");this.childrenInternal.splice(this.childrenInternal.indexOf(e),1)}}e.parentNode=null,this.#_s-=e.#_s,e.#_s&&this.#Ss.dispatchEventToListeners(Tr.MarkersChanged,this),this.renumber()}setChildrenPayload(e){this.childrenInternal=[];for(let t=0;t<e.length;++t){const n=e[t],r=Pr.create(this.#Ss,this.ownerDocument,this.#Cs,n);this.childrenInternal.push(r)}this.renumber()}setPseudoElements(e){if(e)for(let t=0;t<e.length;++t){const n=Pr.create(this.#Ss,this.ownerDocument,this.#Cs,e[t]);n.parentNode=this;const r=n.pseudoType();if(!r)throw new Error("DOMNode.pseudoType() is expected to be defined.");const s=this.#Fs.get(r);s?s.push(n):this.#Fs.set(r,[n])}}setDistributedNodePayloads(e){this.#Bs=[];for(const t of e)this.#Bs.push(new Er(this.#Ss.target(),t.backendNodeId,t.nodeType,t.nodeName))}setAssignedSlot(e){this.assignedSlot=new Er(this.#Ss.target(),e.backendNodeId,e.nodeType,e.nodeName)}renumber(){if(!this.childrenInternal)throw new Error("DOMNode._children is expected to not be null.");if(this.childNodeCountInternal=this.childrenInternal.length,0===this.childNodeCountInternal)return this.firstChild=null,void(this.lastChild=null);this.firstChild=this.childrenInternal[0],this.lastChild=this.childrenInternal[this.childNodeCountInternal-1];for(let e=0;e<this.childNodeCountInternal;++e){const t=this.childrenInternal[e];t.index=e,t.nextSibling=e+1<this.childNodeCountInternal?this.childrenInternal[e+1]:null,t.previousSibling=e-1>=0?this.childrenInternal[e-1]:null,t.parentNode=this}}addAttribute(e,t){const n={name:e,value:t,_node:this};this.#Hs.set(e,n)}setAttributeInternal(e,t){const n=this.#Hs.get(e);n?n.value=t:this.addAttribute(e,t)}removeAttributeInternal(e){this.#Hs.delete(e)}copyTo(e,t,n){this.#ws.invoke_copyTo({nodeId:this.id,targetNodeId:e.id,insertBeforeNodeId:t?t.id:void 0}).then((e=>{e.getError()||this.#Ss.markUndoableState(),n&&n(e.getError()||null,this.#Ss.nodeForId(e.nodeId))}))}moveTo(e,t,n){this.#ws.invoke_moveTo({nodeId:this.id,targetNodeId:e.id,insertBeforeNodeId:t?t.id:void 0}).then((e=>{e.getError()||this.#Ss.markUndoableState(),n&&n(e.getError()||null,this.#Ss.nodeForId(e.nodeId))}))}isXMLNode(){return Boolean(this.#Os)}setMarker(e,t){if(null!==t){if(this.parentNode&&!this.#Us.has(e))for(let e=this;e;e=e.parentNode)++e.#_s;this.#Us.set(e,t);for(let e=this;e;e=e.parentNode)this.#Ss.dispatchEventToListeners(Tr.MarkersChanged,e)}else{if(!this.#Us.has(e))return;this.#Us.delete(e);for(let e=this;e;e=e.parentNode)--e.#_s;for(let e=this;e;e=e.parentNode)this.#Ss.dispatchEventToListeners(Tr.MarkersChanged,e)}}marker(e){return this.#Us.get(e)||null}getMarkerKeysForTest(){return[...this.#Us.keys()]}traverseMarkers(e){!function t(n){if(n.#_s){for(const t of n.#Us.keys())e(n,t);if(n.childrenInternal)for(const e of n.childrenInternal)t(e)}}(this)}resolveURL(t){if(!t)return t;for(let n=this;n;n=n.parentNode)if(n instanceof Ar&&n.baseURL)return e.ParsedURL.ParsedURL.completeURL(n.baseURL,t);return null}highlight(e){this.#Ss.overlayModel().highlightInOverlay({node:this,selectorList:void 0},e)}highlightForTwoSeconds(){this.#Ss.overlayModel().highlightInOverlayForTwoSeconds({node:this,selectorList:void 0})}async resolveToObject(e,t){const{object:n}=await this.#ws.invoke_resolveNode({nodeId:this.id,backendNodeId:void 0,executionContextId:t,objectGroup:e});return n&&this.#Ss.runtimeModelInternal.createRemoteObject(n)||null}async boxModel(){const{model:e}=await this.#ws.invoke_getBoxModel({nodeId:this.id});return e}async setAsInspectedNode(){let e=this;for(e&&e.pseudoType()&&(e=e.parentNode);e;){let t=e.ancestorUserAgentShadowRoot();if(!t)break;if(t=e.ancestorShadowHost(),!t)break;e=t}if(!e)throw new Error("In DOMNode.setAsInspectedNode: node is expected to not be null.");await this.#ws.invoke_setInspectedNode({nodeId:e.id})}enclosingElementOrSelf(){let e=this;return e&&e.nodeType()===Node.TEXT_NODE&&e.parentNode&&(e=e.parentNode),e&&e.nodeType()!==Node.ELEMENT_NODE&&(e=null),e}async callFunction(e,t=[]){const n=await this.resolveToObject();if(!n)return null;const r=await n.callFunction(e,t.map((e=>Nt.toCallArgument(e))));return n.release(),r.wasThrown||!r.object?null:{value:r.object.value}}async scrollIntoView(){const e=this.enclosingElementOrSelf();if(!e)return;await e.callFunction((function(){this.scrollIntoViewIfNeeded(!0)}))&&e.highlightForTwoSeconds()}async focus(){const e=this.enclosingElementOrSelf();if(!e)throw new Error("DOMNode.focus expects node to not be null.");await e.callFunction((function(){this.focus()}))&&(e.highlightForTwoSeconds(),await this.#Ss.target().pageAgent().invoke_bringToFront())}simpleSelector(){const e=this.localName()||this.nodeName().toLowerCase();if(this.nodeType()!==Node.ELEMENT_NODE)return e;const t=this.getAttribute("type"),n=this.getAttribute("id"),r=this.getAttribute("class");if("input"===e&&t&&!n&&!r)return e+'[type="'+CSS.escape(t)+'"]';if(n)return e+"#"+CSS.escape(n);if(r){return("div"===e?"":e)+"."+r.trim().split(/\s+/g).map((e=>CSS.escape(e))).join(".")}return this.pseudoIdentifier()?`${e}(${this.pseudoIdentifier()})`:e}async getAnchorBySpecifier(e){const t=await this.#ws.invoke_getAnchorElement({nodeId:this.id,anchorSpecifier:e});return t.getError()?null:this.domModel().nodeForId(t.nodeId)}}!function(e){let t;!function(e){e.UserAgent="user-agent",e.Open="open",e.Closed="closed"}(t=e.ShadowRootTypes||(e.ShadowRootTypes={}))}(Pr||(Pr={}));class Lr{#Ss;#Rs;constructor(e,t){this.#Ss=e.model(Or),this.#Rs=t}resolve(e){this.resolvePromise().then(e)}async resolvePromise(){const e=await this.#Ss.pushNodesByBackendIdsToFrontend(new Set([this.#Rs]));return e&&e.get(this.#Rs)||null}backendNodeId(){return this.#Rs}domModel(){return this.#Ss}highlight(){this.#Ss.overlayModel().highlightInOverlay({deferredNode:this,selectorList:void 0})}}class Er{nodeType;nodeName;deferredNode;constructor(e,t,n,r){this.nodeType=n,this.nodeName=r,this.deferredNode=new Lr(e,t)}}class Ar extends Pr{body;documentElement;documentURL;baseURL;constructor(e,t){super(e),this.body=null,this.documentElement=null,this.init(this,!1,t),this.documentURL=t.documentURL||"",this.baseURL=t.baseURL||""}}class Or extends h{agent;idToDOMNode=new Map;#zs;#js;runtimeModelInternal;#Vs;#Ws;#Gs;#Ks;#Qs;constructor(e){super(e),this.agent=e.domAgent(),this.#zs=null,this.#js=new Set,e.registerDOMDispatcher(new Dr(this)),this.runtimeModelInternal=e.model(_n),this.#Ws=null,e.suspended()||this.agent.invoke_enable({}),o.Runtime.experiments.isEnabled("capture-node-creation-stacks")&&this.agent.invoke_setNodeStackTracesEnabled({enable:!0})}runtimeModel(){return this.runtimeModelInternal}cssModel(){return this.target().model(xn)}overlayModel(){return this.target().model(wr)}static cancelSearch(){for(const e of z.instance().models(Or))e.cancelSearch()}scheduleMutationEvent(e){this.hasEventListeners(Tr.DOMMutated)&&(this.#Vs=(this.#Vs||0)+1,Promise.resolve().then(function(e,t){if(!this.hasEventListeners(Tr.DOMMutated)||this.#Vs!==t)return;this.dispatchEventToListeners(Tr.DOMMutated,e)}.bind(this,e,this.#Vs)))}requestDocument(){return this.#zs?Promise.resolve(this.#zs):(this.#Ws||(this.#Ws=this.requestDocumentInternal()),this.#Ws)}async getOwnerNodeForFrame(e){const t=await this.agent.invoke_getFrameOwner({frameId:e});return t.getError()?null:new Lr(this.target(),t.backendNodeId)}async requestDocumentInternal(){const e=await this.agent.invoke_getDocument({});if(e.getError())return null;const{root:t}=e;if(this.#Ws=null,t&&this.setDocument(t),!this.#zs)return console.error("No document"),null;const n=this.parentModel();if(n&&!this.#Gs){await n.requestDocument();const e=this.target().model(Gr)?.mainFrame;if(e){const t=await n.agent.invoke_getFrameOwner({frameId:e.id});!t.getError()&&t.nodeId&&(this.#Gs=n.nodeForId(t.nodeId))}}if(this.#Gs){const e=this.#Gs.contentDocument();this.#Gs.setContentDocument(this.#zs),this.#Gs.setChildren([]),this.#zs?(this.#zs.parentNode=this.#Gs,this.dispatchEventToListeners(Tr.NodeInserted,this.#zs)):e&&this.dispatchEventToListeners(Tr.NodeRemoved,{node:e,parent:this.#Gs})}return this.#zs}existingDocument(){return this.#zs}async pushNodeToFrontend(e){await this.requestDocument();const{nodeId:t}=await this.agent.invoke_requestNode({objectId:e});return t?this.nodeForId(t):null}pushNodeByPathToFrontend(e){return this.requestDocument().then((()=>this.agent.invoke_pushNodeByPathToFrontend({path:e}))).then((({nodeId:e})=>e))}async pushNodesByBackendIdsToFrontend(e){await this.requestDocument();const t=[...e],{nodeIds:n}=await this.agent.invoke_pushNodesByBackendIdsToFrontend({backendNodeIds:t});if(!n)return null;const r=new Map;for(let e=0;e<n.length;++e)n[e]&&r.set(t[e],this.nodeForId(n[e]));return r}attributeModified(e,t,n){const r=this.idToDOMNode.get(e);r&&(r.setAttributeInternal(t,n),this.dispatchEventToListeners(Tr.AttrModified,{node:r,name:t}),this.scheduleMutationEvent(r))}attributeRemoved(e,t){const n=this.idToDOMNode.get(e);n&&(n.removeAttributeInternal(t),this.dispatchEventToListeners(Tr.AttrRemoved,{node:n,name:t}),this.scheduleMutationEvent(n))}inlineStyleInvalidated(e){e.forEach((e=>this.#js.add(e))),this.#Ks||(this.#Ks=window.setTimeout(this.loadNodeAttributes.bind(this),20))}loadNodeAttributes(){this.#Ks=void 0;for(const e of this.#js)this.agent.invoke_getAttributes({nodeId:e}).then((({attributes:t})=>{if(!t)return;const n=this.idToDOMNode.get(e);n&&n.setAttributesPayload(t)&&(this.dispatchEventToListeners(Tr.AttrModified,{node:n,name:"style"}),this.scheduleMutationEvent(n))}));this.#js.clear()}characterDataModified(e,t){const n=this.idToDOMNode.get(e);n?(n.setNodeValueInternal(t),this.dispatchEventToListeners(Tr.CharacterDataModified,n),this.scheduleMutationEvent(n)):console.error("nodeId could not be resolved to a node")}nodeForId(e){return e&&this.idToDOMNode.get(e)||null}documentUpdated(){const e=Boolean(this.#zs);this.setDocument(null),this.parentModel()&&e&&!this.#Ws&&this.requestDocument()}setDocument(e){this.idToDOMNode=new Map,this.#zs=e&&"nodeId"in e?new Ar(this,e):null,Fr.instance().dispose(this),this.parentModel()||this.dispatchEventToListeners(Tr.DocumentUpdated,this)}setDocumentForTest(e){this.setDocument(e)}setDetachedRoot(e){"#document"===e.nodeName?new Ar(this,e):Pr.create(this,null,!1,e)}setChildNodes(e,t){if(!e&&t.length)return void this.setDetachedRoot(t[0]);const n=this.idToDOMNode.get(e);n?.setChildrenPayload(t)}childNodeCountUpdated(e,t){const n=this.idToDOMNode.get(e);n?(n.setChildNodeCount(t),this.dispatchEventToListeners(Tr.ChildNodeCountUpdated,n),this.scheduleMutationEvent(n)):console.error("nodeId could not be resolved to a node")}childNodeInserted(e,t,n){const r=this.idToDOMNode.get(e),s=this.idToDOMNode.get(t);if(!r)return void console.error("parentId could not be resolved to a node");const i=r.insertChild(s,n);this.idToDOMNode.set(i.id,i),this.dispatchEventToListeners(Tr.NodeInserted,i),this.scheduleMutationEvent(i)}childNodeRemoved(e,t){const n=this.idToDOMNode.get(e),r=this.idToDOMNode.get(t);n&&r?(n.removeChild(r),this.unbind(r),this.dispatchEventToListeners(Tr.NodeRemoved,{node:r,parent:n}),this.scheduleMutationEvent(r)):console.error("parentId or nodeId could not be resolved to a node")}shadowRootPushed(e,t){const n=this.idToDOMNode.get(e);if(!n)return;const r=Pr.create(this,n.ownerDocument,!0,t);r.parentNode=n,this.idToDOMNode.set(r.id,r),n.shadowRootsInternal.unshift(r),this.dispatchEventToListeners(Tr.NodeInserted,r),this.scheduleMutationEvent(r)}shadowRootPopped(e,t){const n=this.idToDOMNode.get(e);if(!n)return;const r=this.idToDOMNode.get(t);r&&(n.removeChild(r),this.unbind(r),this.dispatchEventToListeners(Tr.NodeRemoved,{node:r,parent:n}),this.scheduleMutationEvent(r))}pseudoElementAdded(e,t){const n=this.idToDOMNode.get(e);if(!n)return;const r=Pr.create(this,n.ownerDocument,!1,t);r.parentNode=n,this.idToDOMNode.set(r.id,r);const s=r.pseudoType();if(!s)throw new Error("DOMModel._pseudoElementAdded expects pseudoType to be defined.");const i=n.pseudoElements().get(s);if(i){if(!s.startsWith("view-transition"))throw new Error("DOMModel.pseudoElementAdded expects parent to not already have this pseudo type added; only view-transition* pseudo elements can coexist under the same parent.");i.push(r)}else n.pseudoElements().set(s,[r]);this.dispatchEventToListeners(Tr.NodeInserted,r),this.scheduleMutationEvent(r)}topLayerElementsUpdated(){this.dispatchEventToListeners(Tr.TopLayerElementsChanged)}pseudoElementRemoved(e,t){const n=this.idToDOMNode.get(e);if(!n)return;const r=this.idToDOMNode.get(t);r&&(n.removeChild(r),this.unbind(r),this.dispatchEventToListeners(Tr.NodeRemoved,{node:r,parent:n}),this.scheduleMutationEvent(r))}distributedNodesUpdated(e,t){const n=this.idToDOMNode.get(e);n&&(n.setDistributedNodePayloads(t),this.dispatchEventToListeners(Tr.DistributedNodesChanged,n),this.scheduleMutationEvent(n))}unbind(e){this.idToDOMNode.delete(e.id);const t=e.children();for(let e=0;t&&e<t.length;++e)this.unbind(t[e]);for(let t=0;t<e.shadowRootsInternal.length;++t)this.unbind(e.shadowRootsInternal[t]);const n=e.pseudoElements();for(const e of n.values())for(const t of e)this.unbind(t);const r=e.templateContent();r&&this.unbind(r)}async getNodesByStyle(e,t=!1){if(await this.requestDocument(),!this.#zs)throw new Error("DOMModel.getNodesByStyle expects to have a document.");const n=await this.agent.invoke_getNodesForSubtreeByStyle({nodeId:this.#zs.id,computedStyles:e,pierce:t});if(n.getError())throw n.getError();return n.nodeIds}async performSearch(e,t){const n=await this.agent.invoke_performSearch({query:e,includeUserAgentShadowDOM:t});return n.getError()||(this.#Qs=n.searchId),n.getError()?0:n.resultCount}async searchResult(e){if(!this.#Qs)return null;const{nodeIds:t}=await this.agent.invoke_getSearchResults({searchId:this.#Qs,fromIndex:e,toIndex:e+1});return t&&1===t.length?this.nodeForId(t[0]):null}cancelSearch(){this.#Qs&&(this.agent.invoke_discardSearchResults({searchId:this.#Qs}),this.#Qs=void 0)}classNamesPromise(e){return this.agent.invoke_collectClassNamesFromSubtree({nodeId:e}).then((({classNames:e})=>e||[]))}querySelector(e,t){return this.agent.invoke_querySelector({nodeId:e,selector:t}).then((({nodeId:e})=>e))}querySelectorAll(e,t){return this.agent.invoke_querySelectorAll({nodeId:e,selector:t}).then((({nodeIds:e})=>e))}getTopLayerElements(){return this.agent.invoke_getTopLayerElements().then((({nodeIds:e})=>e))}getElementByRelation(e,t){return this.agent.invoke_getElementByRelation({nodeId:e,relation:t}).then((({nodeId:e})=>e))}markUndoableState(e){Fr.instance().markUndoableState(this,e||!1)}async nodeForLocation(e,t,n){const r=await this.agent.invoke_getNodeForLocation({x:e,y:t,includeUserAgentShadowDOM:n});return r.getError()||!r.nodeId?null:this.nodeForId(r.nodeId)}async getContainerForNode(e,t,n,r){const{nodeId:s}=await this.agent.invoke_getContainerForNode({nodeId:e,containerName:t,physicalAxes:n,logicalAxes:r});return s?this.nodeForId(s):null}pushObjectAsNodeToFrontend(e){return e.isNode()&&e.objectId?this.pushNodeToFrontend(e.objectId):Promise.resolve(null)}suspendModel(){return this.agent.invoke_disable().then((()=>this.setDocument(null)))}async resumeModel(){await this.agent.invoke_enable({})}dispose(){Fr.instance().dispose(this)}parentModel(){const e=this.target().parentTarget();return e?e.model(Or):null}getAgent(){return this.agent}registerNode(e){this.idToDOMNode.set(e.id,e)}}!function(e){e.AttrModified="AttrModified",e.AttrRemoved="AttrRemoved",e.CharacterDataModified="CharacterDataModified",e.DOMMutated="DOMMutated",e.NodeInserted="NodeInserted",e.NodeRemoved="NodeRemoved",e.DocumentUpdated="DocumentUpdated",e.ChildNodeCountUpdated="ChildNodeCountUpdated",e.DistributedNodesChanged="DistributedNodesChanged",e.MarkersChanged="MarkersChanged",e.TopLayerElementsChanged="TopLayerElementsChanged"}(Tr||(Tr={}));class Dr{#Pn;constructor(e){this.#Pn=e}documentUpdated(){this.#Pn.documentUpdated()}attributeModified({nodeId:e,name:t,value:n}){this.#Pn.attributeModified(e,t,n)}attributeRemoved({nodeId:e,name:t}){this.#Pn.attributeRemoved(e,t)}inlineStyleInvalidated({nodeIds:e}){this.#Pn.inlineStyleInvalidated(e)}characterDataModified({nodeId:e,characterData:t}){this.#Pn.characterDataModified(e,t)}setChildNodes({parentId:e,nodes:t}){this.#Pn.setChildNodes(e,t)}childNodeCountUpdated({nodeId:e,childNodeCount:t}){this.#Pn.childNodeCountUpdated(e,t)}childNodeInserted({parentNodeId:e,previousNodeId:t,node:n}){this.#Pn.childNodeInserted(e,t,n)}childNodeRemoved({parentNodeId:e,nodeId:t}){this.#Pn.childNodeRemoved(e,t)}shadowRootPushed({hostId:e,root:t}){this.#Pn.shadowRootPushed(e,t)}shadowRootPopped({hostId:e,rootId:t}){this.#Pn.shadowRootPopped(e,t)}pseudoElementAdded({parentId:e,pseudoElement:t}){this.#Pn.pseudoElementAdded(e,t)}pseudoElementRemoved({parentId:e,pseudoElementId:t}){this.#Pn.pseudoElementRemoved(e,t)}distributedNodesUpdated({insertionPointId:e,distributedNodes:t}){this.#Pn.distributedNodesUpdated(e,t)}topLayerElementsUpdated(){this.#Pn.topLayerElementsUpdated()}}let Nr=null;class Fr{#Ct;#Fr;#$s;constructor(){this.#Ct=[],this.#Fr=0,this.#$s=null}static instance(e={forceNew:null}){const{forceNew:t}=e;return Nr&&!t||(Nr=new Fr),Nr}async markUndoableState(e,t){this.#$s&&e!==this.#$s&&(this.#$s.markUndoableState(),this.#$s=null),t&&this.#$s===e||(this.#Ct=this.#Ct.slice(0,this.#Fr),this.#Ct.push(e),this.#Fr=this.#Ct.length,t?this.#$s=e:(await e.getAgent().invoke_markUndoableState(),this.#$s=null))}async undo(){if(0===this.#Fr)return Promise.resolve();--this.#Fr,this.#$s=null,await this.#Ct[this.#Fr].getAgent().invoke_undo()}async redo(){if(this.#Fr>=this.#Ct.length)return Promise.resolve();++this.#Fr,this.#$s=null,await this.#Ct[this.#Fr-1].getAgent().invoke_redo()}dispose(e){let t=0;for(let n=0;n<this.#Fr;++n)this.#Ct[n]===e&&++t;s.ArrayUtilities.removeElement(this.#Ct,e),this.#Fr-=t,this.#$s===e&&(this.#$s=null)}}h.register(Or,{capabilities:2,autostart:!0});var Br=Object.freeze({__proto__:null,get DOMNode(){return Pr},DeferredDOMNode:Lr,DOMNodeShortcut:Er,DOMDocument:Ar,DOMModel:Or,get Events(){return Tr},DOMModelUndoStack:Fr});class Hr{#An;#Xs;#Js;#Ys;#Zs;#ei;#ti;#ni;#ri;#si;#ii;#oi;#ai=null;#li=null;constructor(t,n,r,i,o,a,l,d,c,h){this.#An=t,this.#Xs=n,this.url=r,this.#Ys=i,this.#Zs=o,this.#ei=a,this.#ti=l||e.ResourceType.resourceTypes.Other,this.#ni=d,this.#ri=!1,this.#si=c&&s.DateUtilities.isValid(c)?c:null,this.#ii=h}lastModified(){if(this.#si||!this.#Xs)return this.#si;const e=this.#Xs.responseLastModified(),t=e?new Date(e):null;return this.#si=t&&s.DateUtilities.isValid(t)?t:null,this.#si}contentSize(){return"number"!=typeof this.#ii&&this.#Xs?this.#Xs.resourceSize:this.#ii}get request(){return this.#Xs}get url(){return this.#Js}set url(t){this.#Js=t,this.#oi=new e.ParsedURL.ParsedURL(t)}get parsedURL(){return this.#oi}get documentURL(){return this.#Ys}get frameId(){return this.#Zs}get loaderId(){return this.#ei}get displayName(){return this.#oi?this.#oi.displayName:""}resourceType(){return this.#Xs?this.#Xs.resourceType():this.#ti}get mimeType(){return this.#Xs?this.#Xs.mimeType:this.#ni}get content(){return this.#ai?.isTextContent?this.#ai.text:this.#ai?.base64??null}get isGenerated(){return this.#ri}set isGenerated(e){this.#ri=e}contentURL(){return this.#Js}contentType(){return this.resourceType()===e.ResourceType.resourceTypes.Document&&-1!==this.mimeType.indexOf("javascript")?e.ResourceType.resourceTypes.Script:this.resourceType()}async requestContent(){const e=await this.requestContentData();return n.ContentData.ContentData.asDeferredContent(e)}async requestContentData(){return this.#ai?this.#ai:(this.#li||(this.#li=this.innerRequestContent().then((e=>(n.ContentData.ContentData.isError(e)||(this.#ai=e),this.#li=null,e)))),this.#li)}canonicalMimeType(){return this.contentType().canonicalMimeType()||this.mimeType}async searchInContent(e,t,r){if(!this.frameId)return[];if(this.request)return this.request.searchInContent(e,t,r);const s=await this.#An.target().pageAgent().invoke_searchInResource({frameId:this.frameId,url:this.url,query:e,caseSensitive:t,isRegex:r});return n.TextUtils.performSearchInSearchMatches(s.result||[],e,t,r)}async populateImageSource(e){const t=await this.requestContentData();n.ContentData.ContentData.isError(t)||(e.src=t.asDataUrl()??this.#Js)}async innerRequestContent(){if(this.request)return this.request.requestContentData();const e=await this.#An.target().pageAgent().invoke_getResourceContent({frameId:this.frameId,url:this.url}),t=e.getError();return t?{error:t}:new n.ContentData.ContentData(e.content,e.base64Encoded,this.mimeType)}hasTextContent(){return!!this.#ai?.isTextContent||(this.#ti.isTextType()||s.MimeType.isTextType(this.mimeType))}frame(){return this.#Zs?this.#An.frameForId(this.#Zs):null}statusCode(){return this.#Xs?this.#Xs.statusCode:0}}var Ur,_r=Object.freeze({__proto__:null,Resource:Hr});class qr extends h{#di;#ci;#hi;constructor(e){super(e),this.#di="",this.#ci="",this.#hi=new Set}updateSecurityOrigins(e){const t=this.#hi;this.#hi=e;for(const e of t)this.#hi.has(e)||this.dispatchEventToListeners(Ur.SecurityOriginRemoved,e);for(const e of this.#hi)t.has(e)||this.dispatchEventToListeners(Ur.SecurityOriginAdded,e)}securityOrigins(){return[...this.#hi]}mainSecurityOrigin(){return this.#di}unreachableMainSecurityOrigin(){return this.#ci}setMainSecurityOrigin(e,t){this.#di=e,this.#ci=t||null,this.dispatchEventToListeners(Ur.MainSecurityOriginChanged,{mainSecurityOrigin:this.#di,unreachableMainSecurityOrigin:this.#ci})}}!function(e){e.SecurityOriginAdded="SecurityOriginAdded",e.SecurityOriginRemoved="SecurityOriginRemoved",e.MainSecurityOriginChanged="MainSecurityOriginChanged"}(Ur||(Ur={})),h.register(qr,{capabilities:0,autostart:!1});var zr=Object.freeze({__proto__:null,SecurityOriginManager:qr,get Events(){return Ur}});class jr extends h{#ui;#gi;constructor(e){super(e),this.#ui="",this.#gi=new Set}updateStorageKeys(e){const t=this.#gi;this.#gi=e;for(const e of t)this.#gi.has(e)||this.dispatchEventToListeners("StorageKeyRemoved",e);for(const e of this.#gi)t.has(e)||this.dispatchEventToListeners("StorageKeyAdded",e)}storageKeys(){return[...this.#gi]}mainStorageKey(){return this.#ui}setMainStorageKey(e){this.#ui=e,this.dispatchEventToListeners("MainStorageKeyChanged",{mainStorageKey:this.#ui})}}h.register(jr,{capabilities:0,autostart:!1});var Vr,Wr=Object.freeze({__proto__:null,StorageKeyManager:jr,parseStorageKey:function(t){const n=t.split("^"),r={origin:e.ParsedURL.ParsedURL.extractOrigin(n[0]),components:new Map};for(let e=1;e<n.length;++e)r.components.set(n[e].charAt(0),n[e].substring(1));return r}});class Gr extends h{agent;storageAgent;#pi;#mi;framesInternal;#fi;#bi;#yi;isInterstitialShowing;mainFrame;#Ii;constructor(e){super(e);const t=e.model(X);t&&(t.addEventListener(J.RequestFinished,this.onRequestFinished,this),t.addEventListener(J.RequestUpdateDropped,this.onRequestUpdateDropped,this)),this.agent=e.pageAgent(),this.storageAgent=e.storageAgent(),this.agent.invoke_enable(),this.#pi=e.model(qr),this.#mi=e.model(jr),this.#Ii=new Set,e.registerPageDispatcher(new Qr(this)),this.framesInternal=new Map,this.#fi=!1,this.#bi=null,this.#yi=0,this.isInterstitialShowing=!1,this.mainFrame=null,this.#vi()}async#vi(){return this.agent.invoke_getResourceTree().then((e=>{this.processCachedResources(e.getError()?null:e.frameTree),this.mainFrame&&this.processPendingEvents(this.mainFrame)}))}static frameForRequest(e){const t=X.forRequest(e),n=t?t.target().model(Gr):null;return n&&e.frameId?n.frameForId(e.frameId):null}static frames(){const e=[];for(const t of z.instance().models(Gr))e.push(...t.frames());return e}static resourceForURL(e){for(const t of z.instance().models(Gr)){const n=t.mainFrame,r=n?n.resourceForURL(e):null;if(r)return r}return null}static reloadAllPages(e,t){for(const n of z.instance().models(Gr))n.target().parentTarget()?.type()!==B.Frame&&n.reloadPage(e,t)}async storageKeyForFrame(e){if(!this.framesInternal.has(e))return null;const t=await this.storageAgent.invoke_getStorageKeyForFrame({frameId:e});return"Frame tree node for given frame not found"===t.getError()?null:t.storageKey}domModel(){return this.target().model(Or)}processCachedResources(e){e&&":"!==e.frame.url&&(this.dispatchEventToListeners(Vr.WillLoadCachedResources),this.addFramesRecursively(null,e),this.target().setInspectedURL(e.frame.url)),this.#fi=!0;const t=this.target().model(_n);t&&(t.setExecutionContextComparator(this.executionContextComparator.bind(this)),t.fireExecutionContextOrderChanged()),this.dispatchEventToListeners(Vr.CachedResourcesLoaded,this)}cachedResourcesLoaded(){return this.#fi}addFrame(e,t){this.framesInternal.set(e.id,e),e.isMainFrame()&&(this.mainFrame=e),this.dispatchEventToListeners(Vr.FrameAdded,e),this.updateSecurityOrigins(),this.updateStorageKeys()}frameAttached(e,t,n){const r=t&&this.framesInternal.get(t)||null;if(!this.#fi&&r)return null;if(this.framesInternal.has(e))return null;const s=new Kr(this,r,e,null,n||null);return t&&!r&&(s.crossTargetParentFrameId=t),s.isMainFrame()&&this.mainFrame&&this.frameDetached(this.mainFrame.id,!1),this.addFrame(s,!0),s}frameNavigated(e,t){const n=e.parentId&&this.framesInternal.get(e.parentId)||null;if(!this.#fi&&n)return;let r=this.framesInternal.get(e.id)||null;if(!r&&(r=this.frameAttached(e.id,e.parentId||null),console.assert(Boolean(r)),!r))return;this.dispatchEventToListeners(Vr.FrameWillNavigate,r),r.navigate(e),t&&(r.backForwardCacheDetails.restoredFromCache="BackForwardCacheRestore"===t),r.isMainFrame()&&this.target().setInspectedURL(r.url),this.dispatchEventToListeners(Vr.FrameNavigated,r),r.isPrimaryFrame()&&this.primaryPageChanged(r,"Navigation");const s=r.resources();for(let e=0;e<s.length;++e)this.dispatchEventToListeners(Vr.ResourceAdded,s[e]);this.updateSecurityOrigins(),this.updateStorageKeys(),r.backForwardCacheDetails.restoredFromCache&&(Ot.instance().modelRemoved(this),Ot.instance().modelAdded(this),this.#vi())}primaryPageChanged(e,t){this.processPendingEvents(e),this.dispatchEventToListeners(Vr.PrimaryPageChanged,{frame:e,type:t});const n=this.target().model(X);n&&e.isOutermostFrame()&&n.clearRequests()}documentOpened(t){this.frameNavigated(t,void 0);const n=this.framesInternal.get(t.id);if(n&&!n.getResourcesMap().get(t.url)){const r=this.createResourceFromFramePayload(t,t.url,e.ResourceType.resourceTypes.Document,t.mimeType,null,null);r.isGenerated=!0,n.addResource(r)}}frameDetached(e,t){if(!this.#fi)return;const n=this.framesInternal.get(e);if(!n)return;const r=n.sameTargetParentFrame();r?r.removeChildFrame(n,t):n.remove(t),this.updateSecurityOrigins(),this.updateStorageKeys()}onRequestFinished(e){if(!this.#fi)return;const t=e.data;if(t.failed)return;const n=t.frameId?this.framesInternal.get(t.frameId):null;n&&n.addRequest(t)}onRequestUpdateDropped(t){if(!this.#fi)return;const n=t.data,r=n.frameId;if(!r)return;const s=this.framesInternal.get(r);if(!s)return;const i=n.url;if(s.getResourcesMap().get(i))return;const o=new Hr(this,null,i,s.url,r,n.loaderId,e.ResourceType.resourceTypes[n.resourceType],n.mimeType,n.lastModified,null);s.addResource(o)}frameForId(e){return this.framesInternal.get(e)||null}forAllResources(e){return!!this.mainFrame&&this.mainFrame.callForFrameResources(e)}frames(){return[...this.framesInternal.values()]}resourceForURL(e){return this.mainFrame?this.mainFrame.resourceForURL(e):null}addFramesRecursively(t,n){const r=n.frame;let s=this.framesInternal.get(r.id);s||(s=new Kr(this,t,r.id,r,null)),!t&&r.parentId&&(s.crossTargetParentFrameId=r.parentId),this.addFrame(s);for(const e of n.childFrames||[])this.addFramesRecursively(s,e);for(let t=0;t<n.resources.length;++t){const i=n.resources[t],o=this.createResourceFromFramePayload(r,i.url,e.ResourceType.resourceTypes[i.type],i.mimeType,i.lastModified||null,i.contentSize||null);s.addResource(o)}if(!s.getResourcesMap().get(r.url)){const t=this.createResourceFromFramePayload(r,r.url,e.ResourceType.resourceTypes.Document,r.mimeType,null,null);s.addResource(t)}}createResourceFromFramePayload(e,t,n,r,s,i){const o="number"==typeof s?new Date(1e3*s):null;return new Hr(this,null,t,e.url,e.id,e.loaderId,n,r,o,i)}suspendReload(){this.#yi++}resumeReload(){if(this.#yi--,console.assert(this.#yi>=0,"Unbalanced call to ResourceTreeModel.resumeReload()"),!this.#yi&&this.#bi){const{ignoreCache:e,scriptToEvaluateOnLoad:t}=this.#bi;this.reloadPage(e,t)}}reloadPage(e,t){const n=this.mainFrame?.loaderId;if(!n)return;if(this.#bi||this.dispatchEventToListeners(Vr.PageReloadRequested,this),this.#yi)return void(this.#bi={ignoreCache:e,scriptToEvaluateOnLoad:t});this.#bi=null;const r=this.target().model(X);r&&r.clearRequests(),this.dispatchEventToListeners(Vr.WillReloadPage),this.agent.invoke_reload({ignoreCache:e,scriptToEvaluateOnLoad:t,loaderId:n})}navigate(e){return this.agent.invoke_navigate({url:e})}async navigationHistory(){const e=await this.agent.invoke_getNavigationHistory();return e.getError()?null:{currentIndex:e.currentIndex,entries:e.entries}}navigateToHistoryEntry(e){this.agent.invoke_navigateToHistoryEntry({entryId:e.id})}setLifecycleEventsEnabled(e){return this.agent.invoke_setLifecycleEventsEnabled({enabled:e})}async fetchAppManifest(){const e=await this.agent.invoke_getAppManifest({});return e.getError()?{url:e.url,data:null,errors:[]}:{url:e.url,data:e.data||null,errors:e.errors}}async getInstallabilityErrors(){return(await this.agent.invoke_getInstallabilityErrors()).installabilityErrors||[]}async getAppId(){return this.agent.invoke_getAppId()}executionContextComparator(e,t){function n(e){let t=e;const n=[];for(;t;)n.push(t),t=t.sameTargetParentFrame();return n.reverse()}if(e.target()!==t.target())return zn.comparator(e,t);const r=e.frameId?n(this.frameForId(e.frameId)):[],s=t.frameId?n(this.frameForId(t.frameId)):[];let i,o;for(let e=0;;e++)if(!r[e]||!s[e]||r[e]!==s[e]){i=r[e],o=s[e];break}return!i&&o?-1:!o&&i?1:i&&o?i.id.localeCompare(o.id):zn.comparator(e,t)}getSecurityOriginData(){const t=new Set;let n=null,r=null;for(const s of this.framesInternal.values()){const i=s.securityOrigin;if(i&&(t.add(i),s.isMainFrame()&&(n=i,s.unreachableUrl()))){r=new e.ParsedURL.ParsedURL(s.unreachableUrl()).securityOrigin()}}return{securityOrigins:t,mainSecurityOrigin:n,unreachableMainSecurityOrigin:r}}async getStorageKeyData(){const e=new Set;let t=null;for(const{isMainFrame:n,storageKey:r}of await Promise.all([...this.framesInternal.values()].map((e=>e.getStorageKey(!1).then((t=>({isMainFrame:e.isMainFrame(),storageKey:t})))))))n&&(t=r),r&&e.add(r);return{storageKeys:e,mainStorageKey:t}}updateSecurityOrigins(){const e=this.getSecurityOriginData();this.#pi.setMainSecurityOrigin(e.mainSecurityOrigin||"",e.unreachableMainSecurityOrigin||""),this.#pi.updateSecurityOrigins(e.securityOrigins)}async updateStorageKeys(){const e=await this.getStorageKeyData();this.#mi.setMainStorageKey(e.mainStorageKey||""),this.#mi.updateStorageKeys(e.storageKeys)}async getMainStorageKey(){return this.mainFrame?this.mainFrame.getStorageKey(!1):null}getMainSecurityOrigin(){const e=this.getSecurityOriginData();return e.mainSecurityOrigin||e.unreachableMainSecurityOrigin}onBackForwardCacheNotUsed(e){this.mainFrame&&this.mainFrame.id===e.frameId&&this.mainFrame.loaderId===e.loaderId?(this.mainFrame.setBackForwardCacheDetails(e),this.dispatchEventToListeners(Vr.BackForwardCacheDetailsUpdated,this.mainFrame)):this.#Ii.add(e)}processPendingEvents(e){if(e.isMainFrame())for(const t of this.#Ii)if(e.id===t.frameId&&e.loaderId===t.loaderId){e.setBackForwardCacheDetails(t),this.#Ii.delete(t);break}}}!function(e){e.FrameAdded="FrameAdded",e.FrameNavigated="FrameNavigated",e.FrameDetached="FrameDetached",e.FrameResized="FrameResized",e.FrameWillNavigate="FrameWillNavigate",e.PrimaryPageChanged="PrimaryPageChanged",e.ResourceAdded="ResourceAdded",e.WillLoadCachedResources="WillLoadCachedResources",e.CachedResourcesLoaded="CachedResourcesLoaded",e.DOMContentLoaded="DOMContentLoaded",e.LifecycleEvent="LifecycleEvent",e.Load="Load",e.PageReloadRequested="PageReloadRequested",e.WillReloadPage="WillReloadPage",e.InterstitialShown="InterstitialShown",e.InterstitialHidden="InterstitialHidden",e.BackForwardCacheDetailsUpdated="BackForwardCacheDetailsUpdated",e.JavaScriptDialogOpening="JavaScriptDialogOpening"}(Vr||(Vr={}));class Kr{#Br;#ki;#C;crossTargetParentFrameId;#ei;#h;#Js;#Si;#wi;#Ci;#Ri;#xi;#Ti;#Mi;#Pi;#Li;#Ei;#Ai;resourcesMap;backForwardCacheDetails={restoredFromCache:void 0,explanations:[],explanationsTree:void 0};constructor(e,t,n,r,i){this.#Br=e,this.#ki=t,this.#C=n,this.crossTargetParentFrameId=null,this.#ei=r?.loaderId??"",this.#h=r&&r.name,this.#Js=r&&r.url||s.DevToolsPath.EmptyUrlString,this.#Si=r&&r.domainAndRegistry||"",this.#wi=r&&r.securityOrigin,this.#Ri=r&&r.unreachableUrl||s.DevToolsPath.EmptyUrlString,this.#xi=r?.adFrameStatus,this.#Ti=r&&r.secureContextType,this.#Mi=r&&r.crossOriginIsolatedContextType,this.#Pi=r&&r.gatedAPIFeatures,this.#Li=i,this.#Ei=null,this.#Ai=new Set,this.resourcesMap=new Map,this.#ki&&this.#ki.#Ai.add(this)}isSecureContext(){return null!==this.#Ti&&this.#Ti.startsWith("Secure")}getSecureContextType(){return this.#Ti}isCrossOriginIsolated(){return null!==this.#Mi&&this.#Mi.startsWith("Isolated")}getCrossOriginIsolatedContextType(){return this.#Mi}getGatedAPIFeatures(){return this.#Pi}getCreationStackTraceData(){return{creationStackTrace:this.#Li,creationStackTraceTarget:this.#Ei||this.resourceTreeModel().target()}}navigate(e){this.#ei=e.loaderId,this.#h=e.name,this.#Js=e.url,this.#Si=e.domainAndRegistry,this.#wi=e.securityOrigin,this.getStorageKey(!0),this.#Ri=e.unreachableUrl||s.DevToolsPath.EmptyUrlString,this.#xi=e?.adFrameStatus,this.#Ti=e.secureContextType,this.#Mi=e.crossOriginIsolatedContextType,this.#Pi=e.gatedAPIFeatures,this.backForwardCacheDetails={restoredFromCache:void 0,explanations:[],explanationsTree:void 0};const t=this.resourcesMap.get(this.#Js);this.resourcesMap.clear(),this.removeChildFrames(),t&&t.loaderId===this.#ei&&this.addResource(t)}resourceTreeModel(){return this.#Br}get id(){return this.#C}get name(){return this.#h||""}get url(){return this.#Js}domainAndRegistry(){return this.#Si}async getAdScriptId(e){return(await this.#Br.agent.invoke_getAdScriptId({frameId:e})).adScriptId||null}get securityOrigin(){return this.#wi}getStorageKey(e){return this.#Ci&&!e||(this.#Ci=this.#Br.storageKeyForFrame(this.#C)),this.#Ci}unreachableUrl(){return this.#Ri}get loaderId(){return this.#ei}adFrameType(){return this.#xi?.adFrameType||"none"}adFrameStatus(){return this.#xi}get childFrames(){return[...this.#Ai]}sameTargetParentFrame(){return this.#ki}crossTargetParentFrame(){if(!this.crossTargetParentFrameId)return null;const e=this.#Br.target().parentTarget();if(e?.type()!==B.Frame)return null;const t=e.model(Gr);return t&&t.framesInternal.get(this.crossTargetParentFrameId)||null}parentFrame(){return this.sameTargetParentFrame()||this.crossTargetParentFrame()}isMainFrame(){return!this.#ki}isOutermostFrame(){return this.#Br.target().parentTarget()?.type()!==B.Frame&&!this.#ki&&!this.crossTargetParentFrameId}isPrimaryFrame(){return!this.#ki&&this.#Br.target()===z.instance().primaryPageTarget()}removeChildFrame(e,t){this.#Ai.delete(e),e.remove(t)}removeChildFrames(){const e=this.#Ai;this.#Ai=new Set;for(const t of e)t.remove(!1)}remove(e){this.removeChildFrames(),this.#Br.framesInternal.delete(this.id),this.#Br.dispatchEventToListeners(Vr.FrameDetached,{frame:this,isSwap:e})}addResource(e){this.resourcesMap.get(e.url)!==e&&(this.resourcesMap.set(e.url,e),this.#Br.dispatchEventToListeners(Vr.ResourceAdded,e))}addRequest(e){let t=this.resourcesMap.get(e.url());t&&t.request===e||(t=new Hr(this.#Br,e,e.url(),e.documentURL,e.frameId,e.loaderId,e.resourceType(),e.mimeType,null,null),this.resourcesMap.set(t.url,t),this.#Br.dispatchEventToListeners(Vr.ResourceAdded,t))}resources(){return Array.from(this.resourcesMap.values())}resourceForURL(e){const t=this.resourcesMap.get(e);if(t)return t;for(const t of this.#Ai){const n=t.resourceForURL(e);if(n)return n}return null}callForFrameResources(e){for(const t of this.resourcesMap.values())if(e(t))return!0;for(const t of this.#Ai)if(t.callForFrameResources(e))return!0;return!1}displayName(){if(this.isOutermostFrame())return r.i18n.lockedString("top");const t=new e.ParsedURL.ParsedURL(this.#Js).displayName;return t?this.#h?this.#h+" ("+t+")":t:r.i18n.lockedString("iframe")}async getOwnerDeferredDOMNode(){const e=this.parentFrame();return e?e.resourceTreeModel().domModel().getOwnerNodeForFrame(this.#C):null}async getOwnerDOMNodeOrDocument(){const e=await this.getOwnerDeferredDOMNode();return e?e.resolvePromise():this.isOutermostFrame()?this.resourceTreeModel().domModel().requestDocument():null}async highlight(){const e=this.parentFrame(),t=this.resourceTreeModel().target().parentTarget(),n=async e=>{const t=await e.getOwnerNodeForFrame(this.#C);t&&e.overlayModel().highlightInOverlay({deferredNode:t,selectorList:""},"all",!0)};if(e)return n(e.resourceTreeModel().domModel());if(t?.type()===B.Frame){const e=t.model(Or);if(e)return n(e)}const r=await this.resourceTreeModel().domModel().requestDocument();r&&this.resourceTreeModel().domModel().overlayModel().highlightInOverlay({node:r,selectorList:""},"all",!0)}async getPermissionsPolicyState(){const e=await this.resourceTreeModel().target().pageAgent().invoke_getPermissionsPolicyState({frameId:this.#C});return e.getError()?null:e.states}async getOriginTrials(){const e=await this.resourceTreeModel().target().pageAgent().invoke_getOriginTrials({frameId:this.#C});return e.getError()?[]:e.originTrials}setCreationStackTrace(e){this.#Li=e.creationStackTrace,this.#Ei=e.creationStackTraceTarget}setBackForwardCacheDetails(e){this.backForwardCacheDetails.restoredFromCache=!1,this.backForwardCacheDetails.explanations=e.notRestoredExplanations,this.backForwardCacheDetails.explanationsTree=e.notRestoredExplanationsTree}getResourcesMap(){return this.resourcesMap}}class Qr{#An;constructor(e){this.#An=e}backForwardCacheNotUsed(e){this.#An.onBackForwardCacheNotUsed(e)}domContentEventFired({timestamp:e}){this.#An.dispatchEventToListeners(Vr.DOMContentLoaded,e)}loadEventFired({timestamp:e}){this.#An.dispatchEventToListeners(Vr.Load,{resourceTreeModel:this.#An,loadTime:e})}lifecycleEvent({frameId:e,name:t}){this.#An.dispatchEventToListeners(Vr.LifecycleEvent,{frameId:e,name:t})}frameAttached({frameId:e,parentFrameId:t,stack:n}){this.#An.frameAttached(e,t,n)}frameNavigated({frame:e,type:t}){this.#An.frameNavigated(e,t)}documentOpened({frame:e}){this.#An.documentOpened(e)}frameDetached({frameId:e,reason:t}){this.#An.frameDetached(e,"swap"===t)}frameStartedLoading({}){}frameStoppedLoading({}){}frameRequestedNavigation({}){}frameScheduledNavigation({}){}frameClearedScheduledNavigation({}){}navigatedWithinDocument({}){}frameResized(){this.#An.dispatchEventToListeners(Vr.FrameResized)}javascriptDialogOpening(e){this.#An.dispatchEventToListeners(Vr.JavaScriptDialogOpening,e),e.hasBrowserHandler||this.#An.agent.invoke_handleJavaScriptDialog({accept:!1})}javascriptDialogClosed({}){}screencastFrame({}){}screencastVisibilityChanged({}){}interstitialShown(){this.#An.isInterstitialShowing=!0,this.#An.dispatchEventToListeners(Vr.InterstitialShown)}interstitialHidden(){this.#An.isInterstitialShowing=!1,this.#An.dispatchEventToListeners(Vr.InterstitialHidden)}windowOpen({}){}compilationCacheProduced({}){}fileChooserOpened({}){}downloadWillBegin({}){}downloadProgress(){}}h.register(Gr,{capabilities:2,autostart:!0,early:!0});var $r=Object.freeze({__proto__:null,ResourceTreeModel:Gr,get Events(){return Vr},ResourceTreeFrame:Kr,PageDispatcher:Qr});class Xr extends h{#Oi;#Di;#Ni;#Fi;constructor(t){super(t),this.#Ni=new e.Throttler.Throttler(300),this.#Oi=new Map,this.#Di=new Map,this.#Fi=new Map,t.model(Gr)?.addEventListener(Vr.PrimaryPageChanged,this.#Bi,this),t.model(X)?.addEventListener(J.ResponseReceived,this.#Hi,this),t.model(X)?.addEventListener(J.LoadingFinished,this.#Ui,this)}addBlockedCookie(e,t){const n=e.key(),r=this.#Oi.get(n);this.#Oi.set(n,e),t?this.#Di.set(e,t):this.#Di.delete(e),r&&this.#Di.delete(r)}removeBlockedCookie(e){this.#Oi.delete(e.key())}async#Bi(){this.#Oi.clear(),this.#Di.clear(),await this.#_i()}getCookieToBlockedReasonsMap(){return this.#Di}async#qi(e){const t=this.target().networkAgent(),n=new Map(await Promise.all(e.keysArray().map((n=>t.invoke_getCookies({urls:[...e.get(n).values()]}).then((({cookies:e})=>[n,e.map(F.fromProtocolCookie)])))))),r=this.#zi(n);this.#Fi=n,r&&this.dispatchEventToListeners("CookieListUpdated")}async deleteCookie(e){await this.deleteCookies([e])}async clear(e,t){this.#ji()||await this.#Vi();const n=e?this.#Fi.get(e)||[]:[...this.#Fi.values()].flat();if(n.push(...this.#Oi.values()),t){const e=n.filter((e=>e.matchesSecurityOrigin(t)));await this.deleteCookies(e)}else await this.deleteCookies(n)}async saveCookie(e){let t,n=e.domain();n.startsWith(".")||(n=""),e.expires()&&(t=Math.floor(Date.parse(`${e.expires()}`)/1e3));const r=o.Runtime.experiments.isEnabled("experimental-cookie-features"),s={name:e.name(),value:e.value(),url:e.url()||void 0,domain:n,path:e.path(),secure:e.secure(),httpOnly:e.httpOnly(),sameSite:e.sameSite(),expires:t,priority:e.priority(),partitionKey:e.partitionKey(),sourceScheme:r?e.sourceScheme():(i=e.sourceScheme(),"Unset"===i?i:void 0),sourcePort:r?e.sourcePort():void 0};var i;const a=await this.target().networkAgent().invoke_setCookie(s);return!(a.getError()||!a.success)&&(await this.#Vi(),a.success)}async getCookiesForDomain(e,t){this.#ji()&&!t||await this.#Vi();return(this.#Fi.get(e)||[]).concat(Array.from(this.#Oi.values()))}async deleteCookies(e){const t=this.target().networkAgent();this.#Oi.clear(),this.#Di.clear(),await Promise.all(e.map((e=>t.invoke_deleteCookies({name:e.name(),url:void 0,domain:e.domain(),path:e.path(),partitionKey:e.partitionKey()})))),await this.#Vi()}#ji(){return Boolean(this.listeners?.size)}#zi(e){if(e.size!==this.#Fi.size)return!0;for(const[t,n]of e){if(!this.#Fi.has(t))return!0;const e=this.#Fi.get(t)||[];if(n.length!==e.length)return!0;const r=e=>e.key()+" "+e.value(),s=new Set(e.map(r));for(const e of n)if(!s.has(r(e)))return!0}return!1}#Vi(){return this.#Ni.schedule((()=>this.#_i()))}#_i(){const t=new s.MapUtilities.Multimap;const n=this.target().model(Gr);if(n){const r=n.mainFrame?.unreachableUrl();if(r){const n=e.ParsedURL.ParsedURL.fromString(r);n&&t.set(n.securityOrigin(),r)}n.forAllResources((function(n){const r=e.ParsedURL.ParsedURL.fromString(n.documentURL);return r&&t.set(r.securityOrigin(),n.url),!1}))}return this.#qi(t)}#Hi(){this.#ji()&&this.#Vi()}#Ui(){this.#ji()&&this.#Vi()}}h.register(Xr,{capabilities:16,autostart:!1});var Jr=Object.freeze({__proto__:null,CookieModel:Xr});class Yr{#Wi;#Gi;#Ki;#Qi;#$i;#Xi;#Ji;constructor(e){e&&(this.#Wi=e.toLowerCase().replace(/^\./,"")),this.#Gi=[],this.#Qi=0}static parseSetCookie(e,t){return new Yr(t).parseSetCookie(e)}getCookieAttribute(e){if(!e)return null;switch(e.toLowerCase()){case"domain":return"domain";case"expires":return"expires";case"max-age":return"max-age";case"httponly":return"http-only";case"name":return"name";case"path":return"path";case"samesite":return"same-site";case"secure":return"secure";case"value":return"value";case"priority":return"priority";case"sourceport":return"source-port";case"sourcescheme":return"source-scheme";case"partitioned":return"partitioned";default:return console.error("Failed getting cookie attribute: "+e),null}}cookies(){return this.#Gi}parseSetCookie(e){if(!this.initialize(e))return null;for(let e=this.extractKeyValue();e;e=this.extractKeyValue())this.#$i?this.#$i.addAttribute(this.getCookieAttribute(e.key),e.value):this.addCookie(e,1),this.advanceAndCheckCookieDelimiter()&&this.flushCookie();return this.flushCookie(),this.#Gi}initialize(e){return this.#Ki=e,"string"==typeof e&&(this.#Gi=[],this.#$i=null,this.#Xi="",this.#Qi=this.#Ki.length,!0)}flushCookie(){this.#$i&&(this.#$i.setSize(this.#Qi-this.#Ki.length-this.#Ji),this.#$i.setCookieLine(this.#Xi.replace("\n",""))),this.#$i=null,this.#Xi=""}extractKeyValue(){if(!this.#Ki||!this.#Ki.length)return null;const e=/^[ \t]*([^=;\n]+)[ \t]*(?:=[ \t]*([^;\n]*))?/.exec(this.#Ki);if(!e)return console.error("Failed parsing cookie header before: "+this.#Ki),null;const t=new Zr(e[1]&&e[1].trim(),e[2]&&e[2].trim(),this.#Qi-this.#Ki.length);return this.#Xi+=e[0],this.#Ki=this.#Ki.slice(e[0].length),t}advanceAndCheckCookieDelimiter(){if(!this.#Ki)return!1;const e=/^\s*[\n;]\s*/.exec(this.#Ki);return!!e&&(this.#Xi+=e[0],this.#Ki=this.#Ki.slice(e[0].length),null!==e[0].match("\n"))}addCookie(e,t){this.#$i&&this.#$i.setSize(e.position-this.#Ji),this.#$i="string"==typeof e.value?new F(e.key,e.value,t):new F("",e.key,t),this.#Wi&&this.#$i.addAttribute("domain",this.#Wi),this.#Ji=e.position,this.#Gi.push(this.#$i)}}class Zr{key;value;position;constructor(e,t,n){this.key=e,this.value=t,this.position=n}}var es=Object.freeze({__proto__:null,CookieParser:Yr});class ts{#Yi;#Zi;#eo=!1;#to="";#no="";#ro="";#so="";constructor(e,t){this.#Yi=e,this.#Zi=new ns(this.#io.bind(this),t)}async addBase64Chunk(e){await this.#Zi.addBase64Chunk(e)}#io(e){let t=0;for(let n=0;n<e.length;++n)this.#eo&&"\n"===e[n]?(this.#eo=!1,++t):(this.#eo=!1,"\r"!==e[n]&&"\n"!==e[n]||(this.#to+=e.substring(t,n),this.#oo(),this.#to="",t=n+1,this.#eo="\r"===e[n]));this.#to+=e.substring(t)}#oo(){if(0===this.#to.length){if(this.#ro.length>0){const e=this.#ro.slice(0,-1);this.#Yi(this.#so||"message",e,this.#no),this.#ro=""}return void(this.#so="")}let e,t=this.#to.indexOf(":");t<0?(t=this.#to.length,e=t):(e=t+1,e<this.#to.length&&" "===this.#to[e]&&++e);const n=this.#to.substring(0,t);"event"!==n?("data"===n&&(this.#ro+=this.#to.substring(e),this.#ro+="\n"),"id"===n&&(this.#no=this.#to.substring(e))):this.#so=this.#to.substring(e)}}class ns{#Zi;#ao;constructor(e,t){this.#Zi=new TextDecoderStream(t),this.#ao=this.#Zi.writable.getWriter(),this.#Zi.readable.pipeTo(new WritableStream({write:e}))}async addBase64Chunk(e){const t=window.atob(e),n=Uint8Array.from(t,(e=>e.codePointAt(0)));await this.#ao.ready,await this.#ao.write(n)}}var rs=Object.freeze({__proto__:null,ServerSentEventsParser:ts});class ss{#lo;#do;#co=0;#ho=[];constructor(e,t){this.#lo=e,t&&(this.#co=e.pseudoWallTime(e.startTime),this.#do=new ts(this.#uo.bind(this),e.charset()??void 0),this.#lo.requestStreamingContent().then((t=>{n.StreamingContentData.isError(t)||(this.#do?.addBase64Chunk(t.content().base64),t.addEventListener("ChunkAdded",(({data:{chunk:t}})=>{this.#co=e.pseudoWallTime(e.endTime),this.#do?.addBase64Chunk(t)})))})))}get eventSourceMessages(){return this.#ho}onProtocolEventSourceMessageReceived(e,t,n,r){this.#go({eventName:e,eventId:n,data:t,time:r})}#uo(e,t,n){this.#go({eventName:e,eventId:n,data:t,time:this.#co})}#go(e){this.#ho.push(e),this.#lo.dispatchEventToListeners(ps.EventSourceMessageAdded,e)}}const is={deprecatedSyntaxFoundPleaseUse:"Deprecated syntax found. Please use: <name>;dur=<duration>;desc=<description>",duplicateParameterSIgnored:'Duplicate parameter "{PH1}" ignored.',noValueFoundForParameterS:'No value found for parameter "{PH1}".',unrecognizedParameterS:'Unrecognized parameter "{PH1}".',extraneousTrailingCharacters:"Extraneous trailing characters.",unableToParseSValueS:'Unable to parse "{PH1}" value "{PH2}".'},os=r.i18n.registerUIStrings("core/sdk/ServerTiming.ts",is),as=r.i18n.getLocalizedString.bind(void 0,os);class ls{metric;value;description;constructor(e,t,n){this.metric=e,this.value=t,this.description=n}static parseHeaders(e){const t=e.filter((e=>"server-timing"===e.name.toLowerCase()));if(!t.length)return null;const n=t.reduce(((e,t)=>{const n=this.createFromHeaderValue(t.value);return e.push(...n.map((function(e){return new ls(e.name,e.hasOwnProperty("dur")?e.dur:null,e.hasOwnProperty("desc")?e.desc:"")}))),e}),[]);return n.sort(((e,t)=>s.StringUtilities.compare(e.metric.toLowerCase(),t.metric.toLowerCase()))),n}static createFromHeaderValue(e){function t(){e=e.replace(/^\s*/,"")}function n(n){return console.assert(1===n.length),t(),e.charAt(0)===n&&(e=e.substring(1),!0)}function r(){const t=/^(?:\s*)([\w!#$%&'*+\-.^`|~]+)(?:\s*)(.*)/.exec(e);return t?(e=t[2],t[1]):null}function s(){return t(),'"'===e.charAt(0)?function(){console.assert('"'===e.charAt(0)),e=e.substring(1);let t="";for(;e.length;){const n=/^([^"\\]*)(.*)/.exec(e);if(!n)return null;if(t+=n[1],'"'===n[2].charAt(0))return e=n[2].substring(1),t;console.assert("\\"===n[2].charAt(0)),t+=n[2].charAt(1),e=n[2].substring(2)}return null}():r()}function i(){const t=/([,;].*)/.exec(e);t&&(e=t[1])}const o=[];let a;for(;null!==(a=r());){const t={name:a};for("="===e.charAt(0)&&this.showWarning(as(is.deprecatedSyntaxFoundPleaseUse));n(";");){let e;if(null===(e=r()))continue;e=e.toLowerCase();const o=this.getParserForParameter(e);let a=null;if(n("=")&&(a=s(),i()),o){if(t.hasOwnProperty(e)){this.showWarning(as(is.duplicateParameterSIgnored,{PH1:e}));continue}null===a&&this.showWarning(as(is.noValueFoundForParameterS,{PH1:e})),o.call(this,t,a)}else this.showWarning(as(is.unrecognizedParameterS,{PH1:e}))}if(o.push(t),!n(","))break}return e.length&&this.showWarning(as(is.extraneousTrailingCharacters)),o}static getParserForParameter(e){switch(e){case"dur":{function t(t,n){if(t.dur=0,null!==n){const r=parseFloat(n);if(isNaN(r))return void ls.showWarning(as(is.unableToParseSValueS,{PH1:e,PH2:n}));t.dur=r}}return t}case"desc":{function n(e,t){e.desc=t||""}return n}default:return null}}static showWarning(t){e.Console.Console.instance().warn(`ServerTiming: ${t}`)}}var ds=Object.freeze({__proto__:null,ServerTiming:ls});const cs={binary:"(binary)",secureOnly:'This cookie was blocked because it had the "`Secure`" attribute and the connection was not secure.',notOnPath:"This cookie was blocked because its path was not an exact match for or a superdirectory of the request url's path.",domainMismatch:"This cookie was blocked because neither did the request URL's domain exactly match the cookie's domain, nor was the request URL's domain a subdomain of the cookie's Domain attribute value.",sameSiteStrict:'This cookie was blocked because it had the "`SameSite=Strict`" attribute and the request was made from a different site. This includes top-level navigation requests initiated by other sites.',sameSiteLax:'This cookie was blocked because it had the "`SameSite=Lax`" attribute and the request was made from a different site and was not initiated by a top-level navigation.',sameSiteUnspecifiedTreatedAsLax:'This cookie didn\'t specify a "`SameSite`" attribute when it was stored and was defaulted to "SameSite=Lax," and was blocked because the request was made from a different site and was not initiated by a top-level navigation. The cookie had to have been set with "`SameSite=None`" to enable cross-site usage.',sameSiteNoneInsecure:'This cookie was blocked because it had the "`SameSite=None`" attribute but was not marked "Secure". Cookies without SameSite restrictions must be marked "Secure" and sent over a secure connection.',userPreferences:"This cookie was blocked due to user preferences.",thirdPartyPhaseout:"This cookie was blocked due to third-party cookie phaseout. Learn more in the Issues tab.",unknownError:"An unknown error was encountered when trying to send this cookie.",schemefulSameSiteStrict:'This cookie was blocked because it had the "`SameSite=Strict`" attribute but the request was cross-site. This includes top-level navigation requests initiated by other sites. This request is considered cross-site because the URL has a different scheme than the current site.',schemefulSameSiteLax:'This cookie was blocked because it had the "`SameSite=Lax`" attribute but the request was cross-site and was not initiated by a top-level navigation. This request is considered cross-site because the URL has a different scheme than the current site.',schemefulSameSiteUnspecifiedTreatedAsLax:'This cookie didn\'t specify a "`SameSite`" attribute when it was stored, was defaulted to "`SameSite=Lax"`, and was blocked because the request was cross-site and was not initiated by a top-level navigation. This request is considered cross-site because the URL has a different scheme than the current site.',samePartyFromCrossPartyContext:"This cookie was blocked because it had the \"`SameParty`\" attribute but the request was cross-party. The request was considered cross-party because the domain of the resource's URL and the domains of the resource's enclosing frames/documents are neither owners nor members in the same First-Party Set.",nameValuePairExceedsMaxSize:"This cookie was blocked because it was too large. The combined size of the name and value must be less than or equal to 4096 characters.",thisSetcookieWasBlockedDueToUser:"This attempt to set a cookie via a `Set-Cookie` header was blocked due to user preferences.",thisSetcookieWasBlockedDueThirdPartyPhaseout:"Setting this cookie was blocked due to third-party cookie phaseout. Learn more in the Issues tab.",thisSetcookieHadInvalidSyntax:"This `Set-Cookie` header had invalid syntax.",thisSetcookieHadADisallowedCharacter:"This `Set-Cookie` header contained a disallowed character (a forbidden ASCII control character, or the tab character if it appears in the middle of the cookie name, value, an attribute name, or an attribute value).",theSchemeOfThisConnectionIsNot:"The scheme of this connection is not allowed to store cookies.",anUnknownErrorWasEncounteredWhenTrying:"An unknown error was encountered when trying to store this cookie.",thisSetcookieWasBlockedBecauseItHadTheSamesiteStrictLax:'This attempt to set a cookie via a `Set-Cookie` header was blocked because it had the "{PH1}" attribute but came from a cross-site response which was not the response to a top-level navigation. This response is considered cross-site because the URL has a different scheme than the current site.',thisSetcookieDidntSpecifyASamesite:'This `Set-Cookie` header didn\'t specify a "`SameSite`" attribute, was defaulted to "`SameSite=Lax"`, and was blocked because it came from a cross-site response which was not the response to a top-level navigation. This response is considered cross-site because the URL has a different scheme than the current site.',thisSetcookieWasBlockedBecauseItHadTheSameparty:"This attempt to set a cookie via a `Set-Cookie` header was blocked because it had the \"`SameParty`\" attribute but the request was cross-party. The request was considered cross-party because the domain of the resource's URL and the domains of the resource's enclosing frames/documents are neither owners nor members in the same First-Party Set.",thisSetcookieWasBlockedBecauseItHadTheSamepartyAttribute:'This attempt to set a cookie via a `Set-Cookie` header was blocked because it had the "`SameParty`" attribute but also had other conflicting attributes. Chrome requires cookies that use the "`SameParty`" attribute to also have the "Secure" attribute, and to not be restricted to "`SameSite=Strict`".',blockedReasonSecureOnly:'This attempt to set a cookie via a `Set-Cookie` header was blocked because it had the "Secure" attribute but was not received over a secure connection.',blockedReasonSameSiteStrictLax:'This attempt to set a cookie via a `Set-Cookie` header was blocked because it had the "{PH1}" attribute but came from a cross-site response which was not the response to a top-level navigation.',blockedReasonSameSiteUnspecifiedTreatedAsLax:'This `Set-Cookie` header didn\'t specify a "`SameSite`" attribute and was defaulted to "`SameSite=Lax,`" and was blocked because it came from a cross-site response which was not the response to a top-level navigation. The `Set-Cookie` had to have been set with "`SameSite=None`" to enable cross-site usage.',blockedReasonSameSiteNoneInsecure:'This attempt to set a cookie via a `Set-Cookie` header was blocked because it had the "`SameSite=None`" attribute but did not have the "Secure" attribute, which is required in order to use "`SameSite=None`".',blockedReasonOverwriteSecure:"This attempt to set a cookie via a `Set-Cookie` header was blocked because it was not sent over a secure connection and would have overwritten a cookie with the Secure attribute.",blockedReasonInvalidDomain:"This attempt to set a cookie via a `Set-Cookie` header was blocked because its Domain attribute was invalid with regards to the current host url.",blockedReasonInvalidPrefix:'This attempt to set a cookie via a `Set-Cookie` header was blocked because it used the "`__Secure-`" or "`__Host-`" prefix in its name and broke the additional rules applied to cookies with these prefixes as defined in `https://tools.ietf.org/html/draft-west-cookie-prefixes-05`.',thisSetcookieWasBlockedBecauseTheNameValuePairExceedsMaxSize:"This attempt to set a cookie via a `Set-Cookie` header was blocked because the cookie was too large. The combined size of the name and value must be less than or equal to 4096 characters.",setcookieHeaderIsIgnoredIn:"Set-Cookie header is ignored in response from url: {PH1}. The combined size of the name and value must be less than or equal to 4096 characters.",exemptionReasonUserSetting:"This cookie is allowed by user preference.",exemptionReasonTPCDMetadata:"This cookie is allowed by a third-party cookie deprecation trial grace period. Learn more: goo.gle/dt-grace.",exemptionReasonTPCDDeprecationTrial:"This cookie is allowed by third-party cookie phaseout deprecation trial. Learn more: goo.gle/ps-dt.",exemptionReasonTPCDHeuristics:"This cookie is allowed by third-party cookie phaseout heuristics. Learn more: goo.gle/hbe",exemptionReasonEnterprisePolicy:"This cookie is allowed by Chrome Enterprise policy. Learn more: goo.gle/ce-3pc",exemptionReasonStorageAccessAPI:"This cookie is allowed by the Storage Access API. Learn more: goo.gle/saa",exemptionReasonTopLevelStorageAccessAPI:"This cookie is allowed by the top-level Storage Access API. Learn more: goo.gle/saa-top",exemptionReasonCorsOptIn:"This cookie is allowed by CORS opt-in. Learn more: goo.gle/cors",exemptionReasonScheme:"This cookie is allowed by the top-level url scheme"},hs=r.i18n.registerUIStrings("core/sdk/NetworkRequest.ts",cs),us=r.i18n.getLocalizedString.bind(void 0,hs);class gs extends e.ObjectWrapper.ObjectWrapper{#po;#mo;#Ys;#Zs;#ei;#fo;#bo;#yo;#Io;#vo;#ko;#So;#wo;#Co;#Ro;#xo;#To;statusCode;statusText;requestMethod;requestTime;protocol;alternateProtocolUsage;mixedContentType;#Mo;#Po;#Lo;#Eo;#Ao;#Oo;#Do;#No;#Fo;#Bo;#Ho;#Uo;#_o;#qo;#zo;#jo;#Vo;#Wo;#Go;#Ko;#Qo;connectionId;connectionReused;hasNetworkData;#$o;#Xo;#Jo;#Yo;#Zo;#ea;#ta;#na;#ra;#sa;#ia;localizedFailDescription;#Js;#oa;#aa;#ue;#la;#da;#ca;#ni;#ha;#oi;#h;#ua;#ga;#pa;#ma;#fa;#ba;#ya;#Ia;#va;#ka;#Sa;#wa;#Ca;#Ra;#xa;#Ta;#Ma;#Pa;#La;#Ea;#Aa;#Oa;#Da;#Na;#Fa;#Ba;#Ha;#Ua=new Map;#_a;#qa;#za;responseReceivedPromise;responseReceivedPromiseResolve;constructor(t,n,r,s,i,o,a,l){super(),this.#po=t,this.#mo=n,this.setUrl(r),this.#Ys=s,this.#Zs=i,this.#ei=o,this.#bo=a,this.#fo=l,this.#yo=null,this.#Io=null,this.#vo=null,this.#ko=!1,this.#So=null,this.#wo=-1,this.#Co=-1,this.#Ro=-1,this.#xo=void 0,this.#To=void 0,this.statusCode=0,this.statusText="",this.requestMethod="",this.requestTime=0,this.protocol="",this.alternateProtocolUsage=void 0,this.mixedContentType="none",this.#Mo=null,this.#Po=null,this.#Lo=null,this.#Eo=null,this.#Ao=null,this.#Oo=e.ResourceType.resourceTypes.Other,this.#Do=null,this.#No=null,this.#Fo=[],this.#Bo={},this.#Ho="",this.#Uo=[],this.#qo=[],this.#zo=[],this.#jo={},this.#Vo="",this.#Wo="Unknown",this.#Go=null,this.#Ko="unknown",this.#Qo=null,this.connectionId="0",this.connectionReused=!1,this.hasNetworkData=!1,this.#$o=null,this.#Xo=Promise.resolve(null),this.#Jo=!1,this.#Yo=!1,this.#Zo=[],this.#ea=[],this.#ta=[],this.#na=[],this.#ia=!1,this.#ra=null,this.#sa=null,this.localizedFailDescription=null,this.#Ba=null,this.#Ha=!1,this.#_a=!1,this.#qa=!1}static create(e,t,n,r,s,i,o){return new gs(e,e,t,n,r,s,i,o)}static createForWebSocket(e,t,n){return new gs(e,e,t,s.DevToolsPath.EmptyUrlString,null,null,n||null)}static createWithoutBackendRequest(e,t,n,r){return new gs(e,void 0,t,n,null,null,r)}identityCompare(e){const t=this.requestId(),n=e.requestId();return t>n?1:t<n?-1:0}requestId(){return this.#po}backendRequestId(){return this.#mo}url(){return this.#Js}isBlobRequest(){return e.ParsedURL.schemeIs(this.#Js,"blob:")}setUrl(t){this.#Js!==t&&(this.#Js=t,this.#oi=new e.ParsedURL.ParsedURL(t),this.#Da=void 0,this.#Na=void 0,this.#h=void 0,this.#ua=void 0)}get documentURL(){return this.#Ys}get parsedURL(){return this.#oi}get frameId(){return this.#Zs}get loaderId(){return this.#ei}setRemoteAddress(e,t){this.#Vo=e+":"+t,this.dispatchEventToListeners(ps.RemoteAddressChanged,this)}remoteAddress(){return this.#Vo}remoteAddressSpace(){return this.#Wo}getResponseCacheStorageCacheName(){return this.#fa}setResponseCacheStorageCacheName(e){this.#fa=e}serviceWorkerResponseSource(){return this.#ba}setServiceWorkerResponseSource(e){this.#ba=e}setReferrerPolicy(e){this.#Go=e}referrerPolicy(){return this.#Go}securityState(){return this.#Ko}setSecurityState(e){this.#Ko=e}securityDetails(){return this.#Qo}securityOrigin(){return this.#oi.securityOrigin()}setSecurityDetails(e){this.#Qo=e}get startTime(){return this.#Co||-1}setIssueTime(e,t){this.#wo=e,this.#ya=t,this.#Co=e}issueTime(){return this.#wo}pseudoWallTime(e){return this.#ya?this.#ya-this.#wo+e:e}get responseReceivedTime(){return this.#oa||-1}set responseReceivedTime(e){this.#oa=e}getResponseRetrievalTime(){return this.#Ia}setResponseRetrievalTime(e){this.#Ia=e}get endTime(){return this.#Ro||-1}set endTime(e){this.timing&&this.timing.requestTime?this.#Ro=Math.max(e,this.responseReceivedTime):(this.#Ro=e,this.#oa>e&&(this.#oa=e)),this.dispatchEventToListeners(ps.TimingChanged,this)}get duration(){return-1===this.#Ro||-1===this.#Co?-1:this.#Ro-this.#Co}get latency(){return-1===this.#oa||-1===this.#Co?-1:this.#oa-this.#Co}get resourceSize(){return this.#va||0}set resourceSize(e){this.#va=e}get transferSize(){return this.#aa||0}increaseTransferSize(e){this.#aa=(this.#aa||0)+e}setTransferSize(e){this.#aa=e}get finished(){return this.#ue}set finished(e){this.#ue!==e&&(this.#ue=e,e&&this.dispatchEventToListeners(ps.FinishedLoading,this))}get failed(){return this.#la}set failed(e){this.#la=e}get canceled(){return this.#da}set canceled(e){this.#da=e}get preserved(){return this.#ca}set preserved(e){this.#ca=e}blockedReason(){return this.#xo}setBlockedReason(e){this.#xo=e}corsErrorStatus(){return this.#To}setCorsErrorStatus(e){this.#To=e}wasBlocked(){return Boolean(this.#xo)}cached(){return(Boolean(this.#ka)||Boolean(this.#Sa))&&!this.#aa}cachedInMemory(){return Boolean(this.#ka)&&!this.#aa}fromPrefetchCache(){return Boolean(this.#wa)}setFromMemoryCache(){this.#ka=!0,this.#Ta=void 0}get fromDiskCache(){return this.#Sa}setFromDiskCache(){this.#Sa=!0}setFromPrefetchCache(){this.#wa=!0}fromEarlyHints(){return Boolean(this.#Ca)}setFromEarlyHints(){this.#Ca=!0}get fetchedViaServiceWorker(){return Boolean(this.#Ra)}set fetchedViaServiceWorker(e){this.#Ra=e}get serviceWorkerRouterInfo(){return this.#xa}set serviceWorkerRouterInfo(e){this.#xa=e}initiatedByServiceWorker(){const e=X.forRequest(this);return!!e&&e.target().type()===B.ServiceWorker}get timing(){return this.#Ta}set timing(e){if(!e||this.#ka)return;this.#Co=e.requestTime;const t=e.requestTime+e.receiveHeadersEnd/1e3;((this.#oa||-1)<0||this.#oa>t)&&(this.#oa=t),this.#Co>this.#oa&&(this.#oa=this.#Co),this.#Ta=e,this.dispatchEventToListeners(ps.TimingChanged,this)}setConnectTimingFromExtraInfo(e){this.#Co=e.requestTime,this.dispatchEventToListeners(ps.TimingChanged,this)}get mimeType(){return this.#ni}set mimeType(t){if(this.#ni=t,"text/event-stream"===t&&!this.#za){const t=this.resourceType()!==e.ResourceType.resourceTypes.EventSource;this.#za=new ss(this,t)}}get displayName(){return this.#oi.displayName}name(){return this.#h||this.parseNameAndPathFromURL(),this.#h}path(){return this.#ua||this.parseNameAndPathFromURL(),this.#ua}parseNameAndPathFromURL(){if(this.#oi.isDataURL())this.#h=this.#oi.dataURLDisplayName(),this.#ua="";else if(this.#oi.isBlobURL())this.#h=this.#oi.url,this.#ua="";else if(this.#oi.isAboutBlank())this.#h=this.#oi.url,this.#ua="";else{this.#ua=this.#oi.host+this.#oi.folderPathComponents;const t=X.forRequest(this),n=t?e.ParsedURL.ParsedURL.fromString(t.target().inspectedURL()):null;this.#ua=s.StringUtilities.trimURL(this.#ua,n?n.host:""),this.#oi.lastPathComponent||this.#oi.queryParams?this.#h=this.#oi.lastPathComponent+(this.#oi.queryParams?"?"+this.#oi.queryParams:""):this.#oi.folderPathComponents?(this.#h=this.#oi.folderPathComponents.substring(this.#oi.folderPathComponents.lastIndexOf("/")+1)+"/",this.#ua=this.#ua.substring(0,this.#ua.lastIndexOf("/"))):(this.#h=this.#oi.host,this.#ua="")}}get folder(){let e=this.#oi.path;const t=e.indexOf("?");-1!==t&&(e=e.substring(0,t));const n=e.lastIndexOf("/");return-1!==n?e.substring(0,n):""}get pathname(){return this.#oi.path}resourceType(){return this.#Oo}setResourceType(e){this.#Oo=e}get domain(){return this.#oi.host}get scheme(){return this.#oi.scheme}getInferredStatusText(){return this.statusText||(e=this.statusCode,r.i18n.lockedString({100:"Continue",101:"Switching Protocols",102:"Processing",103:"Early Hints",200:"OK",201:"Created",202:"Accepted",203:"Non-Authoritative Information",204:"No Content",205:"Reset Content",206:"Partial Content",207:"Multi-Status",208:"Already Reported",226:"IM Used",300:"Multiple Choices",301:"Moved Permanently",302:"Found",303:"See Other",304:"Not Modified",305:"Use Proxy",307:"Temporary Redirect",308:"Permanent Redirect",400:"Bad Request",401:"Unauthorized",402:"Payment Required",403:"Forbidden",404:"Not Found",405:"Method Not Allowed",406:"Not Acceptable",407:"Proxy Authentication Required",408:"Request Timeout",409:"Conflict",410:"Gone",411:"Length Required",412:"Precondition Failed",413:"Payload Too Large",414:"URI Too Long",415:"Unsupported Media Type",416:"Range Not Satisfiable",417:"Expectation Failed",418:"I'm a teapot",421:"Misdirected Request",422:"Unprocessable Content",423:"Locked",424:"Failed Dependency",425:"Too Early",426:"Upgrade Required",428:"Precondition Required",429:"Too Many Requests",431:"Request Header Fields Too Large",451:"Unavailable For Legal Reasons",500:"Internal Server Error",501:"Not Implemented",502:"Bad Gateway",503:"Service Unavailable",504:"Gateway Timeout",505:"HTTP Version Not Supported",506:"Variant Also Negotiates",507:"Insufficient Storage",508:"Loop Detected",510:"Not Extended",511:"Network Authentication Required"}[e]??""));var e}redirectSource(){return this.#yo}setRedirectSource(e){this.#yo=e}preflightRequest(){return this.#Io}setPreflightRequest(e){this.#Io=e}preflightInitiatorRequest(){return this.#vo}setPreflightInitiatorRequest(e){this.#vo=e}isPreflightRequest(){return null!==this.#bo&&void 0!==this.#bo&&"preflight"===this.#bo.type}redirectDestination(){return this.#So}setRedirectDestination(e){this.#So=e}requestHeaders(){return this.#zo}setRequestHeaders(e){this.#zo=e,this.dispatchEventToListeners(ps.RequestHeadersChanged)}requestHeadersText(){return this.#Ma}setRequestHeadersText(e){this.#Ma=e,this.dispatchEventToListeners(ps.RequestHeadersChanged)}requestHeaderValue(e){return this.#jo[e]||(this.#jo[e]=this.computeHeaderValue(this.requestHeaders(),e)),this.#jo[e]}requestFormData(){return this.#Xo||(this.#Xo=X.requestPostData(this)),this.#Xo}setRequestFormData(e,t){this.#Xo=e&&null===t?null:Promise.resolve(t),this.#$o=null}filteredProtocolName(){const e=this.protocol.toLowerCase();return"h2"===e?"http/2.0":e.replace(/^http\/2(\.0)?\+/,"http/2.0+")}requestHttpVersion(){const e=this.requestHeadersText();if(!e){const e=this.requestHeaderValue("version")||this.requestHeaderValue(":version");return e||this.filteredProtocolName()}const t=e.split(/\r\n/)[0].match(/(HTTP\/\d+\.\d+)$/);return t?t[1]:"HTTP/0.9"}get responseHeaders(){return this.#Pa||[]}set responseHeaders(e){this.#Pa=e,this.#Ea=void 0,this.#Oa=void 0,this.#Aa=void 0,this.#Bo={},this.dispatchEventToListeners(ps.ResponseHeadersChanged)}get earlyHintsHeaders(){return this.#La||[]}set earlyHintsHeaders(e){this.#La=e}get originalResponseHeaders(){return this.#Uo}set originalResponseHeaders(e){this.#Uo=e,this.#_o=void 0}get setCookieHeaders(){return this.#qo}set setCookieHeaders(e){this.#qo=e}get responseHeadersText(){return this.#Ho}set responseHeadersText(e){this.#Ho=e,this.dispatchEventToListeners(ps.ResponseHeadersChanged)}get sortedResponseHeaders(){return void 0!==this.#Ea?this.#Ea:(this.#Ea=this.responseHeaders.slice(),this.#Ea.sort((function(e,t){return s.StringUtilities.compare(e.name.toLowerCase(),t.name.toLowerCase())})))}get sortedOriginalResponseHeaders(){return void 0!==this.#_o?this.#_o:(this.#_o=this.originalResponseHeaders.slice(),this.#_o.sort((function(e,t){return s.StringUtilities.compare(e.name.toLowerCase(),t.name.toLowerCase())})))}get overrideTypes(){const e=[];return this.hasOverriddenContent&&e.push("content"),this.hasOverriddenHeaders()&&e.push("headers"),e}get hasOverriddenContent(){return this.#_a}set hasOverriddenContent(e){this.#_a=e}#ja(e){const t=[];for(const n of e)t.length&&t[t.length-1].name===n.name?t[t.length-1].value+=`, ${n.value}`:t.push({name:n.name,value:n.value});return t}hasOverriddenHeaders(){if(!this.#Uo.length)return!1;const e=this.#ja(this.sortedResponseHeaders),t=this.#ja(this.sortedOriginalResponseHeaders);if(e.length!==t.length)return!0;for(let n=0;n<e.length;n++){if(e[n].name.toLowerCase()!==t[n].name.toLowerCase())return!0;if(e[n].value!==t[n].value)return!0}return!1}responseHeaderValue(e){return e in this.#Bo||(this.#Bo[e]=this.computeHeaderValue(this.responseHeaders,e)),this.#Bo[e]}wasIntercepted(){return this.#Ha}setWasIntercepted(e){this.#Ha=e}setEarlyHintsHeaders(e){this.earlyHintsHeaders=e}get responseCookies(){if(!this.#Aa)if(this.#Aa=Yr.parseSetCookie(this.responseHeaderValue("Set-Cookie"),this.domain)||[],this.#ra)for(const e of this.#Aa)e.partitioned()&&e.setPartitionKey(this.#ra,e.hasCrossSiteAncestor());else if(this.#sa)for(const e of this.#Aa)e.setPartitionKeyOpaque();return this.#Aa}responseLastModified(){return this.responseHeaderValue("last-modified")}allCookiesIncludingBlockedOnes(){return[...this.includedRequestCookies().map((e=>e.cookie)),...this.responseCookies,...this.blockedRequestCookies().map((e=>e.cookie)),...this.blockedResponseCookies().map((e=>e.cookie))].filter((e=>Boolean(e)))}get serverTimings(){return void 0===this.#Oa&&(this.#Oa=ls.parseHeaders(this.responseHeaders)),this.#Oa}queryString(){if(void 0!==this.#Da)return this.#Da;let e=null;const t=this.url(),n=t.indexOf("?");if(-1!==n){e=t.substring(n+1);const r=e.indexOf("#");-1!==r&&(e=e.substring(0,r))}return this.#Da=e,this.#Da}get queryParameters(){if(this.#Na)return this.#Na;const e=this.queryString();return e?(this.#Na=this.parseParameters(e),this.#Na):null}async parseFormParameters(){const e=this.requestContentType();if(!e)return null;if(e.match(/^application\/x-www-form-urlencoded\s*(;.*)?$/i)){const e=await this.requestFormData();return e?this.parseParameters(e):null}const t=e.match(/^multipart\/form-data\s*;\s*boundary\s*=\s*(\S+)\s*$/);if(!t)return null;const n=t[1];if(!n)return null;const r=await this.requestFormData();return r?this.parseMultipartFormDataParameters(r,n):null}formParameters(){return this.#$o||(this.#$o=this.parseFormParameters()),this.#$o}responseHttpVersion(){const e=this.#Ho;if(!e){const e=this.responseHeaderValue("version")||this.responseHeaderValue(":version");return e||this.filteredProtocolName()}const t=e.split(/\r\n/)[0].match(/^(HTTP\/\d+\.\d+)/);return t?t[1]:"HTTP/0.9"}parseParameters(e){return e.split("&").map((function(e){const t=e.indexOf("=");return-1===t?{name:e,value:""}:{name:e.substring(0,t),value:e.substring(t+1)}}))}parseMultipartFormDataParameters(e,t){const n=s.StringUtilities.escapeForRegExp(t),r=new RegExp('^\\r\\ncontent-disposition\\s*:\\s*form-data\\s*;\\s*name="([^"]*)"(?:\\s*;\\s*filename="([^"]*)")?(?:\\r\\ncontent-type\\s*:\\s*([^\\r\\n]*))?\\r\\n\\r\\n(.*)\\r\\n$',"is");return e.split(new RegExp(`--${n}(?:--s*$)?`,"g")).reduce((function(e,t){const[n,s,i,o,a]=t.match(r)||[];if(!n)return e;const l=i||o?us(cs.binary):a;return e.push({name:s,value:l}),e}),[])}computeHeaderValue(e,t){t=t.toLowerCase();const n=[];for(let r=0;r<e.length;++r)e[r].name.toLowerCase()===t&&n.push(e[r].value);if(n.length)return"set-cookie"===t?n.join("\n"):n.join(", ")}requestContentData(){return this.#Do||(this.#Fa?this.#Do=this.#Fa():this.#Do=X.requestContentData(this)),this.#Do}setContentDataProvider(e){console.assert(!this.#Do,"contentData can only be set once."),this.#Fa=e}requestStreamingContent(){if(this.#No)return this.#No;const e=this.finished?this.requestContentData():X.streamResponseBody(this);return this.#No=e.then((e=>n.ContentData.ContentData.isError(e)?e:n.StreamingContentData.StreamingContentData.from(e))),this.#No}contentURL(){return this.#Js}contentType(){return this.#Oo}async requestContent(){return n.ContentData.ContentData.asDeferredContent(await this.requestContentData())}async searchInContent(e,t,r){if(!this.#Fa)return X.searchInRequest(this,e,t,r);const s=await this.requestContentData();return n.ContentData.ContentData.isError(s)||!s.isTextContent?[]:n.TextUtils.performSearchInContent(s.text,e,t,r)}isHttpFamily(){return Boolean(this.url().match(/^https?:/i))}requestContentType(){return this.requestHeaderValue("Content-Type")}hasErrorStatusCode(){return this.statusCode>=400}setInitialPriority(e){this.#Mo=e}initialPriority(){return this.#Mo}setPriority(e){this.#Po=e}priority(){return this.#Po||this.#Mo||null}setSignedExchangeInfo(e){this.#Lo=e}signedExchangeInfo(){return this.#Lo}setWebBundleInfo(e){this.#Eo=e}webBundleInfo(){return this.#Eo}setWebBundleInnerRequestInfo(e){this.#Ao=e}webBundleInnerRequestInfo(){return this.#Ao}async populateImageSource(e){const t=await this.requestContentData();if(n.ContentData.ContentData.isError(t))return;let r=t.asDataUrl();if(null===r&&!this.#la){(this.responseHeaderValue("cache-control")||"").includes("no-cache")||(r=this.#Js)}null!==r&&(e.src=r)}initiator(){return this.#bo||null}hasUserGesture(){return this.#fo??null}frames(){return this.#Fo}addProtocolFrameError(e,t){this.addFrame({type:ms.Error,text:e,time:this.pseudoWallTime(t),opCode:-1,mask:!1})}addProtocolFrame(e,t,n){const r=n?ms.Send:ms.Receive;this.addFrame({type:r,text:e.payloadData,time:this.pseudoWallTime(t),opCode:e.opcode,mask:e.mask})}addFrame(e){this.#Fo.push(e),this.dispatchEventToListeners(ps.WebsocketFrameAdded,e)}eventSourceMessages(){return this.#za?.eventSourceMessages??[]}addEventSourceMessage(e,t,n,r){this.#za?.onProtocolEventSourceMessageReceived(t,r,n,this.pseudoWallTime(e))}markAsRedirect(e){this.#ko=!0,this.#po=`${this.#mo}:redirected.${e}`}isRedirect(){return this.#ko}setRequestIdForTest(e){this.#mo=e,this.#po=e}charset(){return this.#ha??null}setCharset(e){this.#ha=e}addExtraRequestInfo(e){this.#Zo=e.blockedRequestCookies,this.#ea=e.includedRequestCookies,this.setRequestHeaders(e.requestHeaders),this.#Jo=!0,this.setRequestHeadersText(""),this.#ga=e.clientSecurityState,this.setConnectTimingFromExtraInfo(e.connectTiming),this.#ia=e.siteHasCookieInOtherPartition??!1,this.#qa=this.#Zo.some((e=>e.blockedReasons.includes("ThirdPartyPhaseout")))}hasExtraRequestInfo(){return this.#Jo}blockedRequestCookies(){return this.#Zo}includedRequestCookies(){return this.#ea}hasRequestCookies(){return this.#ea.length>0||this.#Zo.length>0}siteHasCookieInOtherPartition(){return this.#ia}static parseStatusTextFromResponseHeadersText(e){return e.split("\r")[0].split(" ").slice(2).join(" ")}addExtraResponseInfo(e){if(this.#ta=e.blockedResponseCookies,e.exemptedResponseCookies&&(this.#na=e.exemptedResponseCookies),this.#ra=e.cookiePartitionKey?.topLevelSite||null,this.#sa=e.cookiePartitionKeyOpaque||null,this.responseHeaders=e.responseHeaders,this.originalResponseHeaders=e.responseHeaders.map((e=>({...e}))),e.responseHeadersText){if(this.responseHeadersText=e.responseHeadersText,!this.requestHeadersText()){let e=`${this.requestMethod} ${this.parsedURL.path}`;this.parsedURL.queryParams&&(e+=`?${this.parsedURL.queryParams}`),e+=" HTTP/1.1\r\n";for(const{name:t,value:n}of this.requestHeaders())e+=`${t}: ${n}\r\n`;this.setRequestHeadersText(e)}this.statusText=gs.parseStatusTextFromResponseHeadersText(e.responseHeadersText)}this.#Wo=e.resourceIPAddressSpace,e.statusCode&&(this.statusCode=e.statusCode),this.#Yo=!0;const t=X.forRequest(this);if(!t)return;for(const e of this.#ta)if(e.blockedReasons.includes("NameValuePairExceedsMaxSize")){const e=us(cs.setcookieHeaderIsIgnoredIn,{PH1:this.url()});t.dispatchEventToListeners(J.MessageGenerated,{message:e,requestId:this.#po,warning:!0})}const n=t.target().model(Xr);if(n){for(const e of this.#na)n.removeBlockedCookie(e.cookie);for(const e of this.#ta){const t=e.cookie;t&&(e.blockedReasons.includes("ThirdPartyPhaseout")&&(this.#qa=!0),n.addBlockedCookie(t,e.blockedReasons.map((e=>({attribute:bs(e),uiString:fs(e)})))))}}}hasExtraResponseInfo(){return this.#Yo}blockedResponseCookies(){return this.#ta}exemptedResponseCookies(){return this.#na}nonBlockedResponseCookies(){const e=this.blockedResponseCookies().map((e=>e.cookieLine));return this.responseCookies.filter((t=>{const n=e.indexOf(t.getCookieLine());return-1===n||(e[n]=null,!1)}))}responseCookiesPartitionKey(){return this.#ra}responseCookiesPartitionKeyOpaque(){return this.#sa}redirectSourceSignedExchangeInfoHasNoErrors(){return null!==this.#yo&&null!==this.#yo.#Lo&&!this.#yo.#Lo.errors}clientSecurityState(){return this.#ga}setTrustTokenParams(e){this.#pa=e}trustTokenParams(){return this.#pa}setTrustTokenOperationDoneEvent(e){this.#ma=e,this.dispatchEventToListeners(ps.TrustTokenResultAdded)}trustTokenOperationDoneEvent(){return this.#ma}setIsSameSite(e){this.#Ba=e}isSameSite(){return this.#Ba}getAssociatedData(e){return this.#Ua.get(e)||null}setAssociatedData(e,t){this.#Ua.set(e,t)}deleteAssociatedData(e){this.#Ua.delete(e)}hasThirdPartyCookiePhaseoutIssue(){return this.#qa}addDataReceivedEvent({timestamp:e,dataLength:t,encodedDataLength:r,data:s}){this.resourceSize+=t,-1!==r&&this.increaseTransferSize(r),this.endTime=e,s&&this.#No?.then((e=>{n.StreamingContentData.isError(e)||e.addChunk(s)}))}waitForResponseReceived(){if(this.responseReceivedPromise)return this.responseReceivedPromise;const{promise:e,resolve:t}=s.PromiseUtilities.promiseWithResolvers();return this.responseReceivedPromise=e,this.responseReceivedPromiseResolve=t,this.responseReceivedPromise}}var ps,ms;!function(e){e.FinishedLoading="FinishedLoading",e.TimingChanged="TimingChanged",e.RemoteAddressChanged="RemoteAddressChanged",e.RequestHeadersChanged="RequestHeadersChanged",e.ResponseHeadersChanged="ResponseHeadersChanged",e.WebsocketFrameAdded="WebsocketFrameAdded",e.EventSourceMessageAdded="EventSourceMessageAdded",e.TrustTokenResultAdded="TrustTokenResultAdded"}(ps||(ps={})),function(e){e.Send="send",e.Receive="receive",e.Error="error"}(ms||(ms={}));const fs=function(e){switch(e){case"SecureOnly":return us(cs.blockedReasonSecureOnly);case"SameSiteStrict":return us(cs.blockedReasonSameSiteStrictLax,{PH1:"SameSite=Strict"});case"SameSiteLax":return us(cs.blockedReasonSameSiteStrictLax,{PH1:"SameSite=Lax"});case"SameSiteUnspecifiedTreatedAsLax":return us(cs.blockedReasonSameSiteUnspecifiedTreatedAsLax);case"SameSiteNoneInsecure":return us(cs.blockedReasonSameSiteNoneInsecure);case"UserPreferences":return us(cs.thisSetcookieWasBlockedDueToUser);case"SyntaxError":return us(cs.thisSetcookieHadInvalidSyntax);case"SchemeNotSupported":return us(cs.theSchemeOfThisConnectionIsNot);case"OverwriteSecure":return us(cs.blockedReasonOverwriteSecure);case"InvalidDomain":return us(cs.blockedReasonInvalidDomain);case"InvalidPrefix":return us(cs.blockedReasonInvalidPrefix);case"UnknownError":return us(cs.anUnknownErrorWasEncounteredWhenTrying);case"SchemefulSameSiteStrict":return us(cs.thisSetcookieWasBlockedBecauseItHadTheSamesiteStrictLax,{PH1:"SameSite=Strict"});case"SchemefulSameSiteLax":return us(cs.thisSetcookieWasBlockedBecauseItHadTheSamesiteStrictLax,{PH1:"SameSite=Lax"});case"SchemefulSameSiteUnspecifiedTreatedAsLax":return us(cs.thisSetcookieDidntSpecifyASamesite);case"SamePartyFromCrossPartyContext":return us(cs.thisSetcookieWasBlockedBecauseItHadTheSameparty);case"SamePartyConflictsWithOtherAttributes":return us(cs.thisSetcookieWasBlockedBecauseItHadTheSamepartyAttribute);case"NameValuePairExceedsMaxSize":return us(cs.thisSetcookieWasBlockedBecauseTheNameValuePairExceedsMaxSize);case"DisallowedCharacter":return us(cs.thisSetcookieHadADisallowedCharacter);case"ThirdPartyPhaseout":return us(cs.thisSetcookieWasBlockedDueThirdPartyPhaseout)}return""},bs=function(e){switch(e){case"SecureOnly":case"OverwriteSecure":return"secure";case"SameSiteStrict":case"SameSiteLax":case"SameSiteUnspecifiedTreatedAsLax":case"SameSiteNoneInsecure":case"SchemefulSameSiteStrict":case"SchemefulSameSiteLax":case"SchemefulSameSiteUnspecifiedTreatedAsLax":return"same-site";case"InvalidDomain":return"domain";case"InvalidPrefix":return"name";case"SamePartyConflictsWithOtherAttributes":case"SamePartyFromCrossPartyContext":case"NameValuePairExceedsMaxSize":case"UserPreferences":case"ThirdPartyPhaseout":case"SyntaxError":case"SchemeNotSupported":case"UnknownError":case"DisallowedCharacter":return null}return null};var ys=Object.freeze({__proto__:null,NetworkRequest:gs,get Events(){return ps},get WebSocketFrameType(){return ms},cookieExemptionReasonToUiString:function(e){switch(e){case"UserSetting":return us(cs.exemptionReasonUserSetting);case"TPCDMetadata":return us(cs.exemptionReasonTPCDMetadata);case"TPCDDeprecationTrial":return us(cs.exemptionReasonTPCDDeprecationTrial);case"TPCDHeuristics":return us(cs.exemptionReasonTPCDHeuristics);case"EnterprisePolicy":return us(cs.exemptionReasonEnterprisePolicy);case"StorageAccess":return us(cs.exemptionReasonStorageAccessAPI);case"TopLevelStorageAccess":return us(cs.exemptionReasonTopLevelStorageAccessAPI);case"CorsOptIn":return us(cs.exemptionReasonCorsOptIn);case"Scheme":return us(cs.exemptionReasonScheme)}return""},cookieBlockedReasonToUiString:function(e){switch(e){case"SecureOnly":return us(cs.secureOnly);case"NotOnPath":return us(cs.notOnPath);case"DomainMismatch":return us(cs.domainMismatch);case"SameSiteStrict":return us(cs.sameSiteStrict);case"SameSiteLax":return us(cs.sameSiteLax);case"SameSiteUnspecifiedTreatedAsLax":return us(cs.sameSiteUnspecifiedTreatedAsLax);case"SameSiteNoneInsecure":return us(cs.sameSiteNoneInsecure);case"UserPreferences":return us(cs.userPreferences);case"UnknownError":return us(cs.unknownError);case"SchemefulSameSiteStrict":return us(cs.schemefulSameSiteStrict);case"SchemefulSameSiteLax":return us(cs.schemefulSameSiteLax);case"SchemefulSameSiteUnspecifiedTreatedAsLax":return us(cs.schemefulSameSiteUnspecifiedTreatedAsLax);case"SamePartyFromCrossPartyContext":return us(cs.samePartyFromCrossPartyContext);case"NameValuePairExceedsMaxSize":return us(cs.nameValuePairExceedsMaxSize);case"ThirdPartyPhaseout":return us(cs.thirdPartyPhaseout)}return""},setCookieBlockedReasonToUiString:fs,cookieBlockedReasonToAttribute:function(e){switch(e){case"SecureOnly":return"secure";case"NotOnPath":return"path";case"DomainMismatch":return"domain";case"SameSiteStrict":case"SameSiteLax":case"SameSiteUnspecifiedTreatedAsLax":case"SameSiteNoneInsecure":case"SchemefulSameSiteStrict":case"SchemefulSameSiteLax":case"SchemefulSameSiteUnspecifiedTreatedAsLax":return"same-site";case"SamePartyFromCrossPartyContext":case"NameValuePairExceedsMaxSize":case"UserPreferences":case"ThirdPartyPhaseout":case"UnknownError":return null}return null},setCookieBlockedReasonToAttribute:bs});class Is{#Va;#C;#Wa;#Ga;#Ka;#Qa;#$a;#h;#zt;#u;#Xa;#Ja;#Ya;#Za;constructor(e,t){this.#Va=e,this.#C=t.nodeId,e.setAXNodeForAXId(this.#C,this),t.backendDOMNodeId?(e.setAXNodeForBackendDOMNodeId(t.backendDOMNodeId,this),this.#Wa=t.backendDOMNodeId,this.#Ga=new Lr(e.target(),t.backendDOMNodeId)):(this.#Wa=null,this.#Ga=null),this.#Ka=t.ignored,this.#Ka&&"ignoredReasons"in t&&(this.#Qa=t.ignoredReasons),this.#$a=t.role||null,this.#h=t.name||null,this.#zt=t.description||null,this.#u=t.value||null,this.#Xa=t.properties||null,this.#Za=[...new Set(t.childIds)]||null,this.#Ja=t.parentId||null,t.frameId&&!t.parentId?(this.#Ya=t.frameId,e.setRootAXNodeForFrameId(t.frameId,this)):this.#Ya=null}id(){return this.#C}accessibilityModel(){return this.#Va}ignored(){return this.#Ka}ignoredReasons(){return this.#Qa||null}role(){return this.#$a||null}coreProperties(){const e=[];return this.#h&&e.push({name:"name",value:this.#h}),this.#zt&&e.push({name:"description",value:this.#zt}),this.#u&&e.push({name:"value",value:this.#u}),e}name(){return this.#h||null}description(){return this.#zt||null}value(){return this.#u||null}properties(){return this.#Xa||null}parentNode(){return this.#Ja?this.#Va.axNodeForId(this.#Ja):null}isDOMNode(){return Boolean(this.#Wa)}backendDOMNodeId(){return this.#Wa}deferredDOMNode(){return this.#Ga}highlightDOMNode(){const e=this.deferredDOMNode();e&&e.highlight()}children(){if(!this.#Za)return[];const e=[];for(const t of this.#Za){const n=this.#Va.axNodeForId(t);n&&e.push(n)}return e}numChildren(){return this.#Za?this.#Za.length:0}hasOnlyUnloadedChildren(){return!(!this.#Za||!this.#Za.length)&&this.#Za.every((e=>null===this.#Va.axNodeForId(e)))}hasUnloadedChildren(){return!(!this.#Za||!this.#Za.length)&&this.#Za.some((e=>null===this.#Va.axNodeForId(e)))}getFrameId(){return this.#Ya||this.parentNode()?.getFrameId()||null}}class vs extends h{agent;#el;#tl;#nl;#rl;#sl;constructor(e){super(e),e.registerAccessibilityDispatcher(this),this.agent=e.accessibilityAgent(),this.resumeModel(),this.#el=new Map,this.#tl=new Map,this.#nl=new Map,this.#rl=new Map,this.#sl=null}clear(){this.#sl=null,this.#el.clear(),this.#tl.clear(),this.#nl.clear()}async resumeModel(){await this.agent.invoke_enable()}async suspendModel(){await this.agent.invoke_disable()}async requestPartialAXTree(e){const{nodes:t}=await this.agent.invoke_getPartialAXTree({nodeId:e.id,fetchRelatives:!0});if(!t)return;const n=[];for(const e of t)n.push(new Is(this,e))}loadComplete({root:e}){this.clear(),this.#sl=new Is(this,e),this.dispatchEventToListeners("TreeUpdated",{root:this.#sl})}nodesUpdated({nodes:e}){this.createNodesFromPayload(e),this.dispatchEventToListeners("TreeUpdated",{})}createNodesFromPayload(e){return e.map((e=>new Is(this,e)))}async requestRootNode(e){if(e&&this.#nl.has(e))return this.#nl.get(e);if(!e&&this.#sl)return this.#sl;const{node:t}=await this.agent.invoke_getRootAXNode({frameId:e});return t?this.createNodesFromPayload([t])[0]:void 0}async requestAXChildren(e,t){const n=this.#el.get(e);if(!n)throw Error("Cannot request children before parent");if(!n.hasUnloadedChildren())return n.children();const r=this.#rl.get(e);if(r)await r;else{const n=this.agent.invoke_getChildAXNodes({id:e,frameId:t});this.#rl.set(e,n);const r=await n;r.getError()||(this.createNodesFromPayload(r.nodes),this.#rl.delete(e))}return n.children()}async requestAndLoadSubTreeToNode(e){const t=[];let n=this.axNodeForDOMNode(e);for(;n;){t.push(n);const e=n.parentNode();if(!e)return t;n=e}const{nodes:r}=await this.agent.invoke_getAXNodeAndAncestors({backendNodeId:e.backendNodeId()});if(!r)return null;return this.createNodesFromPayload(r)}axNodeForId(e){return this.#el.get(e)||null}setRootAXNodeForFrameId(e,t){this.#nl.set(e,t)}axNodeForFrameId(e){return this.#nl.get(e)??null}setAXNodeForAXId(e,t){this.#el.set(e,t)}axNodeForDOMNode(e){return e?this.#tl.get(e.backendNodeId())??null:null}setAXNodeForBackendDOMNodeId(e,t){this.#tl.set(e,t)}getAgent(){return this.agent}}h.register(vs,{capabilities:2,autostart:!1});var ks=Object.freeze({__proto__:null,AccessibilityNode:Is,AccessibilityModel:vs});class Ss extends h{agent;#Kn;constructor(e){super(e),this.agent=e.autofillAgent(),e.registerAutofillDispatcher(this),this.enable()}enable(){this.#Kn||a.InspectorFrontendHost.isUnderTest()||(this.agent.invoke_enable(),this.agent.invoke_setAddresses({addresses:[{fields:[{name:"ADDRESS_HOME_COUNTRY",value:"US"},{name:"NAME_FULL",value:"Jon Stewart Doe"},{name:"NAME_FIRST",value:"Jon"},{name:"NAME_MIDDLE",value:"Stewart"},{name:"NAME_LAST",value:"Doe"},{name:"COMPANY_NAME",value:"Fake Company"},{name:"ADDRESS_HOME_LINE1",value:"1600 Fake Street"},{name:"ADDRESS_HOME_LINE2",value:"Apartment 1"},{name:"ADDRESS_HOME_ZIP",value:"94043"},{name:"ADDRESS_HOME_CITY",value:"Mountain View"},{name:"ADDRESS_HOME_STATE",value:"CA"},{name:"EMAIL_ADDRESS",value:"<EMAIL>"},{name:"PHONE_HOME_WHOLE_NUMBER",value:"+16019521325"}]},{fields:[{name:"ADDRESS_HOME_COUNTRY",value:"BR"},{name:"NAME_FULL",value:"João Souza Silva"},{name:"NAME_FIRST",value:"João"},{name:"NAME_LAST",value:"Souza Silva"},{name:"NAME_LAST_FIRST",value:"Souza"},{name:"NAME_LAST_SECOND",value:"Silva"},{name:"COMPANY_NAME",value:"Empresa Falsa"},{name:"ADDRESS_HOME_STREET_ADDRESS",value:"Rua Inexistente, 2000\nAndar 2, Apartamento 1"},{name:"ADDRESS_HOME_STREET_LOCATION",value:"Rua Inexistente, 2000"},{name:"ADDRESS_HOME_STREET_NAME",value:"Rua Inexistente"},{name:"ADDRESS_HOME_HOUSE_NUMBER",value:"2000"},{name:"ADDRESS_HOME_SUBPREMISE",value:"Andar 2, Apartamento 1"},{name:"ADDRESS_HOME_APT_NUM",value:"1"},{name:"ADDRESS_HOME_FLOOR",value:"2"},{name:"ADDRESS_HOME_APT",value:"Apartamento 1"},{name:"ADDRESS_HOME_APT_TYPE",value:"Apartamento"},{name:"ADDRESS_HOME_APT_NUM",value:"1"},{name:"ADDRESS_HOME_DEPENDENT_LOCALITY",value:"Santa Efigênia"},{name:"ADDRESS_HOME_LANDMARK",value:"Próximo à estação Santa Efigênia"},{name:"ADDRESS_HOME_OVERFLOW",value:"Andar 2, Apartamento 1"},{name:"ADDRESS_HOME_ZIP",value:"30260-080"},{name:"ADDRESS_HOME_CITY",value:"Belo Horizonte"},{name:"ADDRESS_HOME_STATE",value:"MG"},{name:"EMAIL_ADDRESS",value:"<EMAIL>"},{name:"PHONE_HOME_WHOLE_NUMBER",value:"+553121286800"}]},{fields:[{name:"ADDRESS_HOME_COUNTRY",value:"MX"},{name:"NAME_FULL",value:"Juan Francisco García Flores"},{name:"NAME_FIRST",value:"Juan Francisco"},{name:"NAME_LAST",value:"García Flores"},{name:"NAME_LAST_FIRST",value:"García"},{name:"NAME_LAST_SECOND",value:"Flores"},{name:"COMPANY_NAME",value:"Empresa Falsa"},{name:"ADDRESS_HOME_STREET_ADDRESS",value:"C. Falsa 445\nPiso 2, Apartamento 1\nEntre calle Volcán y calle Montes Blancos, cerca de la estación de metro"},{name:"ADDRESS_HOME_STREET_LOCATION",value:"C. Falsa 445"},{name:"ADDRESS_HOME_STREET_NAME",value:"C. Falsa"},{name:"ADDRESS_HOME_HOUSE_NUMBER",value:"445"},{name:"ADDRESS_HOME_SUBPREMISE",value:"Piso 2, Apartamento 1"},{name:"ADDRESS_HOME_FLOOR",value:"2"},{name:"ADDRESS_HOME_APT",value:"Apartamento 1"},{name:"ADDRESS_HOME_APT_TYPE",value:"Apartamento"},{name:"ADDRESS_HOME_APT_NUM",value:"1"},{name:"ADDRESS_HOME_DEPENDENT_LOCALITY",value:"Lomas de Chapultepec"},{name:"ADDRESS_HOME_OVERFLOW",value:"Entre calle Volcán y calle Montes Celestes, cerca de la estación de metro"},{name:"ADDRESS_HOME_BETWEEN_STREETS_OR_LANDMARK",value:"Entre calle Volcán y calle Montes Blancos, cerca de la estación de metro"},{name:"ADDRESS_HOME_LANDMARK",value:"Cerca de la estación de metro"},{name:"ADDRESS_HOME_BETWEEN_STREETS",value:"Entre calle Volcán y calle Montes Blancos"},{name:"ADDRESS_HOME_BETWEEN_STREETS_1",value:"calle Volcán"},{name:"ADDRESS_HOME_BETWEEN_STREETS_2",value:"calle Montes Blancos"},{name:"ADDRESS_HOME_ADMIN_LEVEL2",value:"Miguel Hidalgo"},{name:"ADDRESS_HOME_ZIP",value:"11001"},{name:"ADDRESS_HOME_CITY",value:"Ciudad de México"},{name:"ADDRESS_HOME_STATE",value:"Distrito Federal"},{name:"EMAIL_ADDRESS",value:"<EMAIL>"},{name:"PHONE_HOME_WHOLE_NUMBER",value:"+525553428400"}]},{fields:[{name:"ADDRESS_HOME_COUNTRY",value:"DE"},{name:"NAME_FULL",value:"Gottfried Wilhelm Leibniz"},{name:"NAME_FIRST",value:"Gottfried"},{name:"NAME_MIDDLE",value:"Wilhelm"},{name:"NAME_LAST",value:"Leibniz"},{name:"COMPANY_NAME",value:"Erfundenes Unternehmen"},{name:"ADDRESS_HOME_LINE1",value:"Erfundene Straße 33"},{name:"ADDRESS_HOME_LINE2",value:"Wohnung 1"},{name:"ADDRESS_HOME_ZIP",value:"80732"},{name:"ADDRESS_HOME_CITY",value:"München"},{name:"EMAIL_ADDRESS",value:"<EMAIL>"},{name:"PHONE_HOME_WHOLE_NUMBER",value:"+4930303986300"}]}]}),this.#Kn=!0)}disable(){this.#Kn&&!a.InspectorFrontendHost.isUnderTest()&&(this.#Kn=!1,this.agent.invoke_disable())}addressFormFilled(e){this.dispatchEventToListeners("AddressFormFilled",{autofillModel:this,event:e})}}h.register(Ss,{capabilities:2,autostart:!0});var ws=Object.freeze({__proto__:null,AutofillModel:Ss});class Cs{name;#il;enabledInternal;constructor(e,t){this.#il=e,this.name=t,this.enabledInternal=!1}category(){return this.#il}enabled(){return this.enabledInternal}setEnabled(e){this.enabledInternal=e}}var Rs=Object.freeze({__proto__:null,CategorizedBreakpoint:Cs});class xs{onMessage;#ol;#al;#ll;#Ot;constructor(){this.onMessage=null,this.#ol=null,this.#al="",this.#ll=0,this.#Ot=[a.InspectorFrontendHost.InspectorFrontendHostInstance.events.addEventListener(a.InspectorFrontendHostAPI.Events.DispatchMessage,this.dispatchMessage,this),a.InspectorFrontendHost.InspectorFrontendHostInstance.events.addEventListener(a.InspectorFrontendHostAPI.Events.DispatchMessageChunk,this.dispatchMessageChunk,this)]}setOnMessage(e){this.onMessage=e}setOnDisconnect(e){this.#ol=e}sendRawMessage(e){this.onMessage&&a.InspectorFrontendHost.InspectorFrontendHostInstance.sendMessageToBackend(e)}dispatchMessage(e){this.onMessage&&this.onMessage.call(null,e.data)}dispatchMessageChunk(e){const{messageChunk:t,messageSize:n}=e.data;n&&(this.#al="",this.#ll=n),this.#al+=t,this.#al.length===this.#ll&&this.onMessage&&(this.onMessage.call(null,this.#al),this.#al="",this.#ll=0)}async disconnect(){const t=this.#ol;e.EventTarget.removeEventListeners(this.#Ot),this.#ol=null,this.onMessage=null,t&&t.call(null,"force disconnect")}}class Ts{#dl;onMessage;#ol;#cl;#hl;#ul;constructor(e,t){this.#dl=new WebSocket(e),this.#dl.onerror=this.onError.bind(this),this.#dl.onopen=this.onOpen.bind(this),this.#dl.onmessage=e=>{this.onMessage&&this.onMessage.call(null,e.data)},this.#dl.onclose=this.onClose.bind(this),this.onMessage=null,this.#ol=null,this.#cl=t,this.#hl=!1,this.#ul=[]}setOnMessage(e){this.onMessage=e}setOnDisconnect(e){this.#ol=e}onError(){this.#cl&&this.#cl.call(null),this.#ol&&this.#ol.call(null,"connection failed"),this.close()}onOpen(){if(this.#hl=!0,this.#dl){this.#dl.onerror=console.error;for(const e of this.#ul)this.#dl.send(e)}this.#ul=[]}onClose(){this.#cl&&this.#cl.call(null),this.#ol&&this.#ol.call(null,"websocket closed"),this.close()}close(e){this.#dl&&(this.#dl.onerror=null,this.#dl.onopen=null,this.#dl.onclose=e||null,this.#dl.onmessage=null,this.#dl.close(),this.#dl=null),this.#cl=null}sendRawMessage(e){this.#hl&&this.#dl?this.#dl.send(e):this.#ul.push(e)}disconnect(){return new Promise((e=>{this.close((()=>{this.#ol&&this.#ol.call(null,"force disconnect"),e()}))}))}}class Ms{onMessage;#ol;constructor(){this.onMessage=null,this.#ol=null}setOnMessage(e){this.onMessage=e}setOnDisconnect(e){this.#ol=e}sendRawMessage(e){window.setTimeout(this.respondWithError.bind(this,e),0)}respondWithError(e){const t=JSON.parse(e),n={message:"This is a stub connection, can't dispatch message.",code:l.InspectorBackend.DevToolsStubErrorCode,data:t};this.onMessage&&this.onMessage.call(null,{id:t.id,error:n})}async disconnect(){this.#ol&&this.#ol.call(null,"force disconnect"),this.#ol=null,this.onMessage=null}}class Ps{#gl;#pl;onMessage;#ol;constructor(e,t){this.#gl=e,this.#pl=t,this.onMessage=null,this.#ol=null}setOnMessage(e){this.onMessage=e}setOnDisconnect(e){this.#ol=e}getOnDisconnect(){return this.#ol}sendRawMessage(e){const t=JSON.parse(e);t.sessionId||(t.sessionId=this.#pl),this.#gl.sendRawMessage(JSON.stringify(t))}getSessionId(){return this.#pl}async disconnect(){this.#ol&&this.#ol.call(null,"force disconnect"),this.#ol=null,this.onMessage=null}}function Ls(e){const t=o.Runtime.Runtime.queryParam("ws"),n=o.Runtime.Runtime.queryParam("wss");if(t||n){const r=t?"ws":"wss";let s=t||n;a.InspectorFrontendHost.InspectorFrontendHostInstance.isHostedMode()&&s.startsWith("/")&&(s=`${window.location.host}${s}`);return new Ts(`${r}://${s}`,e)}return a.InspectorFrontendHost.InspectorFrontendHostInstance.isHostedMode()?new Ms:new xs}var Es=Object.freeze({__proto__:null,MainConnection:xs,WebSocketConnection:Ts,StubConnection:Ms,ParallelConnection:Ps,initMainConnection:async function(e,t){l.InspectorBackend.Connection.setFactory(Ls.bind(null,t)),await e(),a.InspectorFrontendHost.InspectorFrontendHostInstance.connectionReady()}});const As={main:"Main"},Os=r.i18n.registerUIStrings("core/sdk/ChildTargetManager.ts",As),Ds=r.i18n.getLocalizedString.bind(void 0,Os);class Ns extends h{#ml;#fl;#bl;#yl=new Map;#Il=new Map;#vl=new Map;#kl=new Map;#Sl=null;constructor(e){super(e),this.#ml=e.targetManager(),this.#fl=e,this.#bl=e.targetAgent(),e.registerTargetDispatcher(this);const t=this.#ml.browserTarget();t?t!==e&&t.targetAgent().invoke_autoAttachRelated({targetId:e.id(),waitForDebuggerOnStart:!0}):this.#bl.invoke_setAutoAttach({autoAttach:!0,waitForDebuggerOnStart:!0,flatten:!0}),e.parentTarget()?.type()===B.Frame||a.InspectorFrontendHost.isUnderTest()||(this.#bl.invoke_setDiscoverTargets({discover:!0}),this.#bl.invoke_setRemoteLocations({locations:[{host:"localhost",port:9229}]}))}static install(e){Ns.attachCallback=e,h.register(Ns,{capabilities:32,autostart:!0})}childTargets(){return Array.from(this.#Il.values())}async suspendModel(){await this.#bl.invoke_setAutoAttach({autoAttach:!0,waitForDebuggerOnStart:!1,flatten:!0})}async resumeModel(){await this.#bl.invoke_setAutoAttach({autoAttach:!0,waitForDebuggerOnStart:!0,flatten:!0})}dispose(){for(const e of this.#Il.keys())this.detachedFromTarget({sessionId:e,targetId:void 0})}targetCreated({targetInfo:e}){this.#yl.set(e.targetId,e),this.fireAvailableTargetsChanged(),this.dispatchEventToListeners("TargetCreated",e)}targetInfoChanged({targetInfo:e}){this.#yl.set(e.targetId,e);const t=this.#vl.get(e.targetId);if(t)if("prerender"!==t.targetInfo()?.subtype||e.subtype)t.updateTargetInfo(e);else{const n=t.model(Gr);t.updateTargetInfo(e),n&&n.mainFrame&&n.primaryPageChanged(n.mainFrame,"Activation"),t.setName(Ds(As.main))}this.fireAvailableTargetsChanged(),this.dispatchEventToListeners("TargetInfoChanged",e)}targetDestroyed({targetId:e}){this.#yl.delete(e),this.fireAvailableTargetsChanged(),this.dispatchEventToListeners("TargetDestroyed",e)}targetCrashed({targetId:e}){this.#yl.delete(e);const t=this.#vl.get(e);t&&t.dispose("targetCrashed event from CDP"),this.fireAvailableTargetsChanged(),this.dispatchEventToListeners("TargetDestroyed",e)}fireAvailableTargetsChanged(){z.instance().dispatchEventToListeners("AvailableTargetsChanged",[...this.#yl.values()])}async getParentTargetId(){return this.#Sl||(this.#Sl=(await this.#fl.targetAgent().invoke_getTargetInfo({})).targetInfo.targetId),this.#Sl}async getTargetInfo(){return(await this.#fl.targetAgent().invoke_getTargetInfo({})).targetInfo}async attachedToTarget({sessionId:t,targetInfo:n,waitingForDebugger:r}){if(this.#Sl===n.targetId)return;let s=B.Browser,i="";if("worker"===n.type&&n.title&&n.title!==n.url)i=n.title;else if(!["page","iframe","webview"].includes(n.type)){if(["^chrome://print/$","^chrome://file-manager/","^chrome://feedback/","^chrome://.*\\.top-chrome/$","^chrome://view-cert/$","^devtools://"].some((e=>n.url.match(e))))s=B.Frame;else{const t=e.ParsedURL.ParsedURL.fromString(n.url);i=t?t.lastPathComponentWithFragment():"#"+ ++Ns.lastAnonymousTargetId}}"iframe"===n.type||"webview"===n.type||"background_page"===n.type||"app"===n.type||"popup_page"===n.type||"page"===n.type?s=B.Frame:"worker"===n.type?s=B.Worker:"worklet"===n.type?s=B.Worklet:"shared_worker"===n.type?s=B.SharedWorker:"shared_storage_worklet"===n.type?s=B.SharedStorageWorklet:"service_worker"===n.type?s=B.ServiceWorker:"auction_worklet"===n.type&&(s=B.AuctionWorklet);const o=this.#ml.createTarget(n.targetId,i,s,this.#fl,t,void 0,void 0,n);this.#Il.set(t,o),this.#vl.set(o.id(),o),Ns.attachCallback&&await Ns.attachCallback({target:o,waitingForDebugger:r}),r&&o.runtimeAgent().invoke_runIfWaitingForDebugger()}detachedFromTarget({sessionId:e}){if(this.#kl.has(e))this.#kl.delete(e);else{const t=this.#Il.get(e);t&&(t.dispose("target terminated"),this.#Il.delete(e),this.#vl.delete(t.id()))}}receivedMessageFromTarget({}){}async createParallelConnection(e){const t=await this.getParentTargetId(),{connection:n,sessionId:r}=await this.createParallelConnectionAndSessionForTarget(this.#fl,t);return n.setOnMessage(e),this.#kl.set(r,n),{connection:n,sessionId:r}}async createParallelConnectionAndSessionForTarget(e,t){const n=e.targetAgent(),r=e.router(),s=(await n.invoke_attachToTarget({targetId:t,flatten:!0})).sessionId,i=new Ps(r.connection(),s);return r.registerSession(e,s,i),i.setOnDisconnect((()=>{r.unregisterSession(s),n.invoke_detachFromTarget({sessionId:s})})),{connection:i,sessionId:s}}targetInfos(){return Array.from(this.#yl.values())}static lastAnonymousTargetId=0;static attachCallback}var Fs=Object.freeze({__proto__:null,ChildTargetManager:Ns});const Bs={couldNotLoadContentForSS:"Could not load content for {PH1} ({PH2})"},Hs=r.i18n.registerUIStrings("core/sdk/CompilerSourceMappingContentProvider.ts",Bs),Us=r.i18n.getLocalizedString.bind(void 0,Hs);var _s,qs=Object.freeze({__proto__:null,CompilerSourceMappingContentProvider:class{#wl;#Cl;#Rl;constructor(e,t,n){this.#wl=e,this.#Cl=t,this.#Rl=n}contentURL(){return this.#wl}contentType(){return this.#Cl}async requestContent(){const e=await this.requestContentData();return n.ContentData.ContentData.asDeferredContent(e)}async requestContentData(){try{const{content:e}=await en.instance().loadResource(this.#wl,this.#Rl);return new n.ContentData.ContentData(e,!1,this.#Cl.canonicalMimeType())}catch(e){const t=Us(Bs.couldNotLoadContentForSS,{PH1:this.#wl,PH2:e.message});return console.error(t),{error:t}}}async searchInContent(e,t,r){const s=await this.requestContentData();return n.TextUtils.performSearchInContentData(s,e,t,r)}}});!function(e){e.Result="result",e.Command="command",e.System="system",e.QueryObjectResult="queryObjectResult"}(_s||(_s={}));const zs={profileD:"Profile {PH1}"},js=r.i18n.registerUIStrings("core/sdk/CPUProfilerModel.ts",zs),Vs=r.i18n.getLocalizedString.bind(void 0,js);class Ws extends h{#xl;#Tl;#Ml;#Pl;#Ll;#El;registeredConsoleProfileMessages=[];constructor(e){super(e),this.#xl=!1,this.#Tl=1,this.#Ml=new Map,this.#Pl=e.profilerAgent(),this.#Ll=null,e.registerProfilerDispatcher(this),this.#Pl.invoke_enable(),this.#El=e.model(nr)}runtimeModel(){return this.#El.runtimeModel()}debuggerModel(){return this.#El}consoleProfileStarted({id:e,location:t,title:n}){n||(n=Vs(zs.profileD,{PH1:this.#Tl++}),this.#Ml.set(e,n));const r=this.createEventDataFrom(e,t,n);this.dispatchEventToListeners("ConsoleProfileStarted",r)}consoleProfileFinished({id:e,location:t,profile:n,title:r}){r||(r=this.#Ml.get(e),this.#Ml.delete(e));const s={...this.createEventDataFrom(e,t,r),cpuProfile:n};this.registeredConsoleProfileMessages.push(s),this.dispatchEventToListeners("ConsoleProfileFinished",s)}createEventDataFrom(e,t,n){const r=ar.fromPayload(this.#El,t);return{id:this.target().id()+"."+e,scriptLocation:r,title:n||"",cpuProfilerModel:this}}isRecordingProfile(){return this.#xl}startRecording(){this.#xl=!0;return this.#Pl.invoke_setSamplingInterval({interval:100}),this.#Pl.invoke_start()}stopRecording(){return this.#xl=!1,this.#Pl.invoke_stop().then((e=>e.profile||null))}startPreciseCoverage(e,t){this.#Ll=t;return this.#Pl.invoke_startPreciseCoverage({callCount:!1,detailed:e,allowTriggeredUpdates:!0})}async takePreciseCoverage(){const e=await this.#Pl.invoke_takePreciseCoverage();return{timestamp:e&&e.timestamp||0,coverage:e&&e.result||[]}}stopPreciseCoverage(){return this.#Ll=null,this.#Pl.invoke_stopPreciseCoverage()}preciseCoverageDeltaUpdate({timestamp:e,occasion:t,result:n}){this.#Ll&&this.#Ll(e,t,n)}}h.register(Ws,{capabilities:4,autostart:!0});var Gs=Object.freeze({__proto__:null,CPUProfilerModel:Ws});class Ks extends h{#Al;constructor(e){super(e),e.registerLogDispatcher(this),this.#Al=e.logAgent(),this.#Al.invoke_enable(),a.InspectorFrontendHost.isUnderTest()||this.#Al.invoke_startViolationsReport({config:[{name:"longTask",threshold:200},{name:"longLayout",threshold:30},{name:"blockedEvent",threshold:100},{name:"blockedParser",threshold:-1},{name:"handler",threshold:150},{name:"recurringHandler",threshold:50},{name:"discouragedAPIUse",threshold:-1}]})}entryAdded({entry:e}){this.dispatchEventToListeners("EntryAdded",{logModel:this,entry:e})}requestClear(){this.#Al.invoke_clear()}}h.register(Ks,{capabilities:8,autostart:!0});var Qs=Object.freeze({__proto__:null,LogModel:Ks});const $s={navigatedToS:"Navigated to {PH1}",bfcacheNavigation:"Navigation to {PH1} was restored from back/forward cache (see https://web.dev/bfcache/)",profileSStarted:"Profile ''{PH1}'' started.",profileSFinished:"Profile ''{PH1}'' finished.",failedToSaveToTempVariable:"Failed to save to temp variable."},Xs=r.i18n.registerUIStrings("core/sdk/ConsoleModel.ts",$s),Js=r.i18n.getLocalizedString.bind(void 0,Xs);class Ys extends h{#Ol;#Dl;#Nl;#Fl;#Bl;#Hl;#Ul;#_l;constructor(t){super(t),this.#Ol=[],this.#Dl=new s.MapUtilities.Multimap,this.#Nl=new Map,this.#Fl=0,this.#Bl=0,this.#Hl=0,this.#Ul=0,this.#_l=new WeakMap;const n=t.model(Gr);if(!n||n.cachedResourcesLoaded())return void this.initTarget(t);const r=n.addEventListener(Vr.CachedResourcesLoaded,(()=>{e.EventTarget.removeEventListeners([r]),this.initTarget(t)}))}initTarget(e){const t=[],n=e.model(Ws);n&&(t.push(n.addEventListener("ConsoleProfileStarted",this.consoleProfileStarted.bind(this,n))),t.push(n.addEventListener("ConsoleProfileFinished",this.consoleProfileFinished.bind(this,n))));const r=e.model(Gr);r&&e.parentTarget()?.type()!==B.Frame&&t.push(r.addEventListener(Vr.PrimaryPageChanged,this.primaryPageChanged,this));const s=e.model(_n);s&&(t.push(s.addEventListener(Hn.ExceptionThrown,this.exceptionThrown.bind(this,s))),t.push(s.addEventListener(Hn.ExceptionRevoked,this.exceptionRevoked.bind(this,s))),t.push(s.addEventListener(Hn.ConsoleAPICalled,this.consoleAPICalled.bind(this,s))),e.parentTarget()?.type()!==B.Frame&&t.push(s.debuggerModel().addEventListener(ir.GlobalObjectCleared,this.clearIfNecessary,this)),t.push(s.addEventListener(Hn.QueryObjectRequested,this.queryObjectRequested.bind(this,s)))),this.#_l.set(e,t)}targetRemoved(t){const n=t.model(_n);n&&this.#Nl.delete(n),e.EventTarget.removeEventListeners(this.#_l.get(t)||[])}async evaluateCommandInConsole(t,n,r,s){const i=await t.evaluate({expression:r,objectGroup:"console",includeCommandLineAPI:s,silent:!1,returnByValue:!1,generatePreview:!0,replMode:!0,allowUnsafeEvalBlockedByCSP:!1},e.Settings.Settings.instance().moduleSetting("console-user-activation-eval").get(),!1);a.userMetrics.actionTaken(a.UserMetrics.Action.ConsoleEvaluated),"error"in i||(await e.Console.Console.instance().showPromise(),this.dispatchEventToListeners(Zs.CommandEvaluated,{result:i.object,commandMessage:n,exceptionDetails:i.exceptionDetails}))}addCommandMessage(e,t){const n=new ti(e.runtimeModel,"javascript",null,t,{type:_s.Command});return n.setExecutionContextId(e.id),this.addMessage(n),n}addMessage(t){t.setPageLoadSequenceNumber(this.#Ul),t.source===e.Console.FrontendMessageSource.ConsoleAPI&&"clear"===t.type&&this.clearIfNecessary(),this.#Ol.push(t),this.#Dl.set(t.timestamp,t);const n=t.runtimeModel(),r=t.getExceptionId();if(r&&n){let e=this.#Nl.get(n);e||(e=new Map,this.#Nl.set(n,e)),e.set(r,t)}this.incrementErrorWarningCount(t),this.dispatchEventToListeners(Zs.MessageAdded,t)}exceptionThrown(e,t){const n=t.data,r=function(e){if(!e)return;return{requestId:e.requestId||void 0,issueId:e.issueId||void 0}}(n.details.exceptionMetaData),s=ti.fromException(e,n.details,void 0,n.timestamp,void 0,r);s.setExceptionId(n.details.exceptionId),this.addMessage(s)}exceptionRevoked(e,t){const n=t.data,r=this.#Nl.get(e),s=r?r.get(n):null;s&&(this.#Bl--,s.level="verbose",this.dispatchEventToListeners(Zs.MessageUpdated,s))}consoleAPICalled(t,n){const r=n.data;let s="info";"debug"===r.type?s="verbose":"error"===r.type||"assert"===r.type?s="error":"warning"===r.type?s="warning":"info"!==r.type&&"log"!==r.type||(s="info");let i="";r.args.length&&r.args[0].unserializableValue?i=r.args[0].unserializableValue:r.args.length&&("object"!=typeof r.args[0].value&&void 0!==r.args[0].value||null===r.args[0].value)?i=String(r.args[0].value):r.args.length&&r.args[0].description&&(i=r.args[0].description);const o=r.stackTrace&&r.stackTrace.callFrames.length?r.stackTrace.callFrames[0]:null,a={type:r.type,url:o?.url,line:o?.lineNumber,column:o?.columnNumber,parameters:r.args,stackTrace:r.stackTrace,timestamp:r.timestamp,executionContextId:r.executionContextId,context:r.context},l=new ti(t,e.Console.FrontendMessageSource.ConsoleAPI,s,i,a);for(const e of this.#Dl.get(l.timestamp).values())if(l.isEqual(e))return;this.addMessage(l)}queryObjectRequested(t,n){const{objects:r,executionContextId:s}=n.data,i={type:_s.QueryObjectResult,parameters:[r],executionContextId:s},o=new ti(t,e.Console.FrontendMessageSource.ConsoleAPI,"info","",i);this.addMessage(o)}clearIfNecessary(){e.Settings.Settings.instance().moduleSetting("preserve-console-log").get()||this.clear(),++this.#Ul}primaryPageChanged(t){if(e.Settings.Settings.instance().moduleSetting("preserve-console-log").get()){const{frame:n}=t.data;n.backForwardCacheDetails.restoredFromCache?e.Console.Console.instance().log(Js($s.bfcacheNavigation,{PH1:n.url})):e.Console.Console.instance().log(Js($s.navigatedToS,{PH1:n.url}))}}consoleProfileStarted(e,t){const{data:n}=t;this.addConsoleProfileMessage(e,"profile",n.scriptLocation,Js($s.profileSStarted,{PH1:n.title}))}consoleProfileFinished(e,t){const{data:n}=t;this.addConsoleProfileMessage(e,"profileEnd",n.scriptLocation,Js($s.profileSFinished,{PH1:n.title}))}addConsoleProfileMessage(t,n,r,s){const i=r.script(),o=[{functionName:"",scriptId:r.scriptId,url:i?i.contentURL():"",lineNumber:r.lineNumber,columnNumber:r.columnNumber||0}];this.addMessage(new ti(t.runtimeModel(),e.Console.FrontendMessageSource.ConsoleAPI,"info",s,{type:n,stackTrace:{callFrames:o}}))}incrementErrorWarningCount(e){if("violation"!==e.source)switch(e.level){case"warning":this.#Fl++;break;case"error":this.#Bl++}else this.#Hl++}messages(){return this.#Ol}static allMessagesUnordered(){const e=[];for(const t of z.instance().targets()){const n=t.model(Ys)?.messages()||[];e.push(...n)}return e}static requestClearMessages(){for(const e of z.instance().models(Ks))e.requestClear();for(const e of z.instance().models(_n))e.discardConsoleEntries();for(const e of z.instance().targets())e.model(Ys)?.clear()}clear(){this.#Ol=[],this.#Dl.clear(),this.#Nl.clear(),this.#Bl=0,this.#Fl=0,this.#Hl=0,this.dispatchEventToListeners(Zs.ConsoleCleared)}errors(){return this.#Bl}static allErrors(){let e=0;for(const t of z.instance().targets())e+=t.model(Ys)?.errors()||0;return e}warnings(){return this.#Fl}static allWarnings(){let e=0;for(const t of z.instance().targets())e+=t.model(Ys)?.warnings()||0;return e}violations(){return this.#Hl}static allViolations(){let e=0;for(const t of z.instance().targets())e+=t.model(Ys)?.violations()||0;return e}async saveToTempVariable(t,n){if(!n||!t)return void a(null);const r=t,s=await r.globalObject("",!1);if("error"in s||Boolean(s.exceptionDetails)||!s.object)return void a("object"in s&&s.object||null);const i=s.object,o=await i.callFunction((function(e){const t="temp";let n=1;for(;t+n in this;)++n;const r=t+n;return this[r]=e,r}),[Nt.toCallArgument(n)]);if(i.release(),o.wasThrown||!o.object||"string"!==o.object.type)a(o.object||null);else{const e=o.object.value,t=this.addCommandMessage(r,e);this.evaluateCommandInConsole(r,t,e,!1)}function a(t){let n=Js($s.failedToSaveToTempVariable);t&&(n=n+" "+t.description),e.Console.Console.instance().error(n)}o.object&&o.object.release()}}var Zs;function ei(e,t){if(!e!=!t)return!1;if(!e||!t)return!0;const n=e.callFrames,r=t.callFrames;if(n.length!==r.length)return!1;for(let e=0,t=n.length;e<t;++e)if(n[e].scriptId!==r[e].scriptId||n[e].functionName!==r[e].functionName||n[e].lineNumber!==r[e].lineNumber||n[e].columnNumber!==r[e].columnNumber)return!1;return ei(e.parent,t.parent)}!function(e){e.ConsoleCleared="ConsoleCleared",e.MessageAdded="MessageAdded",e.MessageUpdated="MessageUpdated",e.CommandEvaluated="CommandEvaluated"}(Zs||(Zs={}));class ti{#$n;source;level;messageText;type;url;line;column;parameters;stackTrace;timestamp;#ql;scriptId;workerId;context;#zl=null;#Ul=void 0;#jl=void 0;#Vl;category;stackFrameWithBreakpoint=null;#Wl=null;constructor(e,t,n,r,s){if(this.#$n=e,this.source=t,this.level=n,this.messageText=r,this.type=s?.type||"log",this.url=s?.url,this.line=s?.line||0,this.column=s?.column||0,this.parameters=s?.parameters,this.stackTrace=s?.stackTrace,this.timestamp=s?.timestamp||Date.now(),this.#ql=s?.executionContextId||0,this.scriptId=s?.scriptId,this.workerId=s?.workerId,this.#Vl=s?.affectedResources,this.category=s?.category,!this.#ql&&this.#$n&&(this.scriptId?this.#ql=this.#$n.executionContextIdForScriptId(this.scriptId):this.stackTrace&&(this.#ql=this.#$n.executionContextForStackTrace(this.stackTrace))),s?.context){const e=s?.context.match(/[^#]*/);this.context=e?.[0]}if(this.stackTrace){const{callFrame:e,type:t}=ti.#Gl(this.stackTrace);this.stackFrameWithBreakpoint=e,this.#Wl=t}}getAffectedResources(){return this.#Vl}setPageLoadSequenceNumber(e){this.#Ul=e}static fromException(e,t,n,r,s,i){const o={type:n,url:s||t.url,line:t.lineNumber,column:t.columnNumber,parameters:t.exception?[Nt.fromLocalObject(t.text),t.exception]:void 0,stackTrace:t.stackTrace,timestamp:r,executionContextId:t.executionContextId,scriptId:t.scriptId,affectedResources:i};return new ti(e,"javascript","error",_n.simpleTextFromException(t),o)}runtimeModel(){return this.#$n}target(){return this.#$n?this.#$n.target():null}setOriginatingMessage(e){this.#zl=e,this.#ql=e.#ql}originatingMessage(){return this.#zl}setExecutionContextId(e){this.#ql=e}getExecutionContextId(){return this.#ql}getExceptionId(){return this.#jl}setExceptionId(e){this.#jl=e}isGroupMessage(){return"startGroup"===this.type||"startGroupCollapsed"===this.type||"endGroup"===this.type}isGroupStartMessage(){return"startGroup"===this.type||"startGroupCollapsed"===this.type}isErrorOrWarning(){return"warning"===this.level||"error"===this.level}isGroupable(){const t="error"===this.level&&("javascript"===this.source||"network"===this.source);return this.source!==e.Console.FrontendMessageSource.ConsoleAPI&&this.type!==_s.Command&&this.type!==_s.Result&&this.type!==_s.System&&!t}groupCategoryKey(){return[this.source,this.level,this.type,this.#Ul].join(":")}isEqual(e){if(!e)return!1;if(this.parameters){if(!e.parameters||this.parameters.length!==e.parameters.length)return!1;for(let t=0;t<e.parameters.length;++t){const n=e.parameters[t],r=this.parameters[t];if("string"==typeof n||"string"==typeof r)return!1;if("object"===n.type&&"error"!==n.subtype&&(!n.objectId||n.objectId!==r.objectId||e.timestamp!==this.timestamp))return!1;if(r.type!==n.type||r.value!==n.value||r.description!==n.description)return!1}}return this.runtimeModel()===e.runtimeModel()&&this.source===e.source&&this.type===e.type&&this.level===e.level&&this.line===e.line&&this.url===e.url&&this.scriptId===e.scriptId&&this.messageText===e.messageText&&this.#ql===e.#ql&&(t=this.#Vl,n=e.#Vl,t?.requestId===n?.requestId)&&ei(this.stackTrace,e.stackTrace);var t,n}get originatesFromLogpoint(){return"LOGPOINT"===this.#Wl}get originatesFromConditionalBreakpoint(){return"CONDITIONAL_BREAKPOINT"===this.#Wl}static#Gl({callFrames:e}){const t=[gr,ur],n=e.findLastIndex((({url:e})=>t.includes(e)));if(-1===n||n===e.length-1)return{callFrame:null,type:null};const r=e[n].url===ur?"LOGPOINT":"CONDITIONAL_BREAKPOINT";return{callFrame:e[n+1],type:r}}}h.register(Ys,{capabilities:4,autostart:!0});const ni=new Map([["xml","xml"],["javascript","javascript"],["network","network"],[e.Console.FrontendMessageSource.ConsoleAPI,"console-api"],["storage","storage"],["appcache","appcache"],["rendering","rendering"],[e.Console.FrontendMessageSource.CSS,"css"],["security","security"],["deprecation","deprecation"],["worker","worker"],["violation","violation"],["intervention","intervention"],["recommendation","recommendation"],["other","other"],[e.Console.FrontendMessageSource.IssuePanel,"issue-panel"]]);var ri=Object.freeze({__proto__:null,ConsoleModel:Ys,get Events(){return Zs},ConsoleMessage:ti,MessageSourceDisplayName:ni,get FrontendMessageType(){return _s}});class si extends t.CPUProfileDataModel.CPUProfileDataModel{}var ii=Object.freeze({__proto__:null,CPUProfileDataModel:si});class oi extends h{#Kl;#Ql;#rt;#$l;#Xl;#Jl;#Yl;#Zl;#ed;#td;constructor(t){super(t),this.#Kl=t.emulationAgent(),this.#Ql=t.deviceOrientationAgent(),this.#rt=t.model(xn),this.#$l=t.model(wr),this.#$l&&this.#$l.addEventListener("InspectModeWillBeToggled",(()=>{this.updateTouch()}),this);const n=e.Settings.Settings.instance().moduleSetting("java-script-disabled");n.addChangeListener((async()=>await this.#Kl.invoke_setScriptExecutionDisabled({value:n.get()}))),n.get()&&this.#Kl.invoke_setScriptExecutionDisabled({value:!0});const r=e.Settings.Settings.instance().moduleSetting("emulation.touch");r.addChangeListener((()=>{const e=r.get();this.overrideEmulateTouch("force"===e)}));const s=e.Settings.Settings.instance().moduleSetting("emulation.idle-detection");s.addChangeListener((async()=>{const e=s.get();if("none"===e)return void await this.clearIdleOverride();const t=JSON.parse(e);await this.setIdleOverride(t)}));const i=e.Settings.Settings.instance().moduleSetting("emulated-css-media"),o=e.Settings.Settings.instance().moduleSetting("emulated-css-media-feature-color-gamut"),a=e.Settings.Settings.instance().moduleSetting("emulated-css-media-feature-prefers-color-scheme"),l=e.Settings.Settings.instance().moduleSetting("emulated-css-media-feature-forced-colors"),d=e.Settings.Settings.instance().moduleSetting("emulated-css-media-feature-prefers-contrast"),c=e.Settings.Settings.instance().moduleSetting("emulated-css-media-feature-prefers-reduced-data"),h=e.Settings.Settings.instance().moduleSetting("emulated-css-media-feature-prefers-reduced-transparency"),u=e.Settings.Settings.instance().moduleSetting("emulated-css-media-feature-prefers-reduced-motion");this.#Xl=new Map([["type",i.get()],["color-gamut",o.get()],["prefers-color-scheme",a.get()],["forced-colors",l.get()],["prefers-contrast",d.get()],["prefers-reduced-data",c.get()],["prefers-reduced-motion",u.get()],["prefers-reduced-transparency",h.get()]]),i.addChangeListener((()=>{this.#Xl.set("type",i.get()),this.updateCssMedia()})),o.addChangeListener((()=>{this.#Xl.set("color-gamut",o.get()),this.updateCssMedia()})),a.addChangeListener((()=>{this.#Xl.set("prefers-color-scheme",a.get()),this.updateCssMedia()})),l.addChangeListener((()=>{this.#Xl.set("forced-colors",l.get()),this.updateCssMedia()})),d.addChangeListener((()=>{this.#Xl.set("prefers-contrast",d.get()),this.updateCssMedia()})),c.addChangeListener((()=>{this.#Xl.set("prefers-reduced-data",c.get()),this.updateCssMedia()})),u.addChangeListener((()=>{this.#Xl.set("prefers-reduced-motion",u.get()),this.updateCssMedia()})),h.addChangeListener((()=>{this.#Xl.set("prefers-reduced-transparency",h.get()),this.updateCssMedia()})),this.updateCssMedia();const g=e.Settings.Settings.instance().moduleSetting("emulate-auto-dark-mode");g.addChangeListener((()=>{const e=g.get();a.setDisabled(e),a.set(e?"dark":""),this.emulateAutoDarkMode(e)})),g.get()&&(a.setDisabled(!0),a.set("dark"),this.emulateAutoDarkMode(!0));const p=e.Settings.Settings.instance().moduleSetting("emulated-vision-deficiency");p.addChangeListener((()=>this.emulateVisionDeficiency(p.get()))),p.get()&&this.emulateVisionDeficiency(p.get());const m=e.Settings.Settings.instance().moduleSetting("local-fonts-disabled");m.addChangeListener((()=>this.setLocalFontsDisabled(m.get()))),m.get()&&this.setLocalFontsDisabled(m.get());const f=e.Settings.Settings.instance().moduleSetting("avif-format-disabled"),b=e.Settings.Settings.instance().moduleSetting("webp-format-disabled"),y=()=>{const e=[];f.get()&&e.push("avif"),b.get()&&e.push("webp"),this.setDisabledImageTypes(e)};f.addChangeListener(y),b.addChangeListener(y),(f.get()||b.get())&&y(),this.#Zl=!0,this.#Jl=!1,this.#Yl=!1,this.#ed=!1,this.#td={enabled:!1,configuration:"mobile"}}setTouchEmulationAllowed(e){this.#Zl=e}supportsDeviceEmulation(){return this.target().hasAllCapabilities(4096)}async resetPageScaleFactor(){await this.#Kl.invoke_resetPageScaleFactor()}async emulateDevice(e){e?await this.#Kl.invoke_setDeviceMetricsOverride(e):await this.#Kl.invoke_clearDeviceMetricsOverride()}overlayModel(){return this.#$l}async emulateLocation(e){if(e)if(e.unavailable)await Promise.all([this.#Kl.invoke_setGeolocationOverride({}),this.#Kl.invoke_setTimezoneOverride({timezoneId:""}),this.#Kl.invoke_setLocaleOverride({locale:""}),this.#Kl.invoke_setUserAgentOverride({userAgent:ae.instance().currentUserAgent()})]);else{function t(e,t){const n=t.getError();return n?Promise.reject({type:e,message:n}):Promise.resolve()}await Promise.all([this.#Kl.invoke_setGeolocationOverride({latitude:e.latitude,longitude:e.longitude,accuracy:ai.defaultGeoMockAccuracy}).then((e=>t("emulation-set-location",e))),this.#Kl.invoke_setTimezoneOverride({timezoneId:e.timezoneId}).then((e=>t("emulation-set-timezone",e))),this.#Kl.invoke_setLocaleOverride({locale:e.locale}).then((e=>t("emulation-set-locale",e))),this.#Kl.invoke_setUserAgentOverride({userAgent:ae.instance().currentUserAgent(),acceptLanguage:e.locale}).then((e=>t("emulation-set-user-agent",e)))])}else await Promise.all([this.#Kl.invoke_clearGeolocationOverride(),this.#Kl.invoke_setTimezoneOverride({timezoneId:""}),this.#Kl.invoke_setLocaleOverride({locale:""}),this.#Kl.invoke_setUserAgentOverride({userAgent:ae.instance().currentUserAgent()})])}async emulateDeviceOrientation(e){e?await this.#Ql.invoke_setDeviceOrientationOverride({alpha:e.alpha,beta:e.beta,gamma:e.gamma}):await this.#Ql.invoke_clearDeviceOrientationOverride()}async setIdleOverride(e){await this.#Kl.invoke_setIdleOverride(e)}async clearIdleOverride(){await this.#Kl.invoke_clearIdleOverride()}async emulateCSSMedia(e,t){await this.#Kl.invoke_setEmulatedMedia({media:e,features:t}),this.#rt&&this.#rt.mediaQueryResultChanged()}async emulateAutoDarkMode(e){e&&(this.#Xl.set("prefers-color-scheme","dark"),await this.updateCssMedia()),await this.#Kl.invoke_setAutoDarkModeOverride({enabled:e||void 0})}async emulateVisionDeficiency(e){await this.#Kl.invoke_setEmulatedVisionDeficiency({type:e})}setLocalFontsDisabled(e){this.#rt&&this.#rt.setLocalFontsEnabled(!e)}setDisabledImageTypes(e){this.#Kl.invoke_setDisabledImageTypes({imageTypes:e})}async setCPUThrottlingRate(e){await this.#Kl.invoke_setCPUThrottlingRate({rate:e})}async setHardwareConcurrency(e){if(e<1)throw new Error("hardwareConcurrency must be a positive value");await this.#Kl.invoke_setHardwareConcurrencyOverride({hardwareConcurrency:e})}async emulateTouch(e,t){this.#Jl=e&&this.#Zl,this.#Yl=t&&this.#Zl,await this.updateTouch()}async overrideEmulateTouch(e){this.#ed=e&&this.#Zl,await this.updateTouch()}async updateTouch(){let e={enabled:this.#Jl,configuration:this.#Yl?"mobile":"desktop"};this.#ed&&(e={enabled:!0,configuration:"mobile"}),this.#$l&&this.#$l.inspectModeEnabled()&&(e={enabled:!1,configuration:"mobile"}),(this.#td.enabled||e.enabled)&&(this.#td.enabled&&e.enabled&&this.#td.configuration===e.configuration||(this.#td=e,await this.#Kl.invoke_setTouchEmulationEnabled({enabled:e.enabled,maxTouchPoints:1}),await this.#Kl.invoke_setEmitTouchEventsForMouse({enabled:e.enabled,configuration:e.configuration})))}async updateCssMedia(){const e=this.#Xl.get("type")??"",t=[{name:"color-gamut",value:this.#Xl.get("color-gamut")??""},{name:"prefers-color-scheme",value:this.#Xl.get("prefers-color-scheme")??""},{name:"forced-colors",value:this.#Xl.get("forced-colors")??""},{name:"prefers-contrast",value:this.#Xl.get("prefers-contrast")??""},{name:"prefers-reduced-data",value:this.#Xl.get("prefers-reduced-data")??""},{name:"prefers-reduced-motion",value:this.#Xl.get("prefers-reduced-motion")??""},{name:"prefers-reduced-transparency",value:this.#Xl.get("prefers-reduced-transparency")??""}];return this.emulateCSSMedia(e,t)}}class ai{latitude;longitude;timezoneId;locale;unavailable;constructor(e,t,n,r,s){this.latitude=e,this.longitude=t,this.timezoneId=n,this.locale=r,this.unavailable=s}static parseSetting(e){if(e){const[t,n,r,s]=e.split(":"),[i,o]=t.split("@");return new ai(parseFloat(i),parseFloat(o),n,r,Boolean(s))}return new ai(0,0,"","",!1)}static parseUserInput(e,t,n,r){if(!e&&!t)return null;const{valid:s}=ai.latitudeValidator(e),{valid:i}=ai.longitudeValidator(t);if(!s&&!i)return null;const o=s?parseFloat(e):-1,a=i?parseFloat(t):-1;return new ai(o,a,n,r,!1)}static latitudeValidator(e){const t=parseFloat(e);return{valid:/^([+-]?[\d]+(\.\d+)?|[+-]?\.\d+)$/.test(e)&&t>=-90&&t<=90,errorMessage:void 0}}static longitudeValidator(e){const t=parseFloat(e);return{valid:/^([+-]?[\d]+(\.\d+)?|[+-]?\.\d+)$/.test(e)&&t>=-180&&t<=180,errorMessage:void 0}}static timezoneIdValidator(e){return{valid:""===e||/[a-zA-Z]/.test(e),errorMessage:void 0}}static localeValidator(e){return{valid:""===e||/[a-zA-Z]{2}/.test(e),errorMessage:void 0}}toSetting(){return`${this.latitude}@${this.longitude}:${this.timezoneId}:${this.locale}:${this.unavailable||""}`}static defaultGeoMockAccuracy=150}class li{alpha;beta;gamma;constructor(e,t,n){this.alpha=e,this.beta=t,this.gamma=n}static parseSetting(e){if(e){const t=JSON.parse(e);return new li(t.alpha,t.beta,t.gamma)}return new li(0,0,0)}static parseUserInput(e,t,n){if(!e&&!t&&!n)return null;const{valid:r}=li.alphaAngleValidator(e),{valid:s}=li.betaAngleValidator(t),{valid:i}=li.gammaAngleValidator(n);if(!r&&!s&&!i)return null;const o=r?parseFloat(e):-1,a=s?parseFloat(t):-1,l=i?parseFloat(n):-1;return new li(o,a,l)}static angleRangeValidator(e,t){const n=parseFloat(e);return{valid:/^([+-]?[\d]+(\.\d+)?|[+-]?\.\d+)$/.test(e)&&n>=t.minimum&&n<t.maximum,errorMessage:void 0}}static alphaAngleValidator(e){return li.angleRangeValidator(e,{minimum:0,maximum:360})}static betaAngleValidator(e){return li.angleRangeValidator(e,{minimum:-180,maximum:180})}static gammaAngleValidator(e){return li.angleRangeValidator(e,{minimum:-90,maximum:90})}toSetting(){return JSON.stringify(this)}}h.register(oi,{capabilities:256,autostart:!0});var di=Object.freeze({__proto__:null,EmulationModel:oi,Location:ai,DeviceOrientation:li});let ci;class hi extends e.ObjectWrapper.ObjectWrapper{#nd;#rd;#sd;constructor(){super(),this.#nd=ui.NoThrottling,z.instance().observeModels(oi,this)}static instance(e={forceNew:null}){const{forceNew:t}=e;return ci&&!t||(ci=new hi),ci}cpuThrottlingRate(){return this.#nd}setCPUThrottlingRate(e){this.#nd=e;for(const e of z.instance().models(oi))e.setCPUThrottlingRate(this.#nd);this.dispatchEventToListeners("RateChanged",this.#nd)}setHardwareConcurrency(e){this.#rd=e;for(const t of z.instance().models(oi))t.setHardwareConcurrency(e);this.dispatchEventToListeners("HardwareConcurrencyChanged",this.#rd)}hasPrimaryPageTargetSet(){try{return null!==z.instance().primaryPageTarget()}catch{return!1}}async getHardwareConcurrency(){const e=z.instance().primaryPageTarget(),t=this.#sd;if(!e)return new Promise(t?e=>{this.#sd=n=>{e(n),t(n)}}:e=>{this.#sd=e});const n=await e.runtimeAgent().invoke_evaluate({expression:"navigator.hardwareConcurrency",returnByValue:!0,silent:!0,throwOnSideEffect:!0}),r=n.getError();if(r)throw new Error(r);const{result:s,exceptionDetails:i}=n;if(i)throw new Error(i.text);return s.value}modelAdded(e){if(this.#nd!==ui.NoThrottling&&e.setCPUThrottlingRate(this.#nd),void 0!==this.#rd&&e.setHardwareConcurrency(this.#rd),this.#sd){const e=this.#sd;this.#sd=void 0,this.getHardwareConcurrency().then(e)}}modelRemoved(e){}}var ui;!function(e){e[e.NoThrottling=1]="NoThrottling",e[e.MidTierMobile=4]="MidTierMobile",e[e.LowEndMobile=6]="LowEndMobile",e[e.ExtraSlow=20]="ExtraSlow"}(ui||(ui={}));var gi=Object.freeze({__proto__:null,CPUThrottlingManager:hi,throttlingManager:function(){return hi.instance()},get CPUThrottlingRates(){return ui}});class pi extends h{agent;#$n;#Pn;#id;#od;suspended=!1;constructor(t){super(t),this.agent=t.domdebuggerAgent(),this.#$n=t.model(_n),this.#Pn=t.model(Or),this.#Pn.addEventListener(Tr.DocumentUpdated,this.documentUpdated,this),this.#Pn.addEventListener(Tr.NodeRemoved,this.nodeRemoved,this),this.#id=[],this.#od=e.Settings.Settings.instance().createLocalSetting("dom-breakpoints",[]),this.#Pn.existingDocument()&&this.documentUpdated()}runtimeModel(){return this.#$n}async suspendModel(){this.suspended=!0}async resumeModel(){this.suspended=!1}async eventListeners(e){if(console.assert(e.runtimeModel()===this.#$n),!e.objectId)return[];const t=await this.agent.invoke_getEventListeners({objectId:e.objectId}),n=[];for(const r of t.listeners||[]){const t=this.#$n.debuggerModel().createRawLocationByScriptId(r.scriptId,r.lineNumber,r.columnNumber);t&&n.push(new bi(this,e,r.type,r.useCapture,r.passive,r.once,r.handler?this.#$n.createRemoteObject(r.handler):null,r.originalHandler?this.#$n.createRemoteObject(r.originalHandler):null,t,null))}return n}retrieveDOMBreakpoints(){this.#Pn.requestDocument()}domBreakpoints(){return this.#id.slice()}hasDOMBreakpoint(e,t){return this.#id.some((n=>n.node===e&&n.type===t))}setDOMBreakpoint(e,t){for(const n of this.#id)if(n.node===e&&n.type===t)return this.toggleDOMBreakpoint(n,!0),n;const n=new fi(this,e,t,!0);return this.#id.push(n),this.saveDOMBreakpoints(),this.enableDOMBreakpoint(n),this.dispatchEventToListeners("DOMBreakpointAdded",n),n}removeDOMBreakpoint(e,t){this.removeDOMBreakpoints((n=>n.node===e&&n.type===t))}removeAllDOMBreakpoints(){this.removeDOMBreakpoints((e=>!0))}toggleDOMBreakpoint(e,t){t!==e.enabled&&(e.enabled=t,t?this.enableDOMBreakpoint(e):this.disableDOMBreakpoint(e),this.dispatchEventToListeners("DOMBreakpointToggled",e))}enableDOMBreakpoint(e){e.node.id&&(this.agent.invoke_setDOMBreakpoint({nodeId:e.node.id,type:e.type}),e.node.setMarker(mi,!0))}disableDOMBreakpoint(e){e.node.id&&(this.agent.invoke_removeDOMBreakpoint({nodeId:e.node.id,type:e.type}),e.node.setMarker(mi,!!this.nodeHasBreakpoints(e.node)||null))}nodeHasBreakpoints(e){for(const t of this.#id)if(t.node===e&&t.enabled)return!0;return!1}resolveDOMBreakpointData(e){const t=e.type,n=this.#Pn.nodeForId(e.nodeId);if(!t||!n)return null;let r=null,s=!1;return"subtree-modified"===t&&(s=e.insertion||!1,r=this.#Pn.nodeForId(e.targetNodeId)),{type:t,node:n,targetNode:r,insertion:s}}currentURL(){const e=this.#Pn.existingDocument();return e?e.documentURL:s.DevToolsPath.EmptyUrlString}async documentUpdated(){if(this.suspended)return;const e=this.#id;this.#id=[],this.dispatchEventToListeners("DOMBreakpointsRemoved",e);const t=await this.#Pn.requestDocument(),n=t?t.documentURL:s.DevToolsPath.EmptyUrlString;for(const e of this.#od.get())e.url===n&&this.#Pn.pushNodeByPathToFrontend(e.path).then(r.bind(this,e));function r(e,t){const n=t?this.#Pn.nodeForId(t):null;if(!n)return;const r=new fi(this,n,e.type,e.enabled);this.#id.push(r),e.enabled&&this.enableDOMBreakpoint(r),this.dispatchEventToListeners("DOMBreakpointAdded",r)}}removeDOMBreakpoints(e){const t=[],n=[];for(const r of this.#id)e(r)?(t.push(r),r.enabled&&(r.enabled=!1,this.disableDOMBreakpoint(r))):n.push(r);t.length&&(this.#id=n,this.saveDOMBreakpoints(),this.dispatchEventToListeners("DOMBreakpointsRemoved",t))}nodeRemoved(e){if(this.suspended)return;const{node:t}=e.data,n=t.children()||[];this.removeDOMBreakpoints((e=>e.node===t||-1!==n.indexOf(e.node)))}saveDOMBreakpoints(){const e=this.currentURL(),t=this.#od.get().filter((t=>t.url!==e));for(const n of this.#id)t.push({url:e,path:n.node.path(),type:n.type,enabled:n.enabled});this.#od.set(t)}}const mi="breakpoint-marker";class fi{domDebuggerModel;node;type;enabled;constructor(e,t,n,r){this.domDebuggerModel=e,this.node=t,this.type=n,this.enabled=r}}class bi{#ad;#ld;#g;#dd;#cd;#hd;#ud;#gd;#xr;#pd;#md;#fd;constructor(e,t,n,r,i,o,a,l,d,c,h){this.#ad=e,this.#ld=t,this.#g=n,this.#dd=r,this.#cd=i,this.#hd=o,this.#ud=a,this.#gd=l||a,this.#xr=d;const u=d.script();this.#pd=u?u.contentURL():s.DevToolsPath.EmptyUrlString,this.#md=c,this.#fd=h||"Raw"}domDebuggerModel(){return this.#ad}type(){return this.#g}useCapture(){return this.#dd}passive(){return this.#cd}once(){return this.#hd}handler(){return this.#ud}location(){return this.#xr}sourceURL(){return this.#pd}originalHandler(){return this.#gd}canRemove(){return Boolean(this.#md)||"FrameworkUser"!==this.#fd}remove(){if(!this.canRemove())return Promise.resolve(void 0);if("FrameworkUser"!==this.#fd){function e(e,t,n){this.removeEventListener(e,t,n),this["on"+e]&&(this["on"+e]=void 0)}return this.#ld.callFunction(e,[Nt.toCallArgument(this.#g),Nt.toCallArgument(this.#gd),Nt.toCallArgument(this.#dd)]).then((()=>{}))}if(this.#md){function t(e,t,n,r){this.call(null,e,t,n,r)}return this.#md.callFunction(t,[Nt.toCallArgument(this.#g),Nt.toCallArgument(this.#gd),Nt.toCallArgument(this.#dd),Nt.toCallArgument(this.#cd)]).then((()=>{}))}return Promise.resolve(void 0)}canTogglePassive(){return"FrameworkUser"!==this.#fd}togglePassive(){return this.#ld.callFunction((function(e,t,n,r){this.removeEventListener(e,t,{capture:n}),this.addEventListener(e,t,{capture:n,passive:!r})}),[Nt.toCallArgument(this.#g),Nt.toCallArgument(this.#gd),Nt.toCallArgument(this.#dd),Nt.toCallArgument(this.#cd)]).then((()=>{}))}origin(){return this.#fd}markAsFramework(){this.#fd="Framework"}isScrollBlockingType(){return"touchstart"===this.#g||"touchmove"===this.#g||"mousewheel"===this.#g||"wheel"===this.#g}}class yi extends Cs{#g;constructor(e,t){super(e,t),this.#g=t}type(){return this.#g}}class Ii extends Cs{eventTargetNames;constructor(e,t,n){super(n,e),this.eventTargetNames=t}setEnabled(e){if(this.enabled()!==e){super.setEnabled(e);for(const e of z.instance().models(pi))this.updateOnModel(e)}}updateOnModel(e){for(const t of this.eventTargetNames)this.enabled()?e.agent.invoke_setEventListenerBreakpoint({eventName:this.name,targetName:t}):e.agent.invoke_removeEventListenerBreakpoint({eventName:this.name,targetName:t})}static listener="listener:"}let vi;class ki{#bd;#yd;#Id;#vd;constructor(){this.#bd=e.Settings.Settings.instance().createLocalSetting("xhr-breakpoints",[]),this.#yd=new Map;for(const e of this.#bd.get())this.#yd.set(e.url,e.enabled);this.#Id=[],this.#Id.push(new yi("trusted-type-violation","trustedtype-sink-violation")),this.#Id.push(new yi("trusted-type-violation","trustedtype-policy-violation")),this.#vd=[],this.createEventListenerBreakpoints("media",["play","pause","playing","canplay","canplaythrough","seeking","seeked","timeupdate","ended","ratechange","durationchange","volumechange","loadstart","progress","suspend","abort","error","emptied","stalled","loadedmetadata","loadeddata","waiting"],["audio","video"]),this.createEventListenerBreakpoints("picture-in-picture",["enterpictureinpicture","leavepictureinpicture"],["video"]),this.createEventListenerBreakpoints("picture-in-picture",["resize"],["PictureInPictureWindow"]),this.createEventListenerBreakpoints("picture-in-picture",["enter"],["documentPictureInPicture"]),this.createEventListenerBreakpoints("clipboard",["copy","cut","paste","beforecopy","beforecut","beforepaste"],["*"]),this.createEventListenerBreakpoints("control",["resize","scroll","scrollend","scrollsnapchange","scrollsnapchanging","zoom","focus","blur","select","change","submit","reset"],["*"]),this.createEventListenerBreakpoints("device",["deviceorientation","devicemotion"],["*"]),this.createEventListenerBreakpoints("dom-mutation",["DOMActivate","DOMFocusIn","DOMFocusOut","DOMAttrModified","DOMCharacterDataModified","DOMNodeInserted","DOMNodeInsertedIntoDocument","DOMNodeRemoved","DOMNodeRemovedFromDocument","DOMSubtreeModified","DOMContentLoaded"],["*"]),this.createEventListenerBreakpoints("drag-drop",["drag","dragstart","dragend","dragenter","dragover","dragleave","drop"],["*"]),this.createEventListenerBreakpoints("keyboard",["keydown","keyup","keypress","input"],["*"]),this.createEventListenerBreakpoints("load",["load","beforeunload","unload","abort","error","hashchange","popstate","navigate","navigatesuccess","navigateerror","currentchange","navigateto","navigatefrom","finish","dispose"],["*"]),this.createEventListenerBreakpoints("mouse",["auxclick","click","dblclick","mousedown","mouseup","mouseover","mousemove","mouseout","mouseenter","mouseleave","mousewheel","wheel","contextmenu"],["*"]),this.createEventListenerBreakpoints("pointer",["pointerover","pointerout","pointerenter","pointerleave","pointerdown","pointerup","pointermove","pointercancel","gotpointercapture","lostpointercapture","pointerrawupdate"],["*"]),this.createEventListenerBreakpoints("touch",["touchstart","touchmove","touchend","touchcancel"],["*"]),this.createEventListenerBreakpoints("worker",["message","messageerror"],["*"]),this.createEventListenerBreakpoints("xhr",["readystatechange","load","loadstart","loadend","abort","error","progress","timeout"],["xmlhttprequest","xmlhttprequestupload"]),z.instance().observeModels(pi,this)}static instance(e={forceNew:null}){const{forceNew:t}=e;return vi&&!t||(vi=new ki),vi}cspViolationBreakpoints(){return this.#Id.slice()}createEventListenerBreakpoints(e,t,n){for(const r of t)this.#vd.push(new Ii(r,n,e))}resolveEventListenerBreakpoint({eventName:e,targetName:t}){const n="listener:";if(!e.startsWith(n))return null;e=e.substring(9),t=(t||"*").toLowerCase();let r=null;for(const n of this.#vd)e&&n.name===e&&-1!==n.eventTargetNames.indexOf(t)&&(r=n),!r&&e&&n.name===e&&-1!==n.eventTargetNames.indexOf("*")&&(r=n);return r}eventListenerBreakpoints(){return this.#vd.slice()}updateCSPViolationBreakpoints(){const e=this.#Id.filter((e=>e.enabled())).map((e=>e.type()));for(const t of z.instance().models(pi))this.updateCSPViolationBreakpointsForModel(t,e)}updateCSPViolationBreakpointsForModel(e,t){e.agent.invoke_setBreakOnCSPViolation({violationTypes:t})}xhrBreakpoints(){return this.#yd}saveXHRBreakpoints(){const e=[];for(const t of this.#yd.keys())e.push({url:t,enabled:this.#yd.get(t)||!1});this.#bd.set(e)}addXHRBreakpoint(e,t){if(this.#yd.set(e,t),t)for(const t of z.instance().models(pi))t.agent.invoke_setXHRBreakpoint({url:e});this.saveXHRBreakpoints()}removeXHRBreakpoint(e){const t=this.#yd.get(e);if(this.#yd.delete(e),t)for(const t of z.instance().models(pi))t.agent.invoke_removeXHRBreakpoint({url:e});this.saveXHRBreakpoints()}toggleXHRBreakpoint(e,t){this.#yd.set(e,t);for(const n of z.instance().models(pi))t?n.agent.invoke_setXHRBreakpoint({url:e}):n.agent.invoke_removeXHRBreakpoint({url:e});this.saveXHRBreakpoints()}modelAdded(e){for(const t of this.#yd.keys())this.#yd.get(t)&&e.agent.invoke_setXHRBreakpoint({url:t});for(const t of this.#vd)t.enabled()&&t.updateOnModel(e);const t=this.#Id.filter((e=>e.enabled())).map((e=>e.type()));this.updateCSPViolationBreakpointsForModel(e,t)}modelRemoved(e){}}h.register(pi,{capabilities:2,autostart:!1});var Si=Object.freeze({__proto__:null,DOMDebuggerModel:pi,DOMBreakpoint:fi,EventListener:bi,CSPViolationBreakpoint:yi,DOMEventListenerBreakpoint:Ii,DOMDebuggerManager:ki});class wi extends h{agent;constructor(e){super(e),this.agent=e.eventBreakpointsAgent()}}class Ci extends Cs{setEnabled(e){if(this.enabled()!==e){super.setEnabled(e);for(const e of z.instance().models(wi))this.updateOnModel(e)}}updateOnModel(e){this.enabled()?e.agent.invoke_setInstrumentationBreakpoint({eventName:this.name}):e.agent.invoke_removeInstrumentationBreakpoint({eventName:this.name})}static instrumentationPrefix="instrumentation:"}let Ri;class xi{#vd=[];constructor(){this.createInstrumentationBreakpoints("auction-worklet",["beforeBidderWorkletBiddingStart","beforeBidderWorkletReportingStart","beforeSellerWorkletScoringStart","beforeSellerWorkletReportingStart"]),this.createInstrumentationBreakpoints("animation",["requestAnimationFrame","cancelAnimationFrame","requestAnimationFrame.callback"]),this.createInstrumentationBreakpoints("canvas",["canvasContextCreated","webglErrorFired","webglWarningFired"]),this.createInstrumentationBreakpoints("geolocation",["Geolocation.getCurrentPosition","Geolocation.watchPosition"]),this.createInstrumentationBreakpoints("notification",["Notification.requestPermission"]),this.createInstrumentationBreakpoints("parse",["Element.setInnerHTML","Document.write"]),this.createInstrumentationBreakpoints("script",["scriptFirstStatement","scriptBlockedByCSP"]),this.createInstrumentationBreakpoints("shared-storage-worklet",["sharedStorageWorkletScriptFirstStatement"]),this.createInstrumentationBreakpoints("timer",["setTimeout","clearTimeout","setTimeout.callback","setInterval","clearInterval","setInterval.callback"]),this.createInstrumentationBreakpoints("window",["DOMWindow.close"]),this.createInstrumentationBreakpoints("web-audio",["audioContextCreated","audioContextClosed","audioContextResumed","audioContextSuspended"]),z.instance().observeModels(wi,this)}static instance(e={forceNew:null}){const{forceNew:t}=e;return Ri&&!t||(Ri=new xi),Ri}createInstrumentationBreakpoints(e,t){for(const n of t)this.#vd.push(new Ci(e,n))}eventListenerBreakpoints(){return this.#vd.slice()}resolveEventListenerBreakpoint({eventName:e}){if(!e.startsWith(Ci.instrumentationPrefix))return null;const t=e.substring(Ci.instrumentationPrefix.length);return this.#vd.find((e=>e.name===t))||null}modelAdded(e){for(const t of this.#vd)t.enabled()&&t.updateOnModel(e)}modelRemoved(e){}}h.register(wi,{capabilities:524288,autostart:!1});var Ti=Object.freeze({__proto__:null,EventBreakpointsModel:wi,EventBreakpointsManager:xi}),Mi=Object.freeze({__proto__:null});let Pi;class Li extends e.ObjectWrapper.ObjectWrapper{#kd;#Sd;#L;#wd;constructor(){super(),this.#kd=new Map,this.#Sd=new Map,this.#L=new Set,z.instance().observeModels(_n,this),this.#wd=0}static instance({forceNew:e}={forceNew:!1}){return Pi&&!e||(Pi=new Li),Pi}observeIsolates(e){if(this.#L.has(e))throw new Error("Observer can only be registered once");this.#L.size||this.poll(),this.#L.add(e);for(const t of this.#kd.values())e.isolateAdded(t)}unobserveIsolates(e){this.#L.delete(e),this.#L.size||++this.#wd}modelAdded(e){this.modelAddedInternal(e)}async modelAddedInternal(e){this.#Sd.set(e,null);const t=await e.isolateId();if(!this.#Sd.has(e))return;if(!t)return void this.#Sd.delete(e);this.#Sd.set(e,t);let n=this.#kd.get(t);if(n||(n=new Oi(t),this.#kd.set(t,n)),n.modelsInternal.add(e),1===n.modelsInternal.size)for(const e of this.#L)e.isolateAdded(n);else for(const e of this.#L)e.isolateChanged(n)}modelRemoved(e){const t=this.#Sd.get(e);if(this.#Sd.delete(e),!t)return;const n=this.#kd.get(t);if(n)if(n.modelsInternal.delete(e),n.modelsInternal.size)for(const e of this.#L)e.isolateChanged(n);else{for(const e of this.#L)e.isolateRemoved(n);this.#kd.delete(t)}}isolateByModel(e){return this.#kd.get(this.#Sd.get(e)||"")||null}isolates(){return this.#kd.values()}async poll(){const e=this.#wd;for(;e===this.#wd;)await Promise.all(Array.from(this.isolates(),(e=>e.update()))),await new Promise((e=>window.setTimeout(e,Ai)))}}const Ei=12e4,Ai=2e3;class Oi{#C;modelsInternal;#Cd;#Rd;constructor(e){this.#C=e,this.modelsInternal=new Set,this.#Cd=0;const t=Ei/Ai;this.#Rd=new Di(t)}id(){return this.#C}models(){return this.modelsInternal}runtimeModel(){return this.modelsInternal.values().next().value||null}heapProfilerModel(){const e=this.runtimeModel();return e&&e.heapProfilerModel()}async update(){const e=this.runtimeModel(),t=e&&await e.heapUsage();t&&(this.#Cd=t.usedSize,this.#Rd.add(this.#Cd),Li.instance().dispatchEventToListeners("MemoryChanged",this))}samplesCount(){return this.#Rd.count()}usedHeapSize(){return this.#Cd}usedHeapSizeGrowRate(){return this.#Rd.fitSlope()}isMainThread(){const e=this.runtimeModel();return!!e&&"main"===e.target().id()}}class Di{#xd;#Td;#Fr;#Md;#Pd;#Ld;#Ed;#Ad;#Od;constructor(e){this.#xd=0|e,this.reset()}reset(){this.#Td=Date.now(),this.#Fr=0,this.#Md=[],this.#Pd=[],this.#Ld=0,this.#Ed=0,this.#Ad=0,this.#Od=0}count(){return this.#Md.length}add(e,t){const n="number"==typeof t?t:Date.now()-this.#Td,r=e;if(this.#Md.length===this.#xd){const e=this.#Md[this.#Fr],t=this.#Pd[this.#Fr];this.#Ld-=e,this.#Ed-=t,this.#Ad-=e*e,this.#Od-=e*t}this.#Ld+=n,this.#Ed+=r,this.#Ad+=n*n,this.#Od+=n*r,this.#Md[this.#Fr]=n,this.#Pd[this.#Fr]=r,this.#Fr=(this.#Fr+1)%this.#xd}fitSlope(){const e=this.count();return e<2?0:(this.#Od-this.#Ld*this.#Ed/e)/(this.#Ad-this.#Ld*this.#Ld/e)}}var Ni=Object.freeze({__proto__:null,IsolateManager:Li,MemoryTrendWindowMs:Ei,Isolate:Oi,MemoryTrend:Di});class Fi extends h{#Dd=!1;#Kn=!1;constructor(e){super(e),this.ensureEnabled()}async ensureEnabled(){if(this.#Kn)return;this.#Kn=!0,this.target().registerAuditsDispatcher(this);const e=this.target().auditsAgent();await e.invoke_enable()}issueAdded(e){this.dispatchEventToListeners("IssueAdded",{issuesModel:this,inspectorIssue:e.issue})}dispose(){super.dispose(),this.#Dd=!0}getTargetIfNotDisposed(){return this.#Dd?null:this.target()}}h.register(Fi,{capabilities:32768,autostart:!0});var Bi=Object.freeze({__proto__:null,IssuesModel:Fi});var Hi=Object.freeze({__proto__:null,StickyPositionConstraint:class{#Nd;#Fd;#Bd;#Hd;constructor(e,t){this.#Nd=t.stickyBoxRect,this.#Fd=t.containingBlockRect,this.#Bd=null,e&&t.nearestLayerShiftingStickyBox&&(this.#Bd=e.layerById(t.nearestLayerShiftingStickyBox)),this.#Hd=null,e&&t.nearestLayerShiftingContainingBlock&&(this.#Hd=e.layerById(t.nearestLayerShiftingContainingBlock))}stickyBoxRect(){return this.#Nd}containingBlockRect(){return this.#Fd}nearestLayerShiftingStickyBox(){return this.#Bd}nearestLayerShiftingContainingBlock(){return this.#Hd}},LayerTreeBase:class{#e;#Pn;layersById;#Ud;#_d;#qd;#zd;constructor(e){this.#e=e,this.#Pn=e?e.model(Or):null,this.layersById=new Map,this.#Ud=null,this.#_d=null,this.#qd=new Map}target(){return this.#e}root(){return this.#Ud}setRoot(e){this.#Ud=e}contentRoot(){return this.#_d}setContentRoot(e){this.#_d=e}forEachLayer(e,t){return!(!t&&!(t=this.root()))&&(e(t)||t.children().some(this.forEachLayer.bind(this,e)))}layerById(e){return this.layersById.get(e)||null}async resolveBackendNodeIds(e){if(!e.size||!this.#Pn)return;const t=await this.#Pn.pushNodesByBackendIdsToFrontend(e);if(t)for(const e of t.keys())this.#qd.set(e,t.get(e)||null)}backendNodeIdToNode(){return this.#qd}setViewportSize(e){this.#zd=e}viewportSize(){return this.#zd}nodeForId(e){return this.#Pn?this.#Pn.nodeForId(e):null}}});class Ui{id;url;startTime;loadTime;contentLoadTime;mainRequest;constructor(e){this.id=++Ui.lastIdentifier,this.url=e.url(),this.startTime=e.startTime,this.mainRequest=e}static forRequest(e){return _i.get(e)||null}bindRequest(e){_i.set(e,this)}static lastIdentifier=0}const _i=new WeakMap;var qi=Object.freeze({__proto__:null,PageLoad:Ui});class zi extends h{layerTreeAgent;constructor(e){super(e),this.layerTreeAgent=e.layerTreeAgent()}async loadSnapshotFromFragments(e){const{snapshotId:t}=await this.layerTreeAgent.invoke_loadSnapshot({tiles:e});return t?new ji(this,t):null}loadSnapshot(e){const t={x:0,y:0,picture:e};return this.loadSnapshotFromFragments([t])}async makeSnapshot(e){const{snapshotId:t}=await this.layerTreeAgent.invoke_makeSnapshot({layerId:e});return t?new ji(this,t):null}}class ji{#jd;#no;#Vd;constructor(e,t){this.#jd=e,this.#no=t,this.#Vd=1}release(){console.assert(this.#Vd>0,"release is already called on the object"),--this.#Vd||this.#jd.layerTreeAgent.invoke_releaseSnapshot({snapshotId:this.#no})}addReference(){++this.#Vd,console.assert(this.#Vd>0,"Referencing a dead object")}async replay(e,t,n){return(await this.#jd.layerTreeAgent.invoke_replaySnapshot({snapshotId:this.#no,fromStep:t,toStep:n,scale:e||1})).dataURL}async profile(e){return(await this.#jd.layerTreeAgent.invoke_profileSnapshot({snapshotId:this.#no,minRepeatCount:5,minDuration:1,clipRect:e||void 0})).timings}async commandLog(){const e=await this.#jd.layerTreeAgent.invoke_snapshotCommandLog({snapshotId:this.#no});return e.commandLog?e.commandLog.map(((e,t)=>new Vi(e,t))):null}}class Vi{method;params;commandIndex;constructor(e,t){this.method=e.method,this.params=e.params,this.commandIndex=t}}h.register(zi,{capabilities:2,autostart:!1});var Wi=Object.freeze({__proto__:null,PaintProfilerModel:zi,PaintProfilerSnapshot:ji,PaintProfilerLogItem:Vi});class Gi extends h{#ws;#Wd;#Gd;constructor(e){super(e),this.#ws=e.performanceAgent(),this.#Wd=new Map([["TaskDuration","CumulativeTime"],["ScriptDuration","CumulativeTime"],["LayoutDuration","CumulativeTime"],["RecalcStyleDuration","CumulativeTime"],["LayoutCount","CumulativeCount"],["RecalcStyleCount","CumulativeCount"]]),this.#Gd=new Map}enable(){return this.#ws.invoke_enable({})}disable(){return this.#ws.invoke_disable()}async requestMetrics(){const e=await this.#ws.invoke_getMetrics()||[],t=new Map,n=performance.now();for(const r of e.metrics){let e,i=this.#Gd.get(r.name);switch(i||(i={lastValue:void 0,lastTimestamp:void 0},this.#Gd.set(r.name,i)),this.#Wd.get(r.name)){case"CumulativeTime":e=i.lastTimestamp&&i.lastValue?s.NumberUtilities.clamp(1e3*(r.value-i.lastValue)/(n-i.lastTimestamp),0,1):0,i.lastValue=r.value,i.lastTimestamp=n;break;case"CumulativeCount":e=i.lastTimestamp&&i.lastValue?Math.max(0,1e3*(r.value-i.lastValue)/(n-i.lastTimestamp)):0,i.lastValue=r.value,i.lastTimestamp=n;break;default:e=r.value}t.set(r.name,e)}return{metrics:t,timestamp:n}}}h.register(Gi,{capabilities:2,autostart:!1});var Ki=Object.freeze({__proto__:null,PerformanceMetricsModel:Gi});class Qi extends h{agent;loaderIds=[];targetJustAttached=!0;lastPrimaryPageModel=null;documents=new Map;constructor(e){super(e),e.registerPreloadDispatcher(new $i(this)),this.agent=e.preloadAgent(),this.agent.invoke_enable();const t=e.targetInfo();void 0!==t&&"prerender"===t.subtype&&(this.lastPrimaryPageModel=z.instance().primaryPageTarget()?.model(Qi)||null),z.instance().addModelListener(Gr,Vr.PrimaryPageChanged,this.onPrimaryPageChanged,this)}dispose(){super.dispose(),z.instance().removeModelListener(Gr,Vr.PrimaryPageChanged,this.onPrimaryPageChanged,this),this.agent.invoke_disable()}ensureDocumentPreloadingData(e){void 0===this.documents.get(e)&&this.documents.set(e,new Xi)}currentLoaderId(){if(this.targetJustAttached)return null;if(0===this.loaderIds.length)throw new Error("unreachable");return this.loaderIds[this.loaderIds.length-1]}currentDocument(){const e=this.currentLoaderId();return null===e?null:this.documents.get(e)||null}getRuleSetById(e){return this.currentDocument()?.ruleSets.getById(e)||null}getAllRuleSets(){return this.currentDocument()?.ruleSets.getAll()||[]}getPreloadCountsByRuleSetId(){const e=new Map;for(const{value:t}of this.getPreloadingAttempts(null))for(const n of[null,...t.ruleSetIds]){void 0===e.get(n)&&e.set(n,new Map);const r=e.get(n);i(r);const s=r.get(t.status)||0;r.set(t.status,s+1)}return e}getPreloadingAttemptById(e){const t=this.currentDocument();return null===t?null:t.preloadingAttempts.getById(e,t.sources)||null}getPreloadingAttempts(e){const t=this.currentDocument();return null===t?[]:t.preloadingAttempts.getAll(e,t.sources)}getPreloadingAttemptsOfPreviousPage(){if(this.loaderIds.length<=1)return[];const e=this.documents.get(this.loaderIds[this.loaderIds.length-2]);return void 0===e?[]:e.preloadingAttempts.getAll(null,e.sources)}onPrimaryPageChanged(e){const{frame:t,type:n}=e.data;if(null===this.lastPrimaryPageModel&&"Activation"===n)return;if(null!==this.lastPrimaryPageModel&&"Activation"!==n)return;if(null!==this.lastPrimaryPageModel&&"Activation"===n){this.loaderIds=this.lastPrimaryPageModel.loaderIds;for(const[e,t]of this.lastPrimaryPageModel.documents.entries())this.ensureDocumentPreloadingData(e),this.documents.get(e)?.mergePrevious(t)}this.lastPrimaryPageModel=null;const r=t.loaderId;this.loaderIds.push(r),this.loaderIds=this.loaderIds.slice(-2),this.ensureDocumentPreloadingData(r);for(const e of this.documents.keys())this.loaderIds.includes(e)||this.documents.delete(e);this.dispatchEventToListeners("ModelUpdated")}onRuleSetUpdated(e){const t=e.ruleSet,n=t.loaderId;null===this.currentLoaderId()&&(this.loaderIds=[n],this.targetJustAttached=!1),this.ensureDocumentPreloadingData(n),this.documents.get(n)?.ruleSets.upsert(t),this.dispatchEventToListeners("ModelUpdated")}onRuleSetRemoved(e){const t=e.id;for(const e of this.documents.values())e.ruleSets.delete(t);this.dispatchEventToListeners("ModelUpdated")}onPreloadingAttemptSourcesUpdated(e){const t=e.loaderId;this.ensureDocumentPreloadingData(t);const n=this.documents.get(t);void 0!==n&&(n.sources.update(e.preloadingAttemptSources),n.preloadingAttempts.maybeRegisterNotTriggered(n.sources),this.dispatchEventToListeners("ModelUpdated"))}onPrefetchStatusUpdated(e){const t=e.key.loaderId;this.ensureDocumentPreloadingData(t);const n={action:"Prefetch",key:e.key,status:Yi(e.status),prefetchStatus:e.prefetchStatus||null,requestId:e.requestId};this.documents.get(t)?.preloadingAttempts.upsert(n),this.dispatchEventToListeners("ModelUpdated")}onPrerenderStatusUpdated(e){const t=e.key.loaderId;this.ensureDocumentPreloadingData(t);const n={action:"Prerender",key:e.key,status:Yi(e.status),prerenderStatus:e.prerenderStatus||null,disallowedMojoInterface:e.disallowedMojoInterface||null,mismatchedHeaders:e.mismatchedHeaders||null};this.documents.get(t)?.preloadingAttempts.upsert(n),this.dispatchEventToListeners("ModelUpdated")}onPreloadEnabledStateUpdated(e){this.dispatchEventToListeners("WarningsUpdated",e)}}h.register(Qi,{capabilities:2,autostart:!1});class $i{model;constructor(e){this.model=e}ruleSetUpdated(e){this.model.onRuleSetUpdated(e)}ruleSetRemoved(e){this.model.onRuleSetRemoved(e)}preloadingAttemptSourcesUpdated(e){this.model.onPreloadingAttemptSourcesUpdated(e)}prefetchStatusUpdated(e){this.model.onPrefetchStatusUpdated(e)}prerenderStatusUpdated(e){this.model.onPrerenderStatusUpdated(e)}preloadEnabledStateUpdated(e){this.model.onPreloadEnabledStateUpdated(e)}}class Xi{ruleSets=new Ji;preloadingAttempts=new eo;sources=new to;mergePrevious(e){if(!this.ruleSets.isEmpty()||!this.sources.isEmpty())throw new Error("unreachable");this.ruleSets=e.ruleSets,this.preloadingAttempts.mergePrevious(e.preloadingAttempts),this.sources=e.sources}}class Ji{map=new Map;isEmpty(){return 0===this.map.size}getById(e){return this.map.get(e)||null}getAll(){return Array.from(this.map.entries()).map((([e,t])=>({id:e,value:t})))}upsert(e){this.map.set(e.id,e)}delete(e){this.map.delete(e)}}function Yi(e){switch(e){case"Pending":return"Pending";case"Running":return"Running";case"Ready":return"Ready";case"Success":return"Success";case"Failure":return"Failure";case"NotSupported":return"NotSupported"}throw new Error("unreachable")}function Zi(e){let t,n;switch(e.action){case"Prefetch":t="Prefetch";break;case"Prerender":t="Prerender"}switch(e.targetHint){case void 0:n="undefined";break;case"Blank":n="Blank";break;case"Self":n="Self"}return`${e.loaderId}:${t}:${e.url}:${n}`}class eo{map=new Map;enrich(e,t){let n=[],r=[];return null!==t&&(n=t.ruleSetIds,r=t.nodeIds),{...e,ruleSetIds:n,nodeIds:r}}getById(e,t){const n=this.map.get(e)||null;return null===n?null:this.enrich(n,t.getById(e))}getAll(e,t){return[...this.map.entries()].map((([e,n])=>({id:e,value:this.enrich(n,t.getById(e))}))).filter((({value:t})=>!e||t.ruleSetIds.includes(e)))}upsert(e){const t=Zi(e.key);this.map.set(t,e)}maybeRegisterNotTriggered(e){for(const[t,{key:n}]of e.entries()){if(void 0!==this.map.get(t))continue;let e;switch(n.action){case"Prefetch":e={action:"Prefetch",key:n,status:"NotTriggered",prefetchStatus:null,requestId:""};break;case"Prerender":e={action:"Prerender",key:n,status:"NotTriggered",prerenderStatus:null,disallowedMojoInterface:null,mismatchedHeaders:null}}this.map.set(t,e)}}mergePrevious(e){for(const[t,n]of this.map.entries())e.map.set(t,n);this.map=e.map}}class to{map=new Map;entries(){return this.map.entries()}isEmpty(){return 0===this.map.size}getById(e){return this.map.get(e)||null}update(e){this.map=new Map(e.map((e=>[Zi(e.key),e])))}}var no=Object.freeze({__proto__:null,PreloadingModel:Qi});var ro=Object.freeze({__proto__:null,ReactNativeApplicationModel:class extends h{#Kn;#ws;metadataCached=null;constructor(e){super(e),a.rnPerfMetrics.fuseboxSetClientMetadataStarted(),this.#Kn=!1,this.#ws=e.reactNativeApplicationAgent(),e.registerReactNativeApplicationDispatcher(this),this.ensureEnabled()}ensureEnabled(){this.#Kn||(this.#ws.invoke_enable().then((e=>{const t=e.getError(),n=!t;a.rnPerfMetrics.fuseboxSetClientMetadataFinished(n,t)})).catch((e=>{a.rnPerfMetrics.fuseboxSetClientMetadataFinished(!1,e)})),this.#Kn=!0)}metadataUpdated(e){this.metadataCached=e,this.dispatchEventToListeners("MetadataUpdated",e)}}});class so extends h{#ws;#Kd;#Qd;constructor(e){super(e),this.#ws=e.pageAgent(),this.#Kd=null,this.#Qd=null,e.registerPageDispatcher(this)}startScreencast(e,t,n,r,s,i,o){this.#Kd=i,this.#Qd=o,this.#ws.invoke_startScreencast({format:e,quality:t,maxWidth:n,maxHeight:r,everyNthFrame:s})}stopScreencast(){this.#Kd=null,this.#Qd=null,this.#ws.invoke_stopScreencast()}async captureScreenshot(e,t,n,r){const s={format:e,quality:t,fromSurface:!0};switch(n){case"fromClip":s.captureBeyondViewport=!0,s.clip=r;break;case"fullpage":s.captureBeyondViewport=!0;break;case"fromViewport":s.captureBeyondViewport=!1;break;default:throw new Error("Unexpected or unspecified screnshotMode")}await wr.muteHighlight();const i=await this.#ws.invoke_captureScreenshot(s);return await wr.unmuteHighlight(),i.data}async fetchLayoutMetrics(){const e=await this.#ws.invoke_getLayoutMetrics();return e.getError()?null:{viewportX:e.cssVisualViewport.pageX,viewportY:e.cssVisualViewport.pageY,viewportScale:e.cssVisualViewport.scale,contentWidth:e.cssContentSize.width,contentHeight:e.cssContentSize.height}}screencastFrame({data:e,metadata:t,sessionId:n}){this.#ws.invoke_screencastFrameAck({sessionId:n}),this.#Kd&&this.#Kd.call(null,e,t)}screencastVisibilityChanged({visible:e}){this.#Qd&&this.#Qd.call(null,e)}backForwardCacheNotUsed(e){}domContentEventFired(e){}loadEventFired(e){}lifecycleEvent(e){}navigatedWithinDocument(e){}frameAttached(e){}frameNavigated(e){}documentOpened(e){}frameDetached(e){}frameStartedLoading(e){}frameStoppedLoading(e){}frameRequestedNavigation(e){}frameScheduledNavigation(e){}frameClearedScheduledNavigation(e){}frameResized(){}javascriptDialogOpening(e){}javascriptDialogClosed(e){}interstitialShown(){}interstitialHidden(){}windowOpen(e){}fileChooserOpened(e){}compilationCacheProduced(e){}downloadWillBegin(e){}downloadProgress(){}prefetchStatusUpdated(e){}prerenderStatusUpdated(e){}}h.register(so,{capabilities:64,autostart:!1});var io=Object.freeze({__proto__:null,ScreenCaptureModel:so});class oo extends h{enabled=!1;storageAgent;storageKeyManager;bucketsById=new Map;trackedStorageKeys=new Set;constructor(e){super(e),e.registerStorageDispatcher(this),this.storageAgent=e.storageAgent(),this.storageKeyManager=e.model(jr)}getBuckets(){return new Set(this.bucketsById.values())}getBucketsForStorageKey(e){const t=[...this.bucketsById.values()];return new Set(t.filter((({bucket:t})=>t.storageKey===e)))}getDefaultBucketForStorageKey(e){return[...this.bucketsById.values()].find((({bucket:t})=>t.storageKey===e&&void 0===t.name))??null}getBucketById(e){return this.bucketsById.get(e)??null}getBucketByName(e,t){if(!t)return this.getDefaultBucketForStorageKey(e);return[...this.bucketsById.values()].find((({bucket:n})=>n.storageKey===e&&n.name===t))??null}deleteBucket(e){this.storageAgent.invoke_deleteStorageBucket({bucket:e})}enable(){if(!this.enabled){if(this.storageKeyManager){this.storageKeyManager.addEventListener("StorageKeyAdded",this.storageKeyAdded,this),this.storageKeyManager.addEventListener("StorageKeyRemoved",this.storageKeyRemoved,this);for(const e of this.storageKeyManager.storageKeys())this.addStorageKey(e)}this.enabled=!0}}storageKeyAdded(e){this.addStorageKey(e.data)}storageKeyRemoved(e){this.removeStorageKey(e.data)}addStorageKey(e){if(this.trackedStorageKeys.has(e))throw new Error("Can't call addStorageKey for a storage key if it has already been added.");this.trackedStorageKeys.add(e),this.storageAgent.invoke_setStorageBucketTracking({storageKey:e,enable:!0})}removeStorageKey(e){if(!this.trackedStorageKeys.has(e))throw new Error("Can't call removeStorageKey for a storage key if it hasn't already been added.");const t=this.getBucketsForStorageKey(e);for(const e of t)this.bucketRemoved(e);this.trackedStorageKeys.delete(e),this.storageAgent.invoke_setStorageBucketTracking({storageKey:e,enable:!1})}bucketAdded(e){this.bucketsById.set(e.id,e),this.dispatchEventToListeners("BucketAdded",{model:this,bucketInfo:e})}bucketRemoved(e){this.bucketsById.delete(e.id),this.dispatchEventToListeners("BucketRemoved",{model:this,bucketInfo:e})}bucketChanged(e){this.dispatchEventToListeners("BucketChanged",{model:this,bucketInfo:e})}bucketInfosAreEqual(e,t){return e.bucket.storageKey===t.bucket.storageKey&&e.id===t.id&&e.bucket.name===t.bucket.name&&e.expiration===t.expiration&&e.quota===t.quota&&e.persistent===t.persistent&&e.durability===t.durability}storageBucketCreatedOrUpdated({bucketInfo:e}){const t=this.getBucketById(e.id);t?this.bucketInfosAreEqual(t,e)||this.bucketChanged(e):this.bucketAdded(e)}storageBucketDeleted({bucketId:e}){const t=this.getBucketById(e);if(!t)throw new Error(`Received an event that Storage Bucket '${e}' was deleted, but it wasn't in the StorageBucketsModel.`);this.bucketRemoved(t)}attributionReportingTriggerRegistered(e){}interestGroupAccessed(e){}interestGroupAuctionEventOccurred(e){}interestGroupAuctionNetworkRequestCreated(e){}indexedDBListUpdated(e){}indexedDBContentUpdated(e){}cacheStorageListUpdated(e){}cacheStorageContentUpdated(e){}sharedStorageAccessed(e){}attributionReportingSourceRegistered(e){}}h.register(oo,{capabilities:8192,autostart:!1});var ao=Object.freeze({__proto__:null,StorageBucketsModel:oo});const lo={serviceworkercacheagentError:"`ServiceWorkerCacheAgent` error deleting cache entry {PH1} in cache: {PH2}"},co=r.i18n.registerUIStrings("core/sdk/ServiceWorkerCacheModel.ts",lo),ho=r.i18n.getLocalizedString.bind(void 0,co);class uo extends h{cacheAgent;#$d;#Xd;#Jd=new Map;#Yd=new Set;#Zd=new Set;#ec=new e.Throttler.Throttler(2e3);#Kn=!1;#tc=!1;constructor(e){super(e),e.registerStorageDispatcher(this),this.cacheAgent=e.cacheStorageAgent(),this.#$d=e.storageAgent(),this.#Xd=e.model(oo)}enable(){if(!this.#Kn){this.#Xd.addEventListener("BucketAdded",this.storageBucketAdded,this),this.#Xd.addEventListener("BucketRemoved",this.storageBucketRemoved,this);for(const e of this.#Xd.getBuckets())this.addStorageBucket(e.bucket);this.#Kn=!0}}clearForStorageKey(e){for(const[t,n]of this.#Jd.entries())n.storageKey===e&&(this.#Jd.delete(t),this.cacheRemoved(n));for(const t of this.#Xd.getBucketsForStorageKey(e))this.loadCacheNames(t.bucket)}refreshCacheNames(){for(const e of this.#Jd.values())this.cacheRemoved(e);this.#Jd.clear();const e=this.#Xd.getBuckets();for(const t of e)this.loadCacheNames(t.bucket)}async deleteCache(e){const t=await this.cacheAgent.invoke_deleteCache({cacheId:e.cacheId});t.getError()?console.error(`ServiceWorkerCacheAgent error deleting cache ${e.toString()}: ${t.getError()}`):(this.#Jd.delete(e.cacheId),this.cacheRemoved(e))}async deleteCacheEntry(t,n){const r=await this.cacheAgent.invoke_deleteEntry({cacheId:t.cacheId,request:n});r.getError()&&e.Console.Console.instance().error(ho(lo.serviceworkercacheagentError,{PH1:t.toString(),PH2:String(r.getError())}))}loadCacheData(e,t,n,r,s){this.requestEntries(e,t,n,r,s)}loadAllCacheData(e,t,n){this.requestAllEntries(e,t,n)}caches(){const e=new Array;for(const t of this.#Jd.values())e.push(t);return e}dispose(){for(const e of this.#Jd.values())this.cacheRemoved(e);this.#Jd.clear(),this.#Kn&&(this.#Xd.removeEventListener("BucketAdded",this.storageBucketAdded,this),this.#Xd.removeEventListener("BucketRemoved",this.storageBucketRemoved,this))}addStorageBucket(e){this.loadCacheNames(e),this.#Yd.has(e.storageKey)||(this.#Yd.add(e.storageKey),this.#$d.invoke_trackCacheStorageForStorageKey({storageKey:e.storageKey}))}removeStorageBucket(e){let t=0;for(const[n,r]of this.#Jd.entries())e.storageKey===r.storageKey&&t++,r.inBucket(e)&&(t--,this.#Jd.delete(n),this.cacheRemoved(r));0===t&&(this.#Yd.delete(e.storageKey),this.#$d.invoke_untrackCacheStorageForStorageKey({storageKey:e.storageKey}))}async loadCacheNames(e){const t=await this.cacheAgent.invoke_requestCacheNames({storageBucket:e});t.getError()||this.updateCacheNames(e,t.caches)}updateCacheNames(e,t){const n=new Set,r=new Map,s=new Map;for(const e of t){const t=e.storageBucket??this.#Xd.getDefaultBucketForStorageKey(e.storageKey)?.bucket;if(!t)continue;const s=new go(this,t,e.cacheName,e.cacheId);n.add(s.cacheId),this.#Jd.has(s.cacheId)||(r.set(s.cacheId,s),this.#Jd.set(s.cacheId,s))}this.#Jd.forEach((function(t){t.inBucket(e)&&!n.has(t.cacheId)&&(s.set(t.cacheId,t),this.#Jd.delete(t.cacheId))}),this),r.forEach(this.cacheAdded,this),s.forEach(this.cacheRemoved,this)}storageBucketAdded({data:{bucketInfo:{bucket:e}}}){this.addStorageBucket(e)}storageBucketRemoved({data:{bucketInfo:{bucket:e}}}){this.removeStorageBucket(e)}cacheAdded(e){this.dispatchEventToListeners("CacheAdded",{model:this,cache:e})}cacheRemoved(e){this.dispatchEventToListeners("CacheRemoved",{model:this,cache:e})}async requestEntries(e,t,n,r,s){const i=await this.cacheAgent.invoke_requestEntries({cacheId:e.cacheId,skipCount:t,pageSize:n,pathFilter:r});i.getError()?console.error("ServiceWorkerCacheAgent error while requesting entries: ",i.getError()):s(i.cacheDataEntries,i.returnCount)}async requestAllEntries(e,t,n){const r=await this.cacheAgent.invoke_requestEntries({cacheId:e.cacheId,pathFilter:t});r.getError()?console.error("ServiceWorkerCacheAgent error while requesting entries: ",r.getError()):n(r.cacheDataEntries,r.returnCount)}cacheStorageListUpdated({bucketId:e}){const t=this.#Xd.getBucketById(e)?.bucket;t&&(this.#Zd.add(t),this.#ec.schedule((()=>{const e=Array.from(this.#Zd,(e=>this.loadCacheNames(e)));return this.#Zd.clear(),Promise.all(e)}),this.#tc?"AsSoonAsPossible":"Default"))}cacheStorageContentUpdated({bucketId:e,cacheName:t}){const n=this.#Xd.getBucketById(e)?.bucket;n&&this.dispatchEventToListeners("CacheStorageContentUpdated",{storageBucket:n,cacheName:t})}attributionReportingTriggerRegistered(e){}indexedDBListUpdated(e){}indexedDBContentUpdated(e){}interestGroupAuctionEventOccurred(e){}interestGroupAccessed(e){}interestGroupAuctionNetworkRequestCreated(e){}sharedStorageAccessed(e){}storageBucketCreatedOrUpdated(e){}storageBucketDeleted(e){}setThrottlerSchedulesAsSoonAsPossibleForTest(){this.#tc=!0}attributionReportingSourceRegistered(e){}}class go{#Br;storageKey;storageBucket;cacheName;cacheId;constructor(e,t,n,r){this.#Br=e,this.storageBucket=t,this.storageKey=t.storageKey,this.cacheName=n,this.cacheId=r}inBucket(e){return this.storageKey===e.storageKey&&this.storageBucket.name===e.name}equals(e){return this.cacheId===e.cacheId}toString(){return this.storageKey+this.cacheName}async requestCachedResponse(e,t){const n=await this.#Br.cacheAgent.invoke_requestCachedResponse({cacheId:this.cacheId,requestURL:e,requestHeaders:t});return n.getError()?null:n.response}}h.register(uo,{capabilities:8192,autostart:!1});var po=Object.freeze({__proto__:null,ServiceWorkerCacheModel:uo,Cache:go});const mo={running:"running",starting:"starting",stopped:"stopped",stopping:"stopping",activated:"activated",activating:"activating",installed:"installed",installing:"installing",new:"new",redundant:"redundant",sSS:"{PH1} #{PH2} ({PH3})"},fo=r.i18n.registerUIStrings("core/sdk/ServiceWorkerManager.ts",mo),bo=r.i18n.getLocalizedString.bind(void 0,fo),yo=r.i18n.getLazilyComputedLocalizedString.bind(void 0,fo);class Io extends h{#ws;#nc;#Kn;#rc;serviceWorkerNetworkRequestsPanelStatus;constructor(t){super(t),t.registerServiceWorkerDispatcher(new vo(this)),this.#ws=t.serviceWorkerAgent(),this.#nc=new Map,this.#Kn=!1,this.enable(),this.#rc=e.Settings.Settings.instance().createSetting("service-worker-update-on-reload",!1),this.#rc.get()&&this.forceUpdateSettingChanged(),this.#rc.addChangeListener(this.forceUpdateSettingChanged,this),new Ro(t,this),this.serviceWorkerNetworkRequestsPanelStatus={isOpen:!1,openedAt:0}}async enable(){this.#Kn||(this.#Kn=!0,await this.#ws.invoke_enable())}async disable(){this.#Kn&&(this.#Kn=!1,this.#nc.clear(),await this.#ws.invoke_enable())}registrations(){return this.#nc}hasRegistrationForURLs(e){for(const t of this.#nc.values())if(e.filter((e=>e&&e.startsWith(t.scopeURL))).length===e.length)return!0;return!1}findVersion(e){for(const t of this.registrations().values()){const n=t.versions.get(e);if(n)return n}return null}deleteRegistration(e){const t=this.#nc.get(e);if(t){if(t.isRedundant())return this.#nc.delete(e),void this.dispatchEventToListeners("RegistrationDeleted",t);t.deleting=!0;for(const e of t.versions.values())this.stopWorker(e.id);this.unregister(t.scopeURL)}}async updateRegistration(e){const t=this.#nc.get(e);t&&await this.#ws.invoke_updateRegistration({scopeURL:t.scopeURL})}async deliverPushMessage(t,n){const r=this.#nc.get(t);if(!r)return;const s=e.ParsedURL.ParsedURL.extractOrigin(r.scopeURL);await this.#ws.invoke_deliverPushMessage({origin:s,registrationId:t,data:n})}async dispatchSyncEvent(t,n,r){const s=this.#nc.get(t);if(!s)return;const i=e.ParsedURL.ParsedURL.extractOrigin(s.scopeURL);await this.#ws.invoke_dispatchSyncEvent({origin:i,registrationId:t,tag:n,lastChance:r})}async dispatchPeriodicSyncEvent(t,n){const r=this.#nc.get(t);if(!r)return;const s=e.ParsedURL.ParsedURL.extractOrigin(r.scopeURL);await this.#ws.invoke_dispatchPeriodicSyncEvent({origin:s,registrationId:t,tag:n})}async unregister(e){await this.#ws.invoke_unregister({scopeURL:e})}async startWorker(e){await this.#ws.invoke_startWorker({scopeURL:e})}async skipWaiting(e){await this.#ws.invoke_skipWaiting({scopeURL:e})}async stopWorker(e){await this.#ws.invoke_stopWorker({versionId:e})}async inspectWorker(e){await this.#ws.invoke_inspectWorker({versionId:e})}workerRegistrationUpdated(e){for(const t of e){let e=this.#nc.get(t.registrationId);e?(e.update(t),e.shouldBeRemoved()?(this.#nc.delete(e.id),this.dispatchEventToListeners("RegistrationDeleted",e)):this.dispatchEventToListeners("RegistrationUpdated",e)):(e=new Co(t),this.#nc.set(t.registrationId,e),this.dispatchEventToListeners("RegistrationUpdated",e))}}workerVersionUpdated(e){const t=new Set;for(const n of e){const e=this.#nc.get(n.registrationId);e&&(e.updateVersion(n),t.add(e))}for(const e of t)e.shouldBeRemoved()?(this.#nc.delete(e.id),this.dispatchEventToListeners("RegistrationDeleted",e)):this.dispatchEventToListeners("RegistrationUpdated",e)}workerErrorReported(e){const t=this.#nc.get(e.registrationId);t&&(t.errors.push(e),this.dispatchEventToListeners("RegistrationErrorAdded",{registration:t,error:e}))}forceUpdateOnReloadSetting(){return this.#rc}forceUpdateSettingChanged(){const e=this.#rc.get();this.#ws.invoke_setForceUpdateOnPageLoad({forceUpdateOnPageLoad:e})}}class vo{#q;constructor(e){this.#q=e}workerRegistrationUpdated({registrations:e}){this.#q.workerRegistrationUpdated(e)}workerVersionUpdated({versions:e}){this.#q.workerVersionUpdated(e)}workerErrorReported({errorMessage:e}){this.#q.workerErrorReported(e)}}class ko{runningStatus;status;lastUpdatedTimestamp;previousState;constructor(e,t,n,r){this.runningStatus=e,this.status=t,this.lastUpdatedTimestamp=r,this.previousState=n}}class So{condition;source;id;constructor(e,t,n){this.condition=e,this.source=t,this.id=n}}class wo{id;scriptURL;parsedURL;securityOrigin;scriptLastModified;scriptResponseTime;controlledClients;targetId;routerRules;currentState;registration;constructor(e,t){this.registration=e,this.update(t)}update(t){this.id=t.versionId,this.scriptURL=t.scriptURL;const n=new e.ParsedURL.ParsedURL(t.scriptURL);this.securityOrigin=n.securityOrigin(),this.currentState=new ko(t.runningStatus,t.status,this.currentState,Date.now()),this.scriptLastModified=t.scriptLastModified,this.scriptResponseTime=t.scriptResponseTime,t.controlledClients?this.controlledClients=t.controlledClients.slice():this.controlledClients=[],this.targetId=t.targetId||null,this.routerRules=null,t.routerRules&&(this.routerRules=this.parseJSONRules(t.routerRules))}isStartable(){return!this.registration.isDeleted&&this.isActivated()&&this.isStopped()}isStoppedAndRedundant(){return"stopped"===this.runningStatus&&"redundant"===this.status}isStopped(){return"stopped"===this.runningStatus}isStarting(){return"starting"===this.runningStatus}isRunning(){return"running"===this.runningStatus}isStopping(){return"stopping"===this.runningStatus}isNew(){return"new"===this.status}isInstalling(){return"installing"===this.status}isInstalled(){return"installed"===this.status}isActivating(){return"activating"===this.status}isActivated(){return"activated"===this.status}isRedundant(){return"redundant"===this.status}get status(){return this.currentState.status}get runningStatus(){return this.currentState.runningStatus}mode(){return this.isNew()||this.isInstalling()?"installing":this.isInstalled()?"waiting":this.isActivating()||this.isActivated()?"active":"redundant"}parseJSONRules(e){try{const t=JSON.parse(e);if(!Array.isArray(t))return console.error("Parse error: `routerRules` in ServiceWorkerVersion should be an array"),null;const n=[];for(const e of t){const{condition:t,source:r,id:s}=e;if(void 0===t||void 0===r||void 0===s)return console.error("Parse error: Missing some fields of `routerRules` in ServiceWorkerVersion"),null;n.push(new So(JSON.stringify(t),JSON.stringify(r),s))}return n}catch(e){return console.error("Parse error: Invalid `routerRules` in ServiceWorkerVersion"),null}}}!function(e){e.RunningStatus={running:yo(mo.running),starting:yo(mo.starting),stopped:yo(mo.stopped),stopping:yo(mo.stopping)},e.Status={activated:yo(mo.activated),activating:yo(mo.activating),installed:yo(mo.installed),installing:yo(mo.installing),new:yo(mo.new),redundant:yo(mo.redundant)}}(wo||(wo={}));class Co{#sc;id;scopeURL;securityOrigin;isDeleted;versions;deleting;errors;constructor(e){this.update(e),this.versions=new Map,this.deleting=!1,this.errors=[]}update(t){this.#sc=Symbol("fingerprint"),this.id=t.registrationId,this.scopeURL=t.scopeURL;const n=new e.ParsedURL.ParsedURL(t.scopeURL);this.securityOrigin=n.securityOrigin(),this.isDeleted=t.isDeleted}fingerprint(){return this.#sc}versionsByMode(){const e=new Map;for(const t of this.versions.values())e.set(t.mode(),t);return e}updateVersion(e){this.#sc=Symbol("fingerprint");let t=this.versions.get(e.versionId);return t?(t.update(e),t):(t=new wo(this,e),this.versions.set(e.versionId,t),t)}isRedundant(){for(const e of this.versions.values())if(!e.isStoppedAndRedundant())return!1;return!0}shouldBeRemoved(){return this.isRedundant()&&(!this.errors.length||this.deleting)}canBeRemoved(){return this.isDeleted||this.deleting}clearErrors(){this.#sc=Symbol("fingerprint"),this.errors=[]}}class Ro{#Cn;#ic;#oc;constructor(e,t){this.#Cn=e,this.#ic=t,this.#oc=new Map,t.addEventListener("RegistrationUpdated",this.registrationsUpdated,this),t.addEventListener("RegistrationDeleted",this.registrationsUpdated,this),z.instance().addModelListener(_n,Hn.ExecutionContextCreated,this.executionContextCreated,this)}registrationsUpdated(){this.#oc.clear();const e=this.#ic.registrations().values();for(const t of e)for(const e of t.versions.values())e.targetId&&this.#oc.set(e.targetId,e);this.updateAllContextLabels()}executionContextCreated(e){const t=e.data,n=this.serviceWorkerTargetId(t.target());n&&this.updateContextLabel(t,this.#oc.get(n)||null)}serviceWorkerTargetId(e){return e.parentTarget()!==this.#Cn||e.type()!==B.ServiceWorker?null:e.id()}updateAllContextLabels(){for(const e of z.instance().targets()){const t=this.serviceWorkerTargetId(e);if(!t)continue;const n=this.#oc.get(t)||null,r=e.model(_n),s=r?r.executionContexts():[];for(const e of s)this.updateContextLabel(e,n)}}updateContextLabel(t,n){if(!n)return void t.setLabel("");const r=e.ParsedURL.ParsedURL.fromString(t.origin),s=r?r.lastPathComponentWithFragment():t.name,i=wo.Status[n.status];t.setLabel(bo(mo.sSS,{PH1:s,PH2:n.id,PH3:i()}))}}h.register(Io,{capabilities:16384,autostart:!0});var xo=Object.freeze({__proto__:null,ServiceWorkerManager:Io,ServiceWorkerVersionState:ko,ServiceWorkerRouterRule:So,get ServiceWorkerVersion(){return wo},ServiceWorkerRegistration:Co});class To extends h{#ws;constructor(e){super(e),this.#ws=e.webAuthnAgent(),e.registerWebAuthnDispatcher(new Mo(this))}setVirtualAuthEnvEnabled(e){return e?this.#ws.invoke_enable({enableUI:!0}):this.#ws.invoke_disable()}async addAuthenticator(e){return(await this.#ws.invoke_addVirtualAuthenticator({options:e})).authenticatorId}async removeAuthenticator(e){await this.#ws.invoke_removeVirtualAuthenticator({authenticatorId:e})}async setAutomaticPresenceSimulation(e,t){await this.#ws.invoke_setAutomaticPresenceSimulation({authenticatorId:e,enabled:t})}async getCredentials(e){return(await this.#ws.invoke_getCredentials({authenticatorId:e})).credentials}async removeCredential(e,t){await this.#ws.invoke_removeCredential({authenticatorId:e,credentialId:t})}credentialAdded(e){this.dispatchEventToListeners("CredentialAdded",e)}credentialAsserted(e){this.dispatchEventToListeners("CredentialAsserted",e)}}class Mo{#Br;constructor(e){this.#Br=e}credentialAdded(e){this.#Br.credentialAdded(e)}credentialAsserted(e){this.#Br.credentialAsserted(e)}}h.register(To,{capabilities:65536,autostart:!1});var Po=Object.freeze({__proto__:null,WebAuthnModel:To});export{ks as AccessibilityModel,ws as AutofillModel,ii as CPUProfileDataModel,Gs as CPUProfilerModel,gi as CPUThrottlingManager,We as CSSContainerQuery,ue as CSSFontFace,Ke as CSSLayer,xt as CSSMatchedStyles,Je as CSSMedia,A as CSSMetadata,Nn as CSSModel,nt as CSSProperty,He as CSSPropertyParser,_e as CSSQuery,mt as CSSRule,Ze as CSSScope,st as CSSStyleDeclaration,Et as CSSStyleSheetHeader,ot as CSSSupports,Rs as CategorizedBreakpoint,Fs as ChildTargetManager,qs as CompilerSourceMappingContentProvider,Es as Connections,ri as ConsoleModel,H as Cookie,Jr as CookieModel,es as CookieParser,Si as DOMDebuggerModel,Br as DOMModel,pr as DebuggerModel,di as EmulationModel,Ti as EventBreakpointsModel,Mi as FrameAssociated,Dt as FrameManager,Un as HeapProfilerModel,Qt as IOModel,Ni as IsolateManager,Bi as IssuesModel,Hi as LayerTreeBase,Qs as LogModel,ce as NetworkManager,ys as NetworkRequest,fr as OverlayColorGenerator,Mr as OverlayModel,yr as OverlayPersistentHighlighter,qi as PageLoad,nn as PageResourceLoader,Wi as PaintProfiler,Ki as PerformanceMetricsModel,no as PreloadingModel,D as ProfileTreeModel,ro as ReactNativeApplicationModel,Gt as RemoteObject,_r as Resource,$r as ResourceTreeModel,jn as RuntimeModel,u as SDKModel,io as ScreenCaptureModel,Jn as Script,zr as SecurityOriginManager,rs as ServerSentEventProtocol,ds as ServerTiming,po as ServiceWorkerCacheModel,xo as ServiceWorkerManager,Sn as SourceMap,Rn as SourceMapManager,cn as SourceMapScopes,gn as SourceMapScopesInfo,ao as StorageBucketsModel,Wr as StorageKeyManager,_ as Target,j as TargetManager,Po as WebAuthnModel};
