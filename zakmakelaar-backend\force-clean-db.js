const mongoose = require('mongoose');
const Listing = require('./src/models/Listing');

async function forceCleanDatabase() {
  try {
    await mongoose.connect('mongodb://localhost:27017/zakmakelaar');
    console.log('✅ Connected to MongoDB');
    
    // Count before
    const before = await Listing.countDocuments();
    console.log(`📊 Listings before: ${before}`);
    
    // Remove all listings with prices less than €100
    const result1 = await Listing.deleteMany({
      price: { $regex: /€\s*[1-9][0-9]?\s*per/ } // €1-99 per month
    });
    console.log(`🗑️ Removed ${result1.deletedCount} listings with prices €1-99`);
    
    // Remove listings with undefined rooms
    const result2 = await Listing.deleteMany({
      rooms: { $in: [null, undefined] }
    });
    console.log(`🗑️ Removed ${result2.deletedCount} listings with undefined rooms`);
    
    // Remove listings with very specific bad titles
    const result3 = await Listing.deleteMany({
      title: { $in: ['Hellingkade 86', 'Cabauwsekade 90 A', 'Vecht En Veld 30', 'Kas 59', 'Varenkade 39'] }
    });
    console.log(`🗑️ Removed ${result3.deletedCount} specific bad listings`);
    
    // Count after
    const after = await Listing.countDocuments();
    console.log(`📊 Listings after: ${after}`);
    
    // Show what's left
    console.log('\n📋 Remaining listings:');
    const remaining = await Listing.find().limit(5);
    remaining.forEach((listing, index) => {
      console.log(`${index + 1}. ${listing.title}`);
      console.log(`   Price: ${listing.price}`);
      console.log(`   Location: ${listing.location}`);
      console.log(`   Rooms: ${listing.rooms}`);
      console.log('');
    });
    
    console.log('🎉 Force cleanup completed!');
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await mongoose.connection.close();
    console.log('🔌 Disconnected');
  }
}

forceCleanDatabase();
