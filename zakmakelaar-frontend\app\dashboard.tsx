import React from 'react';
import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  SafeAreaView,
  ScrollView,
  Image,
} from 'react-native';
import { useRouter } from 'expo-router';

// Header Component
const Header = ({ title }: { title: string }) => (
  <View style={styles.header}>
    <View style={styles.headerCenter}>
      <View style={styles.logoContainer}>
        <Text style={styles.logoText}>ZM</Text>
      </View>
      <Text style={styles.headerTitle}>ZakMakelaar</Text>
    </View>
  </View>
);

export default function DashboardScreen() {
  const router = useRouter();

  // Mock listings data
  const listings = [
    {
      id: 1,
      title: "Spacious Apartment in Amsterdam",
      price: "€1,800/month",
      location: "Amsterdam-Zuid",
      bedrooms: 2,
      imageUrl: "https://placehold.co/300x200/e0e0e0/333333?text=Apartment+1",
    },
    {
      id: 2,
      title: "Cozy Studio in Utrecht",
      price: "€950/month",
      location: "Utrecht Centrum",
      bedrooms: 1,
      imageUrl: "https://placehold.co/300x200/d0d0d0/333333?text=Apartment+2",
    },
    {
      id: 3,
      title: "Family House in Rotterdam",
      price: "€2,500/month",
      location: "Rotterdam Hillegersberg",
      bedrooms: 3,
      imageUrl: "https://placehold.co/300x200/c0c0c0/333333?text=House+1",
    },
  ];

  return (
    <SafeAreaView style={styles.container}>
      <Header title="Your Dashboard" />
      <ScrollView style={styles.scrollContainer} contentContainerStyle={styles.dashboardContainer}>
        <Text style={styles.dashboardTitle}>Your Matches</Text>

        {/* Notifications Section */}
        <View style={styles.notificationContainer}>
          <Text style={styles.notificationIcon}>🔔</Text>
          <Text style={styles.notificationText}>
            New listing matching your criteria in Amsterdam!
          </Text>
        </View>

        {/* Listings Grid */}
        <View style={styles.listingsContainer}>
          {listings.map((listing) => (
            <TouchableOpacity
              key={listing.id}
              style={styles.listingCard}
              onPress={() => router.push('/listing-details')}
            >
              <Image
                source={{ uri: listing.imageUrl }}
                style={styles.listingImage}
              />
              <View style={styles.listingContent}>
                <Text style={styles.listingTitle}>{listing.title}</Text>
                <Text style={styles.listingPrice}>{listing.price}</Text>
                <Text style={styles.listingDetails}>
                  {listing.location} • {listing.bedrooms} bedrooms
                </Text>
              </View>
            </TouchableOpacity>
          ))}
        </View>
      </ScrollView>

      {/* Bottom Navigation */}
      <View style={styles.bottomNavigation}>
        <TouchableOpacity
          onPress={() => router.push('/dashboard')}
          style={styles.navButton}
        >
          <Text style={[styles.navIcon, styles.navIconActive]}>🏠</Text>
          <Text style={[styles.navLabel, styles.navLabelActive]}>Home</Text>
        </TouchableOpacity>
        <TouchableOpacity
          onPress={() => router.push('/application')}
          style={styles.navButton}
        >
          <Text style={styles.navIcon}>📄</Text>
          <Text style={styles.navLabel}>Apply</Text>
        </TouchableOpacity>
        <TouchableOpacity
          onPress={() => router.push('/contract-review')}
          style={styles.navButton}
        >
          <Text style={styles.navIcon}>⚖️</Text>
          <Text style={styles.navLabel}>Contract</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.navButton}>
          <Text style={styles.navIcon}>👤</Text>
          <Text style={styles.navLabel}>Profile</Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f3f4f6',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
    padding: 16,
    backgroundColor: '#ffffff',
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
  },
  headerCenter: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  logoContainer: {
    width: 32,
    height: 32,
    backgroundColor: '#f72585',
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  logoText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#ffffff',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1f2937',
  },
  scrollContainer: {
    flex: 1,
  },
  dashboardContainer: {
    padding: 24,
    backgroundColor: '#ffffff',
    margin: 16,
    marginTop: 0,
    borderBottomLeftRadius: 16,
    borderBottomRightRadius: 16,
  },
  dashboardTitle: {
    fontSize: 24,
    fontWeight: '600',
    color: '#1f2937',
    marginBottom: 24,
  },
  notificationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#dbeafe',
    borderWidth: 1,
    borderColor: '#93c5fd',
    padding: 16,
    borderRadius: 12,
    marginBottom: 24,
  },
  notificationIcon: {
    fontSize: 16,
    marginRight: 12,
  },
  notificationText: {
    fontSize: 14,
    color: '#1e40af',
    flex: 1,
  },
  listingsContainer: {
    gap: 24,
  },
  listingCard: {
    backgroundColor: '#f9fafb',
    borderRadius: 12,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: '#f3f4f6',
  },
  listingImage: {
    width: '100%',
    height: 160,
  },
  listingContent: {
    padding: 16,
  },
  listingTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1f2937',
    marginBottom: 4,
  },
  listingPrice: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#f72585',
    marginBottom: 8,
  },
  listingDetails: {
    fontSize: 14,
    color: '#6b7280',
  },
  bottomNavigation: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    padding: 16,
    backgroundColor: '#ffffff',
    borderTopWidth: 1,
    borderTopColor: '#e5e7eb',
    borderBottomLeftRadius: 16,
    borderBottomRightRadius: 16,
  },
  navButton: {
    alignItems: 'center',
  },
  navIcon: {
    fontSize: 20,
    color: '#6b7280',
    marginBottom: 4,
  },
  navIconActive: {
    color: '#f72585',
  },
  navLabel: {
    fontSize: 12,
    color: '#6b7280',
  },
  navLabelActive: {
    color: '#f72585',
  },
});
