const mongoose = require("mongoose");
const Listing = require("./src/models/Listing");

// Connect to MongoDB
mongoose.connect("mongodb://localhost:27017/zakmakelaar");

async function fixListingData() {
  try {
    console.log("🔧 Fixing listing data...");

    // Get all listings
    const listings = await Listing.find();
    console.log(`📊 Found ${listings.length} listings to fix`);

    for (let listing of listings) {
      const updates = {};

      // Fix location if undefined
      if (!listing.location) {
        updates.location = "Amsterdam, Noord-Holland";
      }

      // Fix rooms if undefined
      if (!listing.rooms) {
        updates.rooms = "3";
      }

      // Fix price format if needed
      if (listing.price && !listing.price.includes("per maand")) {
        // Convert low prices to realistic ones
        const priceMatch = listing.price.match(/€\s*(\d+)/);
        if (priceMatch) {
          const priceNum = parseInt(priceMatch[1]);
          if (priceNum < 100) {
            // These are test prices, make them realistic
            updates.price = `€ ${priceNum * 500} per maand`;
          } else if (!listing.price.includes("per maand")) {
            updates.price = listing.price + " per maand";
          }
        }
      }

      // Add description if missing
      if (!listing.description || listing.description.length < 50) {
        updates.description = `Beautiful ${
          listing.propertyType || "property"
        } in ${
          listing.location || "a great location"
        }. This property offers modern amenities and excellent value for money. Perfect for professionals or families looking for quality accommodation.`;
      }

      // Add images if missing
      if (!listing.images || listing.images.length === 0) {
        updates.images = [
          "https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?w=600&h=400&fit=crop",
          "https://images.unsplash.com/photo-1560448075-bb485b067938?w=600&h=400&fit=crop",
        ];
      }

      // Add property type if missing
      if (!listing.propertyType) {
        updates.propertyType = "Apartment";
      }

      // Add size if missing
      if (!listing.size) {
        updates.size = "75 m²";
      }

      // Add year if missing
      if (!listing.year) {
        updates.year = "2018";
      }

      // Add interior if missing
      if (!listing.interior) {
        updates.interior = "Gestoffeerd";
      }

      // Update the listing if there are changes
      if (Object.keys(updates).length > 0) {
        await Listing.findByIdAndUpdate(listing._id, updates);
        console.log(`✅ Updated listing: ${listing.title}`);
        console.log(`   Updates: ${Object.keys(updates).join(", ")}`);
      }
    }

    // Show final results
    const updatedListings = await Listing.find().limit(3);
    console.log("\n📋 Sample updated listings:");
    updatedListings.forEach((listing, index) => {
      console.log(`${index + 1}. ${listing.title}`);
      console.log(`   Price: ${listing.price}`);
      console.log(`   Location: ${listing.location}`);
      console.log(`   Rooms: ${listing.rooms}`);
      console.log(`   Images: ${listing.images?.length || 0}`);
    });

    console.log("\n🎉 Listing data fix completed!");
  } catch (error) {
    console.error("❌ Error fixing listing data:", error);
  } finally {
    mongoose.connection.close();
  }
}

fixListingData();
