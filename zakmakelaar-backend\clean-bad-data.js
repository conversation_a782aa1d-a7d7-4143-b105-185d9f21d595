const mongoose = require('mongoose');
const Listing = require('./src/models/Listing');

async function cleanBadData() {
  try {
    await mongoose.connect('mongodb://localhost:27017/zakmakelaar');
    console.log('✅ Connected to MongoDB');
    
    // Count current listings
    const totalBefore = await Listing.countDocuments();
    console.log(`📊 Total listings before cleanup: ${totalBefore}`);
    
    // Find and remove listings with very low prices (test data)
    const badPricePattern = /€\s*[1-9]\s*per\s*maand/; // €1-9 per maand
    const badListings = await Listing.find({ 
      price: { $regex: badPricePattern } 
    });
    
    console.log(`🗑️ Found ${badListings.length} listings with unrealistic prices`);
    
    if (badListings.length > 0) {
      console.log('Sample bad listings to be removed:');
      badListings.slice(0, 5).forEach((listing, index) => {
        console.log(`${index + 1}. ${listing.title} - ${listing.price}`);
      });
      
      // Remove bad listings
      const deleteResult = await Listing.deleteMany({ 
        price: { $regex: badPricePattern } 
      });
      
      console.log(`✅ Removed ${deleteResult.deletedCount} bad listings`);
    }
    
    // Count after cleanup
    const totalAfter = await Listing.countDocuments();
    console.log(`📊 Total listings after cleanup: ${totalAfter}`);
    
    // Show remaining good listings
    console.log('\n📋 Remaining listings (first 5):');
    const goodListings = await Listing.find().limit(5);
    goodListings.forEach((listing, index) => {
      console.log(`${index + 1}. ${listing.title}`);
      console.log(`   Price: ${listing.price}`);
      console.log(`   Location: ${listing.location}`);
      console.log(`   Rooms: ${listing.rooms}`);
      console.log('');
    });
    
    console.log('🎉 Database cleanup completed!');
    console.log('💡 The API should now return good data first.');
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await mongoose.connection.close();
    console.log('🔌 Disconnected from MongoDB');
  }
}

cleanBadData();
