const mongoose = require('mongoose');
const Listing = require('./src/models/Listing');

async function debugDatabaseAndAPI() {
  try {
    await mongoose.connect('mongodb://localhost:27017/zakmakelaar');
    console.log('✅ Connected to MongoDB');
    
    // Check total count
    const totalCount = await Listing.countDocuments();
    console.log(`📊 Total listings in database: ${totalCount}`);
    
    // Check what the API query returns (default sort)
    console.log('\n🔍 What API returns (default query):');
    const apiResults = await Listing.find().limit(3);
    apiResults.forEach((listing, index) => {
      console.log(`${index + 1}. ${listing.title}`);
      console.log(`   ID: ${listing._id}`);
      console.log(`   Price: ${listing.price}`);
      console.log(`   Location: ${listing.location}`);
      console.log(`   Rooms: ${listing.rooms}`);
      console.log(`   Date Added: ${listing.dateAdded}`);
      console.log('');
    });
    
    // Check what we expect (our sample data)
    console.log('\n🎯 Looking for our sample data:');
    const sampleResults = await Listing.find({
      title: { $regex: /Modern Apartment in Amsterdam|Spacious House in Utrecht|Cozy Studio in Rotterdam/ }
    }).limit(3);
    
    if (sampleResults.length > 0) {
      console.log('✅ Found our sample data:');
      sampleResults.forEach((listing, index) => {
        console.log(`${index + 1}. ${listing.title}`);
        console.log(`   ID: ${listing._id}`);
        console.log(`   Price: ${listing.price}`);
        console.log(`   Location: ${listing.location}`);
        console.log(`   Rooms: ${listing.rooms}`);
        console.log(`   Date Added: ${listing.dateAdded}`);
        console.log('');
      });
    } else {
      console.log('❌ Our sample data not found!');
    }
    
    // Check sorting - API might be using different sort
    console.log('\n📅 Checking by date (newest first):');
    const newestResults = await Listing.find().sort({ dateAdded: -1 }).limit(3);
    newestResults.forEach((listing, index) => {
      console.log(`${index + 1}. ${listing.title}`);
      console.log(`   Price: ${listing.price}`);
      console.log(`   Date Added: ${listing.dateAdded}`);
      console.log('');
    });
    
    // Check if there are multiple collections or data sources
    console.log('\n🔍 Checking data distribution:');
    const priceRanges = await Listing.aggregate([
      {
        $group: {
          _id: null,
          lowPrices: { $sum: { $cond: [{ $regexMatch: { input: "$price", regex: /€\s*[1-9]\s*per/ } }, 1, 0] } },
          normalPrices: { $sum: { $cond: [{ $regexMatch: { input: "$price", regex: /€\s*[1-9][0-9]{2,3}/ } }, 1, 0] } },
          withRooms: { $sum: { $cond: [{ $ne: ["$rooms", null] }, 1, 0] } },
          withoutRooms: { $sum: { $cond: [{ $eq: ["$rooms", null] }, 1, 0] } }
        }
      }
    ]);
    
    console.log('Price distribution:', priceRanges[0]);
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await mongoose.connection.close();
    console.log('🔌 Disconnected from MongoDB');
  }
}

debugDatabaseAndAPI();
