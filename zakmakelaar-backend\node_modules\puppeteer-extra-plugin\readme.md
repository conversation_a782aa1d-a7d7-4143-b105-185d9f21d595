# puppeteer-extra-plugin [![GitHub Workflow Status](https://img.shields.io/github/actions/workflow/status/berstend/puppeteer-extra/test.yml?branch=master&event=push)](https://github.com/berstend/puppeteer-extra/actions) [![Discord](https://img.shields.io/discord/737009125862408274)](https://extra.community) [![npm](https://img.shields.io/npm/v/puppeteer-extra-plugin.svg)](https://www.npmjs.com/package/puppeteer-extra-plugin)

## Installation

```bash
yarn add puppeteer-extra-plugin
```

## Changelog

<details>
 <summary><strong>v3.0.1</strong></summary><br>

- Now written in TypeScript 🎉
- **Breaking change:** Now using a named export:

```js
// Before
const PuppeteerExtraPlugin = require('puppeteer-extra-plugin')

// After (>= v3.0.1)
const { PuppeteerExtraPlugin } = require('puppeteer-extra-plugin')
```

</details>

## API

<!-- Generated by documentation.js. Update this documentation by updating the source code. -->

#### Table of Contents

- [puppeteer-extra-plugin ![GitHub Workflow Status](https://github.com/berstend/puppeteer-extra/actions) [![Discord](https://img.shields.io/discord/737009125862408274)](https://extra.community) [![npm](https://img.shields.io/npm/v/puppeteer-extra-plugin.svg)](https://www.npmjs.com/package/puppeteer-extra-plugin)](#puppeteer-extra-plugin---)
  - [Installation](#installation)
  - [Changelog](#changelog)
  - [API](#api)
    - [Table of Contents](#table-of-contents)
    - [class: PuppeteerExtraPlugin](#class-puppeteerextraplugin)
      - [.name](#name)
      - [.defaults](#defaults)
      - [.requirements](#requirements)
      - [.dependencies](#dependencies)
      - [.data](#data)
      - [.opts](#opts)
      - [.debug](#debug)
      - [.beforeLaunch(options)](#beforelaunchoptions)
      - [.afterLaunch(browser, opts)](#afterlaunchbrowser-opts)
      - [.beforeConnect(options)](#beforeconnectoptions)
      - [.afterConnect(browser, opts)](#afterconnectbrowser-opts)
      - [.onBrowser(browser, opts)](#onbrowserbrowser-opts)
      - [.onTargetCreated(target)](#ontargetcreatedtarget)
      - [.onPageCreated(page, target)](#onpagecreatedpage-target)
      - [.onTargetChanged(target)](#ontargetchangedtarget)
      - [.onTargetDestroyed(target)](#ontargetdestroyedtarget)
      - [.onDisconnected()](#ondisconnected)
      - [.onClose()](#onclose)
      - [.onPluginRegistered()](#onpluginregistered)
      - [.getDataFromPlugins(name?)](#getdatafrompluginsname)

### class: [PuppeteerExtraPlugin](https://github.com/berstend/puppeteer-extra/blob/dc8b90260a927c0c66c4585c5a56092ea9c35049/packages/puppeteer-extra-plugin/src/index.ts#L65-L572)

- `opts` **PluginOptions?**

Base class for `puppeteer-extra` plugins.

Provides convenience methods to avoid boilerplate.

All common `puppeteer` browser events will be bound to
the plugin instance, if a respectively named class member is found.

Please refer to the [puppeteer API documentation](https://github.com/GoogleChrome/puppeteer/blob/master/docs/api.md) as well.

Example:

```javascript
// hello-world-plugin.js
const { PuppeteerExtraPlugin } = require('puppeteer-extra-plugin')

class Plugin extends PuppeteerExtraPlugin {
  constructor(opts = {}) {
    super(opts)
  }

  get name() {
    return 'hello-world'
  }

  async onPageCreated(page) {
    this.debug('page created', page.url())
    const ua = await page.browser().userAgent()
    this.debug('user agent', ua)
  }
}

module.exports = function(pluginConfig) {
  return new Plugin(pluginConfig)
}

// foo.js
const puppeteer = require('puppeteer-extra')
puppeteer.use(require('./hello-world-plugin')())
;(async () => {
  const browser = await puppeteer.launch({ headless: false })
  const page = await browser.newPage()
  await page.goto('http://example.com', { waitUntil: 'domcontentloaded' })
  await browser.close()
})()
```

---

#### .[name](https://github.com/berstend/puppeteer-extra/blob/dc8b90260a927c0c66c4585c5a56092ea9c35049/packages/puppeteer-extra-plugin/src/index.ts#L92-L94)

Type: **[string](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)**

Plugin name (required).

Convention:

- Package: `puppeteer-extra-plugin-anonymize-ua`
- Name: `anonymize-ua`

Example:

```javascript
get name () { return 'anonymize-ua' }
```

---

#### .[defaults](https://github.com/berstend/puppeteer-extra/blob/dc8b90260a927c0c66c4585c5a56092ea9c35049/packages/puppeteer-extra-plugin/src/index.ts#L117-L119)

Type: **PluginOptions**

Plugin defaults (optional).

If defined will be ([deep-](https://github.com/jonschlinkert/merge-deep))merged with the (optional) user supplied options (supplied during plugin instantiation).

The result of merging defaults with user supplied options can be accessed through `this.opts`.

Example:

```javascript
get defaults () {
  return {
    stripHeadless: true,
    makeWindows: true,
    customFn: null
  }
}

// Users can overwrite plugin defaults during instantiation:
puppeteer.use(require('puppeteer-extra-plugin-foobar')({ makeWindows: false }))
```

- **See: \[[opts]]**

---

#### .[requirements](https://github.com/berstend/puppeteer-extra/blob/dc8b90260a927c0c66c4585c5a56092ea9c35049/packages/puppeteer-extra-plugin/src/index.ts#L145-L147)

Type: **PluginRequirements**

Plugin requirements (optional).

Signal certain plugin requirements to the base class and the user.

Currently supported:

- `launch`
  - If the plugin only supports locally created browser instances (no `puppeteer.connect()`),
    will output a warning to the user.
- `headful`
  - If the plugin doesn't work in `headless: true` mode,
    will output a warning to the user.
- `dataFromPlugins`
  - In case the plugin requires data from other plugins.
    will enable usage of `this.getDataFromPlugins()`.
- `runLast`
  - In case the plugin prefers to run after the others.
    Useful when the plugin needs data from others.

Example:

```javascript
get requirements () {
  return new Set(['runLast', 'dataFromPlugins'])
}
```

---

#### .[dependencies](https://github.com/berstend/puppeteer-extra/blob/dc8b90260a927c0c66c4585c5a56092ea9c35049/packages/puppeteer-extra-plugin/src/index.ts#L160-L162)

Type: **PluginDependencies**

Plugin dependencies (optional).

Missing plugins will be required() by puppeteer-extra.

Example:

```javascript
get dependencies () {
  return new Set(['user-preferences'])
}
// Will ensure the 'puppeteer-extra-plugin-user-preferences' plugin is loaded.
```

---

#### .[data](https://github.com/berstend/puppeteer-extra/blob/dc8b90260a927c0c66c4585c5a56092ea9c35049/packages/puppeteer-extra-plugin/src/index.ts#L196-L198)

Type: **[Array](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array)&lt;PluginData>**

Plugin data (optional).

Plugins can expose data (an array of objects), which in turn can be consumed by other plugins,
that list the `dataFromPlugins` requirement (by using `this.getDataFromPlugins()`).

Convention: `[ {name: 'Any name', value: 'Any value'} ]`

Example:

```javascript
// plugin1.js
get data () {
  return [
    {
      name: 'userPreferences',
      value: { foo: 'bar' }
    },
    {
      name: 'userPreferences',
      value: { hello: 'world' }
    }
  ]

// plugin2.js
get requirements () { return new Set(['dataFromPlugins']) }

async beforeLaunch () {
  const prefs = this.getDataFromPlugins('userPreferences').map(d => d.value)
  this.debug(prefs) // => [ { foo: 'bar' }, { hello: 'world' } ]
}
```

- **See: \[[getDataFromPlugins]]**

---

#### .[opts](https://github.com/berstend/puppeteer-extra/blob/dc8b90260a927c0c66c4585c5a56092ea9c35049/packages/puppeteer-extra-plugin/src/index.ts#L215-L217)

Type: **PluginOptions**

Access the plugin options (usually the `defaults` merged with user defined options)

To skip the auto-merging of defaults with user supplied opts don't define a `defaults`
property and set the `this._opts` Object in your plugin constructor directly.

Example:

```javascript
get defaults () { return { foo: "bar" } }

async onPageCreated (page) {
  this.debug(this.opts.foo) // => bar
}
```

- **See: \[[defaults]]**

---

#### .[debug](https://github.com/berstend/puppeteer-extra/blob/dc8b90260a927c0c66c4585c5a56092ea9c35049/packages/puppeteer-extra-plugin/src/index.ts#L235-L237)

Type: **Debugger**

Convenience debug logger based on the [debug] module.
Will automatically namespace the logging output to the plugin package name.

[debug]: https://www.npmjs.com/package/debug

```bash
# toggle output using environment variables
DEBUG=puppeteer-extra-plugin:<plugin_name> node foo.js
# to debug all the things:
DEBUG=puppeteer-extra,puppeteer-extra-plugin:* node foo.js
```

Example:

```javascript
this.debug('hello world')
// will output e.g. 'puppeteer-extra-plugin:anonymize-ua hello world'
```

---

#### .[beforeLaunch(options)](https://github.com/berstend/puppeteer-extra/blob/dc8b90260a927c0c66c4585c5a56092ea9c35049/packages/puppeteer-extra-plugin/src/index.ts#L256-L258)

- `options` **any** Puppeteer launch options

Before a new browser instance is created/launched.

Can be used to modify the puppeteer launch options by modifying or returning them.

Plugins using this method will be called in sequence to each
be able to update the launch options.

Example:

```javascript
async beforeLaunch (options) {
  if (this.opts.flashPluginPath) {
    options.args.push(`--ppapi-flash-path=${this.opts.flashPluginPath}`)
  }
}
```

---

#### .[afterLaunch(browser, opts)](https://github.com/berstend/puppeteer-extra/blob/dc8b90260a927c0c66c4585c5a56092ea9c35049/packages/puppeteer-extra-plugin/src/index.ts#L287-L292)

- `browser` **Puppeteer.Browser** The `puppeteer` browser instance.
- `opts` (optional, default `{options:({}as Puppeteer.LaunchOptions)}`)

After the browser has launched.

Note: Don't assume that there will only be a single browser instance during the lifecycle of a plugin.
It's possible that `pupeeteer.launch` will be called multiple times and more than one browser created.
In order to make the plugins as stateless as possible don't store a reference to the browser instance
in the plugin but rather consider alternatives.

E.g. when using `onPageCreated` you can get a browser reference by using `page.browser()`.

Alternatively you could expose a class method that takes a browser instance as a parameter to work with:

```es6
const fancyPlugin = require('puppeteer-extra-plugin-fancy')()
puppeteer.use(fancyPlugin)
const browser = await puppeteer.launch()
await fancyPlugin.killBrowser(browser)
```

Example:

```javascript
async afterLaunch (browser, opts) {
  this.debug('browser has been launched', opts.options)
}
```

---

#### .[beforeConnect(options)](https://github.com/berstend/puppeteer-extra/blob/dc8b90260a927c0c66c4585c5a56092ea9c35049/packages/puppeteer-extra-plugin/src/index.ts#L305-L307)

- `options` **[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)** Puppeteer connect options

Before connecting to an existing browser instance.

Can be used to modify the puppeteer connect options by modifying or returning them.

Plugins using this method will be called in sequence to each
be able to update the launch options.

---

#### .[afterConnect(browser, opts)](https://github.com/berstend/puppeteer-extra/blob/dc8b90260a927c0c66c4585c5a56092ea9c35049/packages/puppeteer-extra-plugin/src/index.ts#L319-L321)

- `browser` **Puppeteer.Browser** The `puppeteer` browser instance.
- `opts` **[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)** (optional, default `{}`)
  - `opts.options` **[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)** Puppeteer connect options used.

After connecting to an existing browser instance.

> Note: Don't assume that there will only be a single browser instance during the lifecycle of a plugin.

---

#### .[onBrowser(browser, opts)](https://github.com/berstend/puppeteer-extra/blob/dc8b90260a927c0c66c4585c5a56092ea9c35049/packages/puppeteer-extra-plugin/src/index.ts#L335-L337)

- `browser` **Puppeteer.Browser** The `puppeteer` browser instance.
- `opts` **any**

Returns: **[Promise](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Promise)&lt;void>**

Called when a browser instance is available.

This applies to both `puppeteer.launch()` and `puppeteer.connect()`.

Convenience method created for plugins that need access to a browser instance
and don't mind if it has been created through `launch` or `connect`.

> Note: Don't assume that there will only be a single browser instance during the lifecycle of a plugin.

---

#### .[onTargetCreated(target)](https://github.com/berstend/puppeteer-extra/blob/dc8b90260a927c0c66c4585c5a56092ea9c35049/packages/puppeteer-extra-plugin/src/index.ts#L348-L350)

- `target` **Puppeteer.Target**

Called when a target is created, for example when a new page is opened by window.open or browser.newPage.

> Note: This includes target creations in incognito browser contexts.
>
> Note: This includes browser instances created through `.launch()` as well as `.connect()`.

---

#### .[onPageCreated(page, target)](https://github.com/berstend/puppeteer-extra/blob/dc8b90260a927c0c66c4585c5a56092ea9c35049/packages/puppeteer-extra-plugin/src/index.ts#L371-L373)

- `page` **Puppeteer.Page**
- `target` **Puppeteer.Target**

Same as `onTargetCreated` but prefiltered to only contain Pages, for convenience.

> Note: This includes page creations in incognito browser contexts.
>
> Note: This includes browser instances created through `.launch()` as well as `.connect()`.

Example:

```javascript
async onPageCreated (page) {
  let ua = await page.browser().userAgent()
  if (this.opts.stripHeadless) {
    ua = ua.replace('HeadlessChrome/', 'Chrome/')
  }
  this.debug('new ua', ua)
  await page.setUserAgent(ua)
}
```

---

#### .[onTargetChanged(target)](https://github.com/berstend/puppeteer-extra/blob/dc8b90260a927c0c66c4585c5a56092ea9c35049/packages/puppeteer-extra-plugin/src/index.ts#L384-L386)

- `target` **Puppeteer.Target**

Called when the url of a target changes.

> Note: This includes target changes in incognito browser contexts.
>
> Note: This includes browser instances created through `.launch()` as well as `.connect()`.

---

#### .[onTargetDestroyed(target)](https://github.com/berstend/puppeteer-extra/blob/dc8b90260a927c0c66c4585c5a56092ea9c35049/packages/puppeteer-extra-plugin/src/index.ts#L397-L399)

- `target` **Puppeteer.Target**

Called when a target is destroyed, for example when a page is closed.

> Note: This includes target destructions in incognito browser contexts.
>
> Note: This includes browser instances created through `.launch()` as well as `.connect()`.

---

#### .[onDisconnected()](https://github.com/berstend/puppeteer-extra/blob/dc8b90260a927c0c66c4585c5a56092ea9c35049/packages/puppeteer-extra-plugin/src/index.ts#L408-L410)

Called when Puppeteer gets disconnected from the Chromium instance.

This might happen because of one of the following:

- Chromium is closed or crashed
- The `browser.disconnect` method was called

---

#### .[onClose()](https://github.com/berstend/puppeteer-extra/blob/dc8b90260a927c0c66c4585c5a56092ea9c35049/packages/puppeteer-extra-plugin/src/index.ts#L424-L426)

**Deprecated:** Since puppeteer v1.6.0 `onDisconnected` has been improved
and should be used instead of `onClose`.

In puppeteer &lt; v1.6.0 `onDisconnected` was not catching all exit scenarios.
In order for plugins to clean up properly (e.g. deleting temporary files)
the `onClose` method had been introduced.

> Note: Might be called multiple times on exit.
>
> Note: This only includes browser instances created through `.launch()`.

---

#### .[onPluginRegistered()](https://github.com/berstend/puppeteer-extra/blob/dc8b90260a927c0c66c4585c5a56092ea9c35049/packages/puppeteer-extra-plugin/src/index.ts#L433-L435)

After the plugin has been registered in `puppeteer-extra`.

Normally right after `puppeteer.use(plugin)` is called

---

#### .[getDataFromPlugins(name?)](https://github.com/berstend/puppeteer-extra/blob/dc8b90260a927c0c66c4585c5a56092ea9c35049/packages/puppeteer-extra-plugin/src/index.ts#L448-L450)

- `name` **[string](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)?** Filter data by `name` property

Returns: **[Array](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array)&lt;PluginData>**

Helper method to retrieve `data` objects from other plugins.

A plugin needs to state the `dataFromPlugins` requirement
in order to use this method. Will be mapped to `puppeteer.getPluginData`.

- **See: [data]**
- **See: [requirements]**

---
