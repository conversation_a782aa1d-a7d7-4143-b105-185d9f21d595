const {
  scrapeFunda,
  scrapePararius,
  scrapeHuurwoningen,
  getScrapingMetrics,
} = require("../services/scraper");
const { catchAsync, AppError } = require("../middleware/errorHandler");

exports.scrape = catchAsync(async (req, res, next) => {
  console.log("Manual scraping triggered by user");

  // Run all three scrapers in parallel
  const [fundaResult, pariusResult, huurwoningenResult] =
    await Promise.allSettled([
      scrapeFunda(),
      scrapePararius(),
      scrapeHuurwoningen(),
    ]);

  let allListings = [];
  let errors = [];

  if (fundaResult.status === "fulfilled") {
    allListings = allListings.concat(fundaResult.value || []);
  } else {
    errors.push({ source: "Funda", error: fundaResult.reason.message });
  }

  if (pariusResult.status === "fulfilled") {
    allListings = allListings.concat(pariusResult.value || []);
  } else {
    errors.push({ source: "Pararius", error: pariusResult.reason.message });
  }

  if (huurwoningenResult.status === "fulfilled") {
    allListings = allListings.concat(huurwoningenResult.value || []);
  } else {
    errors.push({
      source: "Huurwoningen",
      error: huurwoningenResult.reason.message,
    });
  }

  if (allListings.length === 0) {
    return next(new AppError("No listings were found during scraping", 404));
  }

  res.status(200).json({
    status: "success",
    message: "Scraping completed successfully",
    results: allListings.length,
    data: {
      listings: allListings,
      errors: errors.length > 0 ? errors : undefined,
    },
    metrics: getScrapingMetrics(),
  });
});

exports.getMetrics = catchAsync(async (req, res, next) => {
  const metrics = getScrapingMetrics();

  res.status(200).json({
    status: "success",
    data: {
      metrics,
    },
  });
});
