const cacheService = require('./src/services/cacheService');

async function clearCache() {
  try {
    console.log('🧹 Clearing API cache...');
    
    // Wait a moment for cache service to initialize
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Clear all cache
    const result = await cacheService.flush();
    
    if (result) {
      console.log('✅ Cache cleared successfully!');
    } else {
      console.log('⚠️ Cache clear failed or Redis not connected');
    }
    
    // Also try to clear specific listing patterns
    await cacheService.delPattern('api:*listings*');
    console.log('🗑️ Cleared listing-specific cache patterns');
    
    console.log('🎉 Cache cleanup completed!');
    
  } catch (error) {
    console.error('❌ Error clearing cache:', error);
  } finally {
    process.exit(0);
  }
}

clearCache();
