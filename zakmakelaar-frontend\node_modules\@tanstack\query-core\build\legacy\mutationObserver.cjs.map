{"version": 3, "sources": ["../../src/mutationObserver.ts"], "sourcesContent": ["import { getDefaultState } from './mutation'\nimport { notify<PERSON>anager } from './notifyManager'\nimport { Subscribable } from './subscribable'\nimport { hashKey, shallowEqualObjects } from './utils'\nimport type { QueryClient } from './queryClient'\nimport type {\n  DefaultError,\n  MutateOptions,\n  MutationObserverOptions,\n  MutationObserverResult,\n} from './types'\nimport type { Action, Mutation } from './mutation'\n\n// TYPES\n\ntype MutationObserverListener<TData, TError, TVariables, TContext> = (\n  result: MutationObserverResult<TData, TError, TVariables, TContext>,\n) => void\n\n// CLASS\n\nexport class MutationObserver<\n  TData = unknown,\n  TError = DefaultError,\n  TVariables = void,\n  TContext = unknown,\n> extends Subscribable<\n  MutationObserverListener<TData, TError, TVariables, TContext>\n> {\n  options!: MutationObserverOptions<TData, TError, TVariables, TContext>\n\n  #client: QueryClient\n  #currentResult: MutationObserverResult<TData, TError, TVariables, TContext> =\n    undefined!\n  #currentMutation?: Mutation<TData, TError, TVariables, TContext>\n  #mutateOptions?: MutateOptions<TData, TError, TVariables, TContext>\n\n  constructor(\n    client: QueryClient,\n    options: MutationObserverOptions<TData, TError, TVariables, TContext>,\n  ) {\n    super()\n\n    this.#client = client\n    this.setOptions(options)\n    this.bindMethods()\n    this.#updateResult()\n  }\n\n  protected bindMethods(): void {\n    this.mutate = this.mutate.bind(this)\n    this.reset = this.reset.bind(this)\n  }\n\n  setOptions(\n    options: MutationObserverOptions<TData, TError, TVariables, TContext>,\n  ) {\n    const prevOptions = this.options as\n      | MutationObserverOptions<TData, TError, TVariables, TContext>\n      | undefined\n    this.options = this.#client.defaultMutationOptions(options)\n    if (!shallowEqualObjects(this.options, prevOptions)) {\n      this.#client.getMutationCache().notify({\n        type: 'observerOptionsUpdated',\n        mutation: this.#currentMutation,\n        observer: this,\n      })\n    }\n\n    if (\n      prevOptions?.mutationKey &&\n      this.options.mutationKey &&\n      hashKey(prevOptions.mutationKey) !== hashKey(this.options.mutationKey)\n    ) {\n      this.reset()\n    } else if (this.#currentMutation?.state.status === 'pending') {\n      this.#currentMutation.setOptions(this.options)\n    }\n  }\n\n  protected onUnsubscribe(): void {\n    if (!this.hasListeners()) {\n      this.#currentMutation?.removeObserver(this)\n    }\n  }\n\n  onMutationUpdate(action: Action<TData, TError, TVariables, TContext>): void {\n    this.#updateResult()\n\n    this.#notify(action)\n  }\n\n  getCurrentResult(): MutationObserverResult<\n    TData,\n    TError,\n    TVariables,\n    TContext\n  > {\n    return this.#currentResult\n  }\n\n  reset(): void {\n    // reset needs to remove the observer from the mutation because there is no way to \"get it back\"\n    // another mutate call will yield a new mutation!\n    this.#currentMutation?.removeObserver(this)\n    this.#currentMutation = undefined\n    this.#updateResult()\n    this.#notify()\n  }\n\n  mutate(\n    variables: TVariables,\n    options?: MutateOptions<TData, TError, TVariables, TContext>,\n  ): Promise<TData> {\n    this.#mutateOptions = options\n\n    this.#currentMutation?.removeObserver(this)\n\n    this.#currentMutation = this.#client\n      .getMutationCache()\n      .build(this.#client, this.options)\n\n    this.#currentMutation.addObserver(this)\n\n    return this.#currentMutation.execute(variables)\n  }\n\n  #updateResult(): void {\n    const state =\n      this.#currentMutation?.state ??\n      getDefaultState<TData, TError, TVariables, TContext>()\n\n    this.#currentResult = {\n      ...state,\n      isPending: state.status === 'pending',\n      isSuccess: state.status === 'success',\n      isError: state.status === 'error',\n      isIdle: state.status === 'idle',\n      mutate: this.mutate,\n      reset: this.reset,\n    } as MutationObserverResult<TData, TError, TVariables, TContext>\n  }\n\n  #notify(action?: Action<TData, TError, TVariables, TContext>): void {\n    notifyManager.batch(() => {\n      // First trigger the mutate callbacks\n      if (this.#mutateOptions && this.hasListeners()) {\n        const variables = this.#currentResult.variables!\n        const context = this.#currentResult.context\n\n        if (action?.type === 'success') {\n          this.#mutateOptions.onSuccess?.(action.data, variables, context!)\n          this.#mutateOptions.onSettled?.(action.data, null, variables, context)\n        } else if (action?.type === 'error') {\n          this.#mutateOptions.onError?.(action.error, variables, context)\n          this.#mutateOptions.onSettled?.(\n            undefined,\n            action.error,\n            variables,\n            context,\n          )\n        }\n      }\n\n      // Then trigger the listeners\n      this.listeners.forEach((listener) => {\n        listener(this.#currentResult)\n      })\n    })\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,sBAAgC;AAChC,2BAA8B;AAC9B,0BAA6B;AAC7B,mBAA6C;AAH7C;AAqBO,IAAM,mBAAN,cAKG,iCAER;AAAA,EASA,YACE,QACA,SACA;AACA,UAAM;AApBH;AAUL;AACA;AAEA;AACA;AAQE,uBAAK,SAAU;AACf,SAAK,WAAW,OAAO;AACvB,SAAK,YAAY;AACjB,0BAAK,8CAAL;AAAA,EACF;AAAA,EAEU,cAAoB;AAC5B,SAAK,SAAS,KAAK,OAAO,KAAK,IAAI;AACnC,SAAK,QAAQ,KAAK,MAAM,KAAK,IAAI;AAAA,EACnC;AAAA,EAEA,WACE,SACA;AAxDJ;AAyDI,UAAM,cAAc,KAAK;AAGzB,SAAK,UAAU,mBAAK,SAAQ,uBAAuB,OAAO;AAC1D,QAAI,KAAC,kCAAoB,KAAK,SAAS,WAAW,GAAG;AACnD,yBAAK,SAAQ,iBAAiB,EAAE,OAAO;AAAA,QACrC,MAAM;AAAA,QACN,UAAU,mBAAK;AAAA,QACf,UAAU;AAAA,MACZ,CAAC;AAAA,IACH;AAEA,SACE,2CAAa,gBACb,KAAK,QAAQ,mBACb,sBAAQ,YAAY,WAAW,UAAM,sBAAQ,KAAK,QAAQ,WAAW,GACrE;AACA,WAAK,MAAM;AAAA,IACb,aAAW,wBAAK,sBAAL,mBAAuB,MAAM,YAAW,WAAW;AAC5D,yBAAK,kBAAiB,WAAW,KAAK,OAAO;AAAA,IAC/C;AAAA,EACF;AAAA,EAEU,gBAAsB;AAhFlC;AAiFI,QAAI,CAAC,KAAK,aAAa,GAAG;AACxB,+BAAK,sBAAL,mBAAuB,eAAe;AAAA,IACxC;AAAA,EACF;AAAA,EAEA,iBAAiB,QAA2D;AAC1E,0BAAK,8CAAL;AAEA,0BAAK,wCAAL,WAAa;AAAA,EACf;AAAA,EAEA,mBAKE;AACA,WAAO,mBAAK;AAAA,EACd;AAAA,EAEA,QAAc;AArGhB;AAwGI,6BAAK,sBAAL,mBAAuB,eAAe;AACtC,uBAAK,kBAAmB;AACxB,0BAAK,8CAAL;AACA,0BAAK,wCAAL;AAAA,EACF;AAAA,EAEA,OACE,WACA,SACgB;AAjHpB;AAkHI,uBAAK,gBAAiB;AAEtB,6BAAK,sBAAL,mBAAuB,eAAe;AAEtC,uBAAK,kBAAmB,mBAAK,SAC1B,iBAAiB,EACjB,MAAM,mBAAK,UAAS,KAAK,OAAO;AAEnC,uBAAK,kBAAiB,YAAY,IAAI;AAEtC,WAAO,mBAAK,kBAAiB,QAAQ,SAAS;AAAA,EAChD;AA6CF;AA3IE;AACA;AAEA;AACA;AAdK;AA0GL,kBAAa,WAAS;AA/HxB;AAgII,QAAM,UACJ,wBAAK,sBAAL,mBAAuB,cACvB,iCAAqD;AAEvD,qBAAK,gBAAiB;AAAA,IACpB,GAAG;AAAA,IACH,WAAW,MAAM,WAAW;AAAA,IAC5B,WAAW,MAAM,WAAW;AAAA,IAC5B,SAAS,MAAM,WAAW;AAAA,IAC1B,QAAQ,MAAM,WAAW;AAAA,IACzB,QAAQ,KAAK;AAAA,IACb,OAAO,KAAK;AAAA,EACd;AACF;AAEA,YAAO,SAAC,QAA4D;AAClE,qCAAc,MAAM,MAAM;AAhJ9B;AAkJM,QAAI,mBAAK,mBAAkB,KAAK,aAAa,GAAG;AAC9C,YAAM,YAAY,mBAAK,gBAAe;AACtC,YAAM,UAAU,mBAAK,gBAAe;AAEpC,WAAI,iCAAQ,UAAS,WAAW;AAC9B,uCAAK,iBAAe,cAApB,4BAAgC,OAAO,MAAM,WAAW;AACxD,uCAAK,iBAAe,cAApB,4BAAgC,OAAO,MAAM,MAAM,WAAW;AAAA,MAChE,YAAW,iCAAQ,UAAS,SAAS;AACnC,uCAAK,iBAAe,YAApB,4BAA8B,OAAO,OAAO,WAAW;AACvD,uCAAK,iBAAe,cAApB;AAAA;AAAA,UACE;AAAA,UACA,OAAO;AAAA,UACP;AAAA,UACA;AAAA;AAAA,MAEJ;AAAA,IACF;AAGA,SAAK,UAAU,QAAQ,CAAC,aAAa;AACnC,eAAS,mBAAK,eAAc;AAAA,IAC9B,CAAC;AAAA,EACH,CAAC;AACH;", "names": []}