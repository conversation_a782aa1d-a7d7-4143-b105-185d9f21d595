{"version": 3, "sources": ["../../src/useSuspenseQueries.ts"], "sourcesContent": ["'use client'\nimport { skipToken } from '@tanstack/query-core'\nimport { useQueries } from './useQueries'\nimport { defaultThrowOnError } from './suspense'\nimport type { UseSuspenseQueryOptions, UseSuspenseQueryResult } from './types'\nimport type {\n  DefaultError,\n  QueryClient,\n  QueryFunction,\n  ThrowOnError,\n} from '@tanstack/query-core'\n\n// Avoid TS depth-limit error in case of large array literal\ntype MAXIMUM_DEPTH = 20\n\n// Widen the type of the symbol to enable type inference even if skipToken is not immutable.\ntype SkipTokenForUseQueries = symbol\n\ntype GetUseSuspenseQueryOptions<T> =\n  // Part 1: responsible for applying explicit type parameter to function arguments, if object { queryFnData: TQueryFnData, error: TError, data: TData }\n  T extends {\n    queryFnData: infer TQueryFnData\n    error?: infer TError\n    data: infer TData\n  }\n    ? UseSuspenseQueryOptions<TQueryFnData, TError, TData>\n    : T extends { queryFnData: infer TQueryFnData; error?: infer TError }\n      ? UseSuspenseQueryOptions<TQueryFnData, TError>\n      : T extends { data: infer TData; error?: infer TError }\n        ? UseSuspenseQueryOptions<unknown, TError, TData>\n        : // Part 2: responsible for applying explicit type parameter to function arguments, if tuple [TQueryFnData, TError, TData]\n          T extends [infer TQueryFnData, infer TError, infer TData]\n          ? UseSuspenseQueryOptions<TQueryFnData, TError, TData>\n          : T extends [infer TQueryFnData, infer TError]\n            ? UseSuspenseQueryOptions<TQueryFnData, TError>\n            : T extends [infer TQueryFnData]\n              ? UseSuspenseQueryOptions<TQueryFnData>\n              : // Part 3: responsible for inferring and enforcing type if no explicit parameter was provided\n                T extends {\n                    queryFn?:\n                      | QueryFunction<infer TQueryFnData, infer TQueryKey>\n                      | SkipTokenForUseQueries\n                    select?: (data: any) => infer TData\n                    throwOnError?: ThrowOnError<any, infer TError, any, any>\n                  }\n                ? UseSuspenseQueryOptions<\n                    TQueryFnData,\n                    TError,\n                    TData,\n                    TQueryKey\n                  >\n                : T extends {\n                      queryFn?:\n                        | QueryFunction<infer TQueryFnData, infer TQueryKey>\n                        | SkipTokenForUseQueries\n                      throwOnError?: ThrowOnError<any, infer TError, any, any>\n                    }\n                  ? UseSuspenseQueryOptions<\n                      TQueryFnData,\n                      TError,\n                      TQueryFnData,\n                      TQueryKey\n                    >\n                  : // Fallback\n                    UseSuspenseQueryOptions\n\ntype GetUseSuspenseQueryResult<T> =\n  // Part 1: responsible for mapping explicit type parameter to function result, if object\n  T extends { queryFnData: any; error?: infer TError; data: infer TData }\n    ? UseSuspenseQueryResult<TData, TError>\n    : T extends { queryFnData: infer TQueryFnData; error?: infer TError }\n      ? UseSuspenseQueryResult<TQueryFnData, TError>\n      : T extends { data: infer TData; error?: infer TError }\n        ? UseSuspenseQueryResult<TData, TError>\n        : // Part 2: responsible for mapping explicit type parameter to function result, if tuple\n          T extends [any, infer TError, infer TData]\n          ? UseSuspenseQueryResult<TData, TError>\n          : T extends [infer TQueryFnData, infer TError]\n            ? UseSuspenseQueryResult<TQueryFnData, TError>\n            : T extends [infer TQueryFnData]\n              ? UseSuspenseQueryResult<TQueryFnData>\n              : // Part 3: responsible for mapping inferred type to results, if no explicit parameter was provided\n                T extends {\n                    queryFn?:\n                      | QueryFunction<infer TQueryFnData, any>\n                      | SkipTokenForUseQueries\n                    select?: (data: any) => infer TData\n                    throwOnError?: ThrowOnError<any, infer TError, any, any>\n                  }\n                ? UseSuspenseQueryResult<\n                    unknown extends TData ? TQueryFnData : TData,\n                    unknown extends TError ? DefaultError : TError\n                  >\n                : T extends {\n                      queryFn?:\n                        | QueryFunction<infer TQueryFnData, any>\n                        | SkipTokenForUseQueries\n                      throwOnError?: ThrowOnError<any, infer TError, any, any>\n                    }\n                  ? UseSuspenseQueryResult<\n                      TQueryFnData,\n                      unknown extends TError ? DefaultError : TError\n                    >\n                  : // Fallback\n                    UseSuspenseQueryResult\n\n/**\n * SuspenseQueriesOptions reducer recursively unwraps function arguments to infer/enforce type param\n */\nexport type SuspenseQueriesOptions<\n  T extends Array<any>,\n  TResults extends Array<any> = [],\n  TDepth extends ReadonlyArray<number> = [],\n> = TDepth['length'] extends MAXIMUM_DEPTH\n  ? Array<UseSuspenseQueryOptions>\n  : T extends []\n    ? []\n    : T extends [infer Head]\n      ? [...TResults, GetUseSuspenseQueryOptions<Head>]\n      : T extends [infer Head, ...infer Tails]\n        ? SuspenseQueriesOptions<\n            [...Tails],\n            [...TResults, GetUseSuspenseQueryOptions<Head>],\n            [...TDepth, 1]\n          >\n        : Array<unknown> extends T\n          ? T\n          : // If T is *some* array but we couldn't assign unknown[] to it, then it must hold some known/homogenous type!\n            // use this to infer the param types in the case of Array.map() argument\n            T extends Array<\n                UseSuspenseQueryOptions<\n                  infer TQueryFnData,\n                  infer TError,\n                  infer TData,\n                  infer TQueryKey\n                >\n              >\n            ? Array<\n                UseSuspenseQueryOptions<TQueryFnData, TError, TData, TQueryKey>\n              >\n            : // Fallback\n              Array<UseSuspenseQueryOptions>\n\n/**\n * SuspenseQueriesResults reducer recursively maps type param to results\n */\nexport type SuspenseQueriesResults<\n  T extends Array<any>,\n  TResults extends Array<any> = [],\n  TDepth extends ReadonlyArray<number> = [],\n> = TDepth['length'] extends MAXIMUM_DEPTH\n  ? Array<UseSuspenseQueryResult>\n  : T extends []\n    ? []\n    : T extends [infer Head]\n      ? [...TResults, GetUseSuspenseQueryResult<Head>]\n      : T extends [infer Head, ...infer Tails]\n        ? SuspenseQueriesResults<\n            [...Tails],\n            [...TResults, GetUseSuspenseQueryResult<Head>],\n            [...TDepth, 1]\n          >\n        : { [K in keyof T]: GetUseSuspenseQueryResult<T[K]> }\n\nexport function useSuspenseQueries<\n  T extends Array<any>,\n  TCombinedResult = SuspenseQueriesResults<T>,\n>(\n  options: {\n    queries:\n      | readonly [...SuspenseQueriesOptions<T>]\n      | readonly [...{ [K in keyof T]: GetUseSuspenseQueryOptions<T[K]> }]\n    combine?: (result: SuspenseQueriesResults<T>) => TCombinedResult\n  },\n  queryClient?: QueryClient,\n): TCombinedResult\n\nexport function useSuspenseQueries<\n  T extends Array<any>,\n  TCombinedResult = SuspenseQueriesResults<T>,\n>(\n  options: {\n    queries: readonly [...SuspenseQueriesOptions<T>]\n    combine?: (result: SuspenseQueriesResults<T>) => TCombinedResult\n  },\n  queryClient?: QueryClient,\n): TCombinedResult\n\nexport function useSuspenseQueries(options: any, queryClient?: QueryClient) {\n  return useQueries(\n    {\n      ...options,\n      queries: options.queries.map((query: any) => {\n        if (process.env.NODE_ENV !== 'production') {\n          if (query.queryFn === skipToken) {\n            console.error('skipToken is not allowed for useSuspenseQueries')\n          }\n        }\n\n        return {\n          ...query,\n          suspense: true,\n          throwOnError: defaultThrowOnError,\n          enabled: true,\n          placeholderData: undefined,\n        }\n      }),\n    },\n    queryClient,\n  )\n}\n"], "mappings": ";;;AACA,SAAS,iBAAiB;AAC1B,SAAS,kBAAkB;AAC3B,SAAS,2BAA2B;AAyL7B,SAAS,mBAAmB,SAAc,aAA2B;AAC1E,SAAO;AAAA,IACL;AAAA,MACE,GAAG;AAAA,MACH,SAAS,QAAQ,QAAQ,IAAI,CAAC,UAAe;AAC3C,YAAI,QAAQ,IAAI,aAAa,cAAc;AACzC,cAAI,MAAM,YAAY,WAAW;AAC/B,oBAAQ,MAAM,iDAAiD;AAAA,UACjE;AAAA,QACF;AAEA,eAAO;AAAA,UACL,GAAG;AAAA,UACH,UAAU;AAAA,UACV,cAAc;AAAA,UACd,SAAS;AAAA,UACT,iBAAiB;AAAA,QACnB;AAAA,MACF,CAAC;AAAA,IACH;AAAA,IACA;AAAA,EACF;AACF;", "names": []}