import React, { useEffect, useState } from "react";
import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  SafeAreaView,
  ScrollView,
  Image,
  ActivityIndicator,
  Alert,
  Dimensions,
  Linking,
} from "react-native";
import { useRouter, useLocalSearchParams } from "expo-router";
import { Ionicons, MaterialIcons } from "@expo/vector-icons";
import { useListingsStore } from "../store/listingsStore";
import { listingsService } from "../services/listingsService";

const { width } = Dimensions.get("window");

// Header Component
const Header = ({
  title,
  showBackButton = false,
  onBack,
  onShare,
  onFavorite,
  isFavorite = false,
}: {
  title: string;
  showBackButton?: boolean;
  onBack?: () => void;
  onShare?: () => void;
  onFavorite?: () => void;
  isFavorite?: boolean;
}) => (
  <View style={styles.header}>
    {showBackButton ? (
      <TouchableOpacity onPress={onBack} style={styles.backButton}>
        <Ionicons name="arrow-back" size={24} color="#f72585" />
      </TouchableOpacity>
    ) : (
      <View style={styles.placeholder} />
    )}
    <View style={styles.headerCenter}>
      <View style={styles.logoContainer}>
        <Text style={styles.logoText}>ZM</Text>
      </View>
      <Text style={styles.headerTitle}>ZakMakelaar</Text>
    </View>
    <View style={styles.headerActions}>
      {onShare && (
        <TouchableOpacity onPress={onShare} style={styles.headerActionButton}>
          <Ionicons name="share-outline" size={24} color="#6b7280" />
        </TouchableOpacity>
      )}
      {onFavorite && (
        <TouchableOpacity
          onPress={onFavorite}
          style={styles.headerActionButton}
        >
          <Ionicons
            name={isFavorite ? "heart" : "heart-outline"}
            size={24}
            color={isFavorite ? "#f72585" : "#6b7280"}
          />
        </TouchableOpacity>
      )}
    </View>
  </View>
);

export default function ListingDetailsScreen() {
  const router = useRouter();
  const { id } = useLocalSearchParams();
  const { currentListing, fetchListing, isLoading, error } = useListingsStore();

  const [isFavorite, setIsFavorite] = useState(false);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);

  useEffect(() => {
    console.log("🔍 Listing Details - URL params:", { id, type: typeof id });
    if (id && typeof id === "string") {
      console.log("📡 Fetching listing with ID:", id);
      fetchListing(id);
    } else {
      console.log("❌ No valid listing ID found");
    }
  }, [id]);

  const formatPrice = (price: number | string | undefined) => {
    if (price === undefined || price === null || price === "") {
      return "€ --";
    }

    let numPrice: number;

    if (typeof price === "string") {
      const cleanPrice = price.replace(/[^\d.,]/g, "");
      const normalizedPrice = cleanPrice.replace(",", ".");
      numPrice = parseFloat(normalizedPrice);
    } else {
      numPrice = price;
    }

    if (isNaN(numPrice) || numPrice <= 0) {
      return "€ --";
    }

    return listingsService.formatPrice(numPrice);
  };

  const handleShare = async () => {
    if (currentListing) {
      try {
        await Linking.openURL(
          `mailto:?subject=Check out this property&body=I found this great property: ${
            currentListing.title
          } - ${formatPrice(currentListing.price)}. Check it out: ${
            currentListing.url
          }`
        );
      } catch (error) {
        Alert.alert("Share", "Unable to open email app");
      }
    }
  };

  const handleFavorite = () => {
    setIsFavorite(!isFavorite);
    Alert.alert(
      isFavorite ? "Removed from favorites" : "Added to favorites",
      isFavorite
        ? "Property removed from your favorites"
        : "Property added to your favorites"
    );
  };

  const handleApply = () => {
    router.push(`/application?listingId=${id}`);
  };

  const handleContact = () => {
    Alert.alert(
      "Contact Agent",
      "Would you like to contact the agent about this property?",
      [
        { text: "Cancel", style: "cancel" },
        { text: "Call", onPress: () => Alert.alert("Calling agent...") },
        { text: "Email", onPress: () => Alert.alert("Opening email...") },
      ]
    );
  };

  if (isLoading) {
    return (
      <SafeAreaView style={styles.container}>
        <Header
          title="Loading..."
          showBackButton={true}
          onBack={() => router.back()}
        />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#f72585" />
          <Text style={styles.loadingText}>Loading property details...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (error || !currentListing) {
    return (
      <SafeAreaView style={styles.container}>
        <Header
          title="Error"
          showBackButton={true}
          onBack={() => router.back()}
        />
        <View style={styles.errorContainer}>
          <Ionicons name="alert-circle-outline" size={64} color="#ef4444" />
          <Text style={styles.errorTitle}>Property not found</Text>
          <Text style={styles.errorText}>
            {error || "The property you're looking for could not be found."}
          </Text>
          <TouchableOpacity
            style={styles.errorButton}
            onPress={() => router.back()}
          >
            <Text style={styles.errorButtonText}>Go Back</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  const listing = currentListing;

  return (
    <SafeAreaView style={styles.container}>
      <Header
        title="Property Details"
        showBackButton={true}
        onBack={() => router.back()}
        onShare={handleShare}
        onFavorite={handleFavorite}
        isFavorite={isFavorite}
      />
      <ScrollView
        style={styles.scrollContainer}
        contentContainerStyle={styles.detailsContainer}
      >
        {/* Image Gallery */}
        <View style={styles.imageContainer}>
          <Image
            source={{
              uri:
                listing.images && listing.images.length > 0
                  ? listing.images[currentImageIndex]
                  : "https://placehold.co/600x400/e0e0e0/333333?text=No+Image",
            }}
            style={styles.listingImage}
          />
          {listing.images && listing.images.length > 1 && (
            <View style={styles.imageIndicators}>
              {listing.images.map((_, index) => (
                <TouchableOpacity
                  key={index}
                  style={[
                    styles.imageIndicator,
                    currentImageIndex === index && styles.imageIndicatorActive,
                  ]}
                  onPress={() => setCurrentImageIndex(index)}
                />
              ))}
            </View>
          )}
        </View>

        {/* Property Header */}
        <View style={styles.propertyHeader}>
          <Text style={styles.listingTitle}>{listing.title}</Text>
          <Text style={styles.listingPrice}>{formatPrice(listing.price)}</Text>
          <View style={styles.locationContainer}>
            <Ionicons name="location-outline" size={16} color="#6b7280" />
            <Text style={styles.locationText}>
              {typeof listing.location === "string"
                ? listing.location
                : listing.location?.city || "Unknown location"}
            </Text>
          </View>
        </View>

        {/* Property Details Grid */}
        <View style={styles.detailsGrid}>
          <View style={styles.detailItem}>
            <Ionicons name="bed-outline" size={24} color="#f72585" />
            <Text style={styles.detailLabel}>Bedrooms</Text>
            <Text style={styles.detailValue}>
              {listing.bedrooms || listing.rooms || "N/A"}
            </Text>
          </View>
          <View style={styles.detailItem}>
            <Ionicons name="water-outline" size={24} color="#f72585" />
            <Text style={styles.detailLabel}>Bathrooms</Text>
            <Text style={styles.detailValue}>{listing.bathrooms || "1"}</Text>
          </View>
          <View style={styles.detailItem}>
            <Ionicons name="resize-outline" size={24} color="#f72585" />
            <Text style={styles.detailLabel}>Size</Text>
            <Text style={styles.detailValue}>{listing.size || "N/A"}</Text>
          </View>
          <View style={styles.detailItem}>
            <Ionicons name="calendar-outline" size={24} color="#f72585" />
            <Text style={styles.detailLabel}>Year Built</Text>
            <Text style={styles.detailValue}>{listing.year || "N/A"}</Text>
          </View>
        </View>

        {/* Property Type & Interior */}
        <View style={styles.propertyInfo}>
          <View style={styles.infoItem}>
            <Text style={styles.infoLabel}>Property Type</Text>
            <Text style={styles.infoValue}>
              {listing.propertyType || "Apartment"}
            </Text>
          </View>
          <View style={styles.infoItem}>
            <Text style={styles.infoLabel}>Interior</Text>
            <Text style={styles.infoValue}>
              {listing.interior || "Unfurnished"}
            </Text>
          </View>
        </View>

        {/* Description */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Description</Text>
          <Text style={styles.descriptionText}>
            {listing.description ||
              "No description available for this property."}
          </Text>
        </View>

        {/* Action Buttons */}
        <View style={styles.actionButtons}>
          <TouchableOpacity
            style={styles.secondaryButton}
            onPress={handleContact}
          >
            <Ionicons name="call-outline" size={20} color="#f72585" />
            <Text style={styles.secondaryButtonText}>Contact Agent</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.primaryButton} onPress={handleApply}>
            <Ionicons name="document-text-outline" size={20} color="#ffffff" />
            <Text style={styles.primaryButtonText}>Apply Now</Text>
          </TouchableOpacity>
        </View>

        {/* Source Info */}
        <View style={styles.sourceInfo}>
          <Text style={styles.sourceText}>
            Listed on {listing.source || "Unknown"} • Added{" "}
            {new Date(listing.dateAdded).toLocaleDateString()}
          </Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f3f4f6",
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    width: "100%",
    padding: 16,
    backgroundColor: "#ffffff",
    borderBottomWidth: 1,
    borderBottomColor: "#e5e7eb",
  },
  backButton: {
    padding: 8,
  },
  placeholder: {
    width: 24,
  },
  headerCenter: {
    flexDirection: "row",
    alignItems: "center",
  },
  logoContainer: {
    width: 32,
    height: 32,
    backgroundColor: "#f72585",
    borderRadius: 8,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 8,
  },
  logoText: {
    fontSize: 16,
    fontWeight: "bold",
    color: "#ffffff",
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: "bold",
    color: "#1f2937",
  },
  headerActions: {
    flexDirection: "row",
    gap: 8,
  },
  headerActionButton: {
    padding: 8,
  },
  scrollContainer: {
    flex: 1,
  },
  detailsContainer: {
    paddingBottom: 32,
  },
  // Loading and Error States
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 32,
  },
  loadingText: {
    fontSize: 16,
    color: "#6b7280",
    marginTop: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 32,
  },
  errorTitle: {
    fontSize: 20,
    fontWeight: "600",
    color: "#ef4444",
    marginTop: 16,
    marginBottom: 8,
  },
  errorText: {
    fontSize: 16,
    color: "#6b7280",
    textAlign: "center",
    marginBottom: 24,
  },
  errorButton: {
    backgroundColor: "#f72585",
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
  },
  errorButtonText: {
    color: "#ffffff",
    fontSize: 16,
    fontWeight: "600",
  },
  // Image Gallery
  imageContainer: {
    position: "relative",
    marginBottom: 20,
  },
  listingImage: {
    width: "100%",
    height: 300,
    borderRadius: 0,
  },
  imageIndicators: {
    position: "absolute",
    bottom: 16,
    left: 0,
    right: 0,
    flexDirection: "row",
    justifyContent: "center",
    gap: 8,
  },
  imageIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: "rgba(255, 255, 255, 0.5)",
  },
  imageIndicatorActive: {
    backgroundColor: "#ffffff",
  },
  // Property Header
  propertyHeader: {
    padding: 20,
    backgroundColor: "#ffffff",
    marginBottom: 16,
  },
  listingTitle: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#1f2937",
    marginBottom: 8,
    lineHeight: 32,
  },
  listingPrice: {
    fontSize: 28,
    fontWeight: "bold",
    color: "#f72585",
    marginBottom: 12,
  },
  locationContainer: {
    flexDirection: "row",
    alignItems: "center",
    gap: 4,
  },
  locationText: {
    fontSize: 16,
    color: "#6b7280",
  },
  // Property Details Grid
  detailsGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    backgroundColor: "#ffffff",
    padding: 20,
    marginBottom: 16,
    gap: 16,
  },
  detailItem: {
    width: (width - 72) / 2,
    alignItems: "center",
    padding: 16,
    backgroundColor: "#f9fafb",
    borderRadius: 12,
  },
  detailLabel: {
    fontSize: 12,
    color: "#6b7280",
    marginTop: 8,
    marginBottom: 4,
  },
  detailValue: {
    fontSize: 16,
    fontWeight: "600",
    color: "#1f2937",
  },
  // Property Info
  propertyInfo: {
    backgroundColor: "#ffffff",
    padding: 20,
    marginBottom: 16,
    flexDirection: "row",
    gap: 20,
  },
  infoItem: {
    flex: 1,
  },
  infoLabel: {
    fontSize: 14,
    color: "#6b7280",
    marginBottom: 4,
  },
  infoValue: {
    fontSize: 16,
    fontWeight: "600",
    color: "#1f2937",
  },
  // Section
  section: {
    backgroundColor: "#ffffff",
    padding: 20,
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: "600",
    color: "#1f2937",
    marginBottom: 12,
  },
  descriptionText: {
    fontSize: 16,
    color: "#6b7280",
    lineHeight: 24,
  },
  // Action Buttons
  actionButtons: {
    flexDirection: "row",
    padding: 20,
    gap: 12,
    backgroundColor: "#ffffff",
    marginBottom: 16,
  },
  primaryButton: {
    flex: 1,
    backgroundColor: "#f72585",
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    alignItems: "center",
    flexDirection: "row",
    justifyContent: "center",
    gap: 8,
  },
  primaryButtonText: {
    color: "#ffffff",
    fontSize: 16,
    fontWeight: "600",
  },
  secondaryButton: {
    flex: 1,
    backgroundColor: "#ffffff",
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    alignItems: "center",
    borderWidth: 2,
    borderColor: "#f72585",
    flexDirection: "row",
    justifyContent: "center",
    gap: 8,
  },
  secondaryButtonText: {
    color: "#f72585",
    fontSize: 16,
    fontWeight: "600",
  },
  // Source Info
  sourceInfo: {
    padding: 20,
    alignItems: "center",
  },
  sourceText: {
    fontSize: 14,
    color: "#9ca3af",
    textAlign: "center",
  },
});
