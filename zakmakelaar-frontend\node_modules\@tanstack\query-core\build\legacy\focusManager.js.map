{"version": 3, "sources": ["../../src/focusManager.ts"], "sourcesContent": ["import { Subscribable } from './subscribable'\nimport { isServer } from './utils'\n\ntype Listener = (focused: boolean) => void\n\ntype SetupFn = (\n  setFocused: (focused?: boolean) => void,\n) => (() => void) | undefined\n\nexport class FocusManager extends Subscribable<Listener> {\n  #focused?: boolean\n  #cleanup?: () => void\n\n  #setup: SetupFn\n\n  constructor() {\n    super()\n    this.#setup = (onFocus) => {\n      // addEventListener does not exist in React Native, but window does\n      // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n      if (!isServer && window.addEventListener) {\n        const listener = () => onFocus()\n        // Listen to visibilitychange\n        window.addEventListener('visibilitychange', listener, false)\n\n        return () => {\n          // Be sure to unsubscribe if a new handler is set\n          window.removeEventListener('visibilitychange', listener)\n        }\n      }\n      return\n    }\n  }\n\n  protected onSubscribe(): void {\n    if (!this.#cleanup) {\n      this.setEventListener(this.#setup)\n    }\n  }\n\n  protected onUnsubscribe() {\n    if (!this.hasListeners()) {\n      this.#cleanup?.()\n      this.#cleanup = undefined\n    }\n  }\n\n  setEventListener(setup: SetupFn): void {\n    this.#setup = setup\n    this.#cleanup?.()\n    this.#cleanup = setup((focused) => {\n      if (typeof focused === 'boolean') {\n        this.setFocused(focused)\n      } else {\n        this.onFocus()\n      }\n    })\n  }\n\n  setFocused(focused?: boolean): void {\n    const changed = this.#focused !== focused\n    if (changed) {\n      this.#focused = focused\n      this.onFocus()\n    }\n  }\n\n  onFocus(): void {\n    const isFocused = this.isFocused()\n    this.listeners.forEach((listener) => {\n      listener(isFocused)\n    })\n  }\n\n  isFocused(): boolean {\n    if (typeof this.#focused === 'boolean') {\n      return this.#focused\n    }\n\n    // document global can be unavailable in react native\n    // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n    return globalThis.document?.visibilityState !== 'hidden'\n  }\n}\n\nexport const focusManager = new FocusManager()\n"], "mappings": ";;;;;;;AAAA,SAAS,oBAAoB;AAC7B,SAAS,gBAAgB;AADzB;AASO,IAAM,eAAN,cAA2B,aAAuB;AAAA,EAMvD,cAAc;AACZ,UAAM;AANR;AACA;AAEA;AAIE,uBAAK,QAAS,CAAC,YAAY;AAGzB,UAAI,CAAC,YAAY,OAAO,kBAAkB;AACxC,cAAM,WAAW,MAAM,QAAQ;AAE/B,eAAO,iBAAiB,oBAAoB,UAAU,KAAK;AAE3D,eAAO,MAAM;AAEX,iBAAO,oBAAoB,oBAAoB,QAAQ;AAAA,QACzD;AAAA,MACF;AACA;AAAA,IACF;AAAA,EACF;AAAA,EAEU,cAAoB;AAC5B,QAAI,CAAC,mBAAK,WAAU;AAClB,WAAK,iBAAiB,mBAAK,OAAM;AAAA,IACnC;AAAA,EACF;AAAA,EAEU,gBAAgB;AAxC5B;AAyCI,QAAI,CAAC,KAAK,aAAa,GAAG;AACxB,+BAAK,cAAL;AACA,yBAAK,UAAW;AAAA,IAClB;AAAA,EACF;AAAA,EAEA,iBAAiB,OAAsB;AA/CzC;AAgDI,uBAAK,QAAS;AACd,6BAAK,cAAL;AACA,uBAAK,UAAW,MAAM,CAAC,YAAY;AACjC,UAAI,OAAO,YAAY,WAAW;AAChC,aAAK,WAAW,OAAO;AAAA,MACzB,OAAO;AACL,aAAK,QAAQ;AAAA,MACf;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EAEA,WAAW,SAAyB;AAClC,UAAM,UAAU,mBAAK,cAAa;AAClC,QAAI,SAAS;AACX,yBAAK,UAAW;AAChB,WAAK,QAAQ;AAAA,IACf;AAAA,EACF;AAAA,EAEA,UAAgB;AACd,UAAM,YAAY,KAAK,UAAU;AACjC,SAAK,UAAU,QAAQ,CAAC,aAAa;AACnC,eAAS,SAAS;AAAA,IACpB,CAAC;AAAA,EACH;AAAA,EAEA,YAAqB;AA1EvB;AA2EI,QAAI,OAAO,mBAAK,cAAa,WAAW;AACtC,aAAO,mBAAK;AAAA,IACd;AAIA,aAAO,gBAAW,aAAX,mBAAqB,qBAAoB;AAAA,EAClD;AACF;AAzEE;AACA;AAEA;AAwEK,IAAM,eAAe,IAAI,aAAa;", "names": []}