const fs = require("fs");
const path = require("path");

// Environment variables configuration
const envConfig = `# Database Configuration
MONGO_URI=mongodb://localhost:27017/zakmakelaar
REDIS_URL=redis://localhost:6379

# Server Configuration
PORT=3000
NODE_ENV=development
CORS_ORIGIN=http://localhost:3000

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-here-change-this-in-production
JWT_EXPIRES_IN=7d

# Rate Limiting
RATE_LIMIT_MAX_REQUESTS=100
RATE_LIMIT_WINDOW_MS=900000

# Scraping Configuration
SCRAPING_INTERVAL_MINUTES=5
SCRAPING_TIMEOUT_MS=60000

# Notification Services
SENDGRID_API_KEY=your-sendgrid-api-key
SENDGRID_FROM_EMAIL=<EMAIL>
TWILIO_ACCOUNT_SID=your-twilio-account-sid
TWILIO_AUTH_TOKEN=your-twilio-auth-token
TWILIO_WHATSAPP_FROM=whatsapp:+**********

# Cache Configuration
CACHE_LISTINGS_TTL=300
CACHE_USER_TTL=1800

# OpenRouter AI Configuration
OPENROUTER_API_KEY=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
OPENROUTER_DEFAULT_MODEL=openai/gpt-4o-mini
OPENROUTER_MAX_TOKENS=4000
OPENROUTER_TEMPERATURE=0.7

# AI Model Configuration (optional - will use defaults if not set)
OPENROUTER_ANALYSIS_MODEL=openai/gpt-4o-mini
OPENROUTER_MATCHING_MODEL=anthropic/claude-3-haiku
OPENROUTER_SUMMARIZATION_MODEL=meta-llama/llama-3.1-8b-instruct
OPENROUTER_TRANSLATION_MODEL=google/gemini-flash-1.5
`;

function createEnvFile() {
  const envPath = path.join(__dirname, ".env");

  try {
    fs.writeFileSync(envPath, envConfig);
    console.log("✅ .env file created successfully!");
    console.log(
      "📝 Your OpenRouter API key has been added to the configuration."
    );
    console.log("\n🔧 Next steps:");
    console.log("1. Install dependencies: npm install");
    console.log("2. Test AI features: npm run test:ai");
    console.log("3. Start the server: npm run dev");
    console.log(
      "\n⚠️  Important: Update other API keys (SendGrid, Twilio) before production use."
    );
  } catch (error) {
    console.error("❌ Error creating .env file:", error.message);
    console.log(
      "\n📋 Please create a .env file manually with the following content:"
    );
    console.log("\n" + "=".repeat(50));
    console.log(envConfig);
    console.log("=".repeat(50));
  }
}

// Run the setup
createEnvFile();
