import React, { useState } from "react";
import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  SafeAreaView,
  ScrollView,
  TextInput,
} from "react-native";
import { Picker } from "@react-native-picker/picker";
import { useRouter } from "expo-router";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { Ionicons } from "@expo/vector-icons";

// Header Component
const Header = ({
  title,
  showBackButton = false,
  onBack,
}: {
  title: string;
  showBackButton?: boolean;
  onBack?: () => void;
}) => {
  const insets = useSafeAreaInsets();

  return (
    <View style={[styles.header, { paddingTop: Math.max(insets.top, 16) }]}>
      {showBackButton ? (
        <TouchableOpacity onPress={onBack} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color="#f72585" />
        </TouchableOpacity>
      ) : (
        <View style={styles.placeholder} />
      )}
      <View style={styles.headerCenter}>
        <View style={styles.logoContainer}>
          <Text style={styles.logoText}>ZM</Text>
        </View>
        <Text style={styles.headerTitle}>ZakMakelaar</Text>
      </View>
      <View style={styles.placeholder} />
    </View>
  );
};

export default function PreferencesSetupScreen() {
  const router = useRouter();
  const [location, setLocation] = useState("");
  const [rentRange, setRentRange] = useState("500-1500");
  const [bedrooms, setBedrooms] = useState("1");
  const [propertyType, setPropertyType] = useState("Apartment");

  const handleSavePreferences = () => {
    console.log("Saving preferences:", {
      location,
      rentRange,
      bedrooms,
      propertyType,
    });
    router.push("/dashboard");
  };

  return (
    <SafeAreaView style={styles.container}>
      <Header
        title="Set Your Preferences"
        showBackButton={true}
        onBack={() => router.back()}
      />
      <ScrollView
        style={styles.scrollContainer}
        contentContainerStyle={styles.preferencesContainer}
      >
        <Text style={styles.preferencesTitle}>
          Tell us what you&apos;re looking for!
        </Text>
        <Text style={styles.preferencesSubtitle}>
          Help our AI find your perfect rental home.
        </Text>

        <View style={styles.formContainer}>
          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>Preferred Location</Text>
            <TextInput
              style={styles.textInput}
              placeholder="e.g., Amsterdam, Utrecht"
              value={location}
              onChangeText={setLocation}
            />
          </View>

          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>Monthly Rent Range</Text>
            <View style={styles.pickerContainer}>
              <Picker
                selectedValue={rentRange}
                onValueChange={(itemValue) => setRentRange(itemValue)}
                style={styles.picker}
              >
                <Picker.Item label="€0 - €500" value="0-500" />
                <Picker.Item label="€500 - €1000" value="500-1000" />
                <Picker.Item label="€1000 - €1500" value="1000-1500" />
                <Picker.Item label="€1500 - €2000" value="1500-2000" />
                <Picker.Item label="€2000+" value="2000+" />
              </Picker>
            </View>
          </View>

          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>Number of Bedrooms</Text>
            <View style={styles.pickerContainer}>
              <Picker
                selectedValue={bedrooms}
                onValueChange={(itemValue) => setBedrooms(itemValue)}
                style={styles.picker}
              >
                <Picker.Item label="Studio" value="studio" />
                <Picker.Item label="1" value="1" />
                <Picker.Item label="2" value="2" />
                <Picker.Item label="3" value="3" />
                <Picker.Item label="4+" value="4+" />
              </Picker>
            </View>
          </View>

          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>Property Type</Text>
            <View style={styles.pickerContainer}>
              <Picker
                selectedValue={propertyType}
                onValueChange={(itemValue) => setPropertyType(itemValue)}
                style={styles.picker}
              >
                <Picker.Item label="Apartment" value="Apartment" />
                <Picker.Item label="House" value="House" />
                <Picker.Item label="Room" value="Room" />
              </Picker>
            </View>
          </View>
        </View>

        <TouchableOpacity
          style={styles.primaryButton}
          onPress={handleSavePreferences}
        >
          <Text style={styles.primaryButtonText}>
            Save Preferences & Find Homes
          </Text>
        </TouchableOpacity>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f3f4f6",
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    width: "100%",
    paddingHorizontal: 16,
    paddingBottom: 16,
    backgroundColor: "#ffffff",
    borderBottomWidth: 1,
    borderBottomColor: "#e5e7eb",
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
  },
  backButton: {
    padding: 8,
  },
  placeholder: {
    width: 24,
  },
  headerCenter: {
    flexDirection: "row",
    alignItems: "center",
  },
  logoContainer: {
    width: 32,
    height: 32,
    backgroundColor: "#f72585",
    borderRadius: 8,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 8,
  },
  logoText: {
    fontSize: 16,
    fontWeight: "bold",
    color: "#ffffff",
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: "bold",
    color: "#1f2937",
  },
  scrollContainer: {
    flex: 1,
  },
  preferencesContainer: {
    alignItems: "center",
    padding: 32,
    backgroundColor: "#ffffff",
    marginTop: 0,
    borderBottomLeftRadius: 16,
    borderBottomRightRadius: 16,
  },
  preferencesTitle: {
    fontSize: 24,
    fontWeight: "600",
    color: "#1f2937",
    marginBottom: 16,
    marginTop: 16,
    textAlign: "center",
  },
  preferencesSubtitle: {
    fontSize: 16,
    color: "#6b7280",
    textAlign: "center",
    marginBottom: 32,
    lineHeight: 22,
  },
  formContainer: {
    width: "100%",
  },
  inputContainer: {
    marginBottom: 24,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: "500",
    color: "#374151",
    marginBottom: 8,
  },
  textInput: {
    width: "100%",
    padding: 12,
    borderWidth: 1,
    borderColor: "#e5e7eb",
    borderRadius: 12,
    fontSize: 16,
    color: "#374151",
    backgroundColor: "#ffffff",
  },
  pickerContainer: {
    borderWidth: 1,
    borderColor: "#e5e7eb",
    borderRadius: 12,
    backgroundColor: "#ffffff",
  },
  picker: {
    height: 50,
    width: "100%",
  },
  primaryButton: {
    width: "100%",
    backgroundColor: "#f72585",
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 12,
    alignItems: "center",
    marginTop: 40,
  },
  primaryButtonText: {
    color: "#ffffff",
    fontSize: 18,
    fontWeight: "600",
  },
});
