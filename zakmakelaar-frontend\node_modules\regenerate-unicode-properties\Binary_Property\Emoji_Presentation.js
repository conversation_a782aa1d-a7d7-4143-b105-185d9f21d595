const set = require('regenerate')(0x23F0, 0x23F3, 0x267F, 0x2693, 0x26A1, 0x26CE, 0x26D4, 0x26EA, 0x26F5, 0x26FA, 0x26FD, 0x2705, 0x2728, 0x274C, 0x274E, 0x2757, 0x27B0, 0x27BF, 0x2B50, 0x2B55, 0x1F004, 0x1F0CF, 0x1F18E, 0x1F201, 0x1F21A, 0x1F22F, 0x1F3F4, 0x1F440, 0x1F57A, 0x1F5A4, 0x1F6CC, 0x1F7F0);
set.addRange(0x231A, 0x231B).addRange(0x23E9, 0x23EC).addRange(0x25FD, 0x25FE).addRange(0x2614, 0x2615).addRange(0x2648, 0x2653).addRange(0x26AA, 0x26AB).addRange(0x26BD, 0x26BE).addRange(0x26C4, 0x26C5).addRange(0x26F2, 0x26F3).addRange(0x270A, 0x270B).addRange(0x2753, 0x2755).addRange(0x2795, 0x2797).addRange(0x2B1B, 0x2B1C).addRange(0x1F191, 0x1F19A).addRange(0x1F1E6, 0x1F1FF).addRange(0x1F232, 0x1F236).addRange(0x1F238, 0x1F23A).addRange(0x1F250, 0x1F251).addRange(0x1F300, 0x1F320).addRange(0x1F32D, 0x1F335).addRange(0x1F337, 0x1F37C).addRange(0x1F37E, 0x1F393).addRange(0x1F3A0, 0x1F3CA).addRange(0x1F3CF, 0x1F3D3).addRange(0x1F3E0, 0x1F3F0).addRange(0x1F3F8, 0x1F43E).addRange(0x1F442, 0x1F4FC).addRange(0x1F4FF, 0x1F53D).addRange(0x1F54B, 0x1F54E).addRange(0x1F550, 0x1F567).addRange(0x1F595, 0x1F596).addRange(0x1F5FB, 0x1F64F).addRange(0x1F680, 0x1F6C5).addRange(0x1F6D0, 0x1F6D2).addRange(0x1F6D5, 0x1F6D7).addRange(0x1F6DC, 0x1F6DF).addRange(0x1F6EB, 0x1F6EC).addRange(0x1F6F4, 0x1F6FC).addRange(0x1F7E0, 0x1F7EB).addRange(0x1F90C, 0x1F93A).addRange(0x1F93C, 0x1F945).addRange(0x1F947, 0x1F9FF).addRange(0x1FA70, 0x1FA7C).addRange(0x1FA80, 0x1FA89).addRange(0x1FA8F, 0x1FAC6).addRange(0x1FACE, 0x1FADC).addRange(0x1FADF, 0x1FAE9).addRange(0x1FAF0, 0x1FAF8);
exports.characters = set;
