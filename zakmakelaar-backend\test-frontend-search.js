const axios = require('axios');

async function testFrontendSearch() {
  try {
    console.log('🔍 Testing frontend search simulation...');
    
    // Test 1: Simulate frontend search request
    console.log('\n📱 Test 1: Frontend search for "Amsterdam"');
    const response1 = await axios.get('http://localhost:3000/api/listings', {
      params: {
        search: 'Amsterdam',
        limit: 5,
        sortBy: 'dateAdded',
        sortOrder: 'desc'
      }
    });
    
    console.log(`✅ Status: ${response1.status}`);
    console.log(`📊 Results: ${response1.data.results}`);
    console.log(`🔍 Search params:`, response1.data.searchParams);
    
    if (response1.data.data?.listings?.length > 0) {
      console.log(`🏠 First result: ${response1.data.data.listings[0].title}`);
      console.log(`📍 Location: ${response1.data.data.listings[0].location}`);
    }
    
    // Test 2: Search for property type
    console.log('\n🏢 Test 2: Frontend search for "appartement"');
    const response2 = await axios.get('http://localhost:3000/api/listings', {
      params: {
        search: 'appartement',
        limit: 5,
        sortBy: 'dateAdded',
        sortOrder: 'desc'
      }
    });
    
    console.log(`✅ Status: ${response2.status}`);
    console.log(`📊 Results: ${response2.data.results}`);
    
    if (response2.data.data?.listings?.length > 0) {
      console.log(`🏠 First result: ${response2.data.data.listings[0].title}`);
      console.log(`🏢 Property type: ${response2.data.data.listings[0].propertyType}`);
    }
    
    // Test 3: Empty search (should return recent listings)
    console.log('\n📋 Test 3: No search term (recent listings)');
    const response3 = await axios.get('http://localhost:3000/api/listings', {
      params: {
        limit: 5,
        sortBy: 'dateAdded',
        sortOrder: 'desc'
      }
    });
    
    console.log(`✅ Status: ${response3.status}`);
    console.log(`📊 Results: ${response3.data.results}`);
    
    if (response3.data.data?.listings?.length > 0) {
      console.log(`🏠 First result: ${response3.data.data.listings[0].title}`);
    }
    
    console.log('\n🎉 Frontend search simulation completed!');
    
  } catch (error) {
    console.error('❌ Frontend search test failed:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
  }
}

testFrontendSearch();
