{"name": "mixin-object", "description": "Mixin the own and inherited properties of other objects onto the first object. Pass an empty object as the first arg to shallow clone.", "version": "2.0.1", "homepage": "https://github.com/jonschlinkert/mixin-object", "author": "<PERSON> (https://github.com/jonschlinkert)", "repository": "jonschlinkert/mixin-object", "bugs": {"url": "https://github.com/jonschlinkert/mixin-object/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "dependencies": {"for-in": "^0.1.3", "is-extendable": "^0.1.1"}, "devDependencies": {"mocha": "^2.2.5", "should": "^7.0.1"}, "keywords": ["assign", "copy", "extend", "key", "merge", "mixin", "object", "objects", "prop", "properties", "property", "shallow", "util", "value"], "verbiage": {"related": {"list": ["defaults-deep", "extend-shallow", "assign-deep", "mixin-deep", "merge-deep", "isobject", "is-plain-object", "for-own", "for-in"]}}}