defaults: &defaults
  working_directory: ~/user-agents
  docker:
    - image: circleci/node:fermium

whitelist: &whitelist
  paths:
    - .babelrc
    - .clabot
    - .circleci/*
    - .eslintrc
    - .git/*
    - .github/*
    - .gitignore
    - dist/*
    - CLA.md
    - CONTRIBUTING.md
    - LICENSE
    - README.md
    - media/*
    - package.json
    - src/*
    - test/*
    - webpack.config.js
    - yarn.lock

version: 2
jobs:
  checkout:
    <<: *defaults
    steps:
      - checkout
      - restore_cache:
          key: dependency-cache-{{ checksum "yarn.lock" }}
      - run:
          name: Install Dependencies
          command: yarn install
      - save_cache:
          key: dependency-cache-{{ checksum "yarn.lock" }}
          paths:
            - ./node_modules
      - persist_to_workspace:
          root: ~/user-agents
          <<: *whitelist

  build:
    <<: *defaults
    steps:
      - attach_workspace:
          at: ~/user-agents
      - restore_cache:
          key: dependency-cache-{{ checksum "yarn.lock" }}
      - run:
          name: Build
          command: |
            yarn gunzip-data
            yarn build
      - persist_to_workspace:
          root: ~/user-agents
          <<: *whitelist

  test:
    <<: *defaults
    steps:
      - attach_workspace:
          at: ~/user-agents
      - restore_cache:
          key: dependency-cache-{{ checksum "yarn.lock" }}
      - run:
          name: Test
          command: |
            yarn test

  deploy:
    <<: *defaults
    steps:
      - attach_workspace:
          at: ~/user-agents
      - run:
          name: Write NPM Token to ~/.npmrc
          command: echo "//registry.npmjs.org/:_authToken=$NPM_TOKEN" >> ~/.npmrc
      - run:
          name: Install dot-json package
          command: npm install --no-save dot-json
      - run:
          name: Write version to package.json
          command: $(yarn bin)/dot-json package.json version ${CIRCLE_TAG:1}
      - run:
          name: Publish to NPM
          command: npm publish --access=public

  update:
    <<: *defaults
    steps:
      - attach_workspace:
          at: ~/user-agents
      - restore_cache:
          key: dependency-cache-{{ checksum "yarn.lock" }}
      - run:
          name: Update the user agents data
          no_output_timeout: 30m
          command: |
            yarn update-data
      - store_artifacts:
          path: ~/user-agents/src/user-agents.json.gz
          destination: user-agents.json.gz
      - persist_to_workspace:
          root: ~/user-agents
          <<: *whitelist

  publish-new-version:
    <<: *defaults
    steps:
      - attach_workspace:
          at: ~/user-agents
      - run:
          name: Commit the newly downloaded data
          command: |
            git add src/user-agents.json.gz
            # Configure some identity details for the machine deployment account.
            git config --global user.email "<EMAIL>"
            git config --global user.name "User Agents"
            git config --global push.default "simple"
            # Disable strict host checking.
            mkdir -p ~/.ssh/
            echo -e "Host github.com\n\tStrictHostKeyChecking no\n" >> ~/.ssh/config
            # The status code will be 1 if there are no changes,
            # but we want to publish anyway to stay on a regular schedule.
            git commit -m 'Regularly scheduled user agent data update.' || true
      - run:
          name: Bump the patch version and trigger a new release
          command: |
            npm version patch
            git push --set-upstream origin v1-release
            git push --tags

workflows:
  version: 2

  build:
    jobs:
      - checkout
      - build:
          filters:
            tags:
              ignore: /v[0-9]+(\.[0-9]+)*/
          requires:
            - checkout
      - test:
          filters:
            tags:
              ignore: /v[0-9]+(\.[0-9]+)*/
          requires:
            - build

  release:
    jobs:
      - checkout:
          filters:
            tags:
              only: /v1(\.[0-9]+)*/
            branches:
              ignore: /.*/
      - build:
          filters:
            tags:
              only: /v1(\.[0-9]+)*/
            branches:
              ignore: /.*/
          requires:
            - checkout
      - test:
          filters:
            tags:
              only: /v1(\.[0-9]+)*/
            branches:
              ignore: /.*/
          requires:
            - build
      - deploy:
          filters:
            tags:
              only: /v1(\.[0-9]+)*/
            branches:
              ignore: /.*/
          requires:
            - test

  scheduled-release:
    triggers:
      - schedule:
          cron: "00 06 * * *"
          filters:
            branches:
              only:
                - v1-release

    jobs:
      - checkout
      - update:
          requires:
            - checkout
      - build:
          requires:
            - update
      - test:
          requires:
            - build
      - publish-new-version:
          requires:
            - test
