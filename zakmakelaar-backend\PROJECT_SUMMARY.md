# ZakMakelaar Project Summary

## Purpose

ZakMakelaar is an AI-powered rental property aggregation and automation platform focused on the Dutch rental market. Its goal is to make finding and securing rental housing in the Netherlands faster, easier, and more successful—especially for students, expats, and young professionals.

---

## Key Features

- **Multi-Source Scraping:** Aggregates rental listings from major Dutch property sites (Funda, Pararius, Huurwoningen, and more planned), updating every 5 minutes.
- **AI Integration:** Uses advanced AI (via OpenRouter) for:
  - Smart property-to-user matching
  - Automated contract analysis and legal guidance
  - Personalized rental application letter generation
  - Market analysis, listing summarization, and translation
- **Automated Notifications:** Real-time alerts for new listings and perfect matches.
- **User & Admin Management:** JWT-based authentication, user roles, and admin tools.
- **REST API:** Provides endpoints for listings, scraping, AI features, and user management.
- **Mobile-First Vision:** Designed for a seamless mobile experience (Expo React Native app in the product vision).

---

## Technical Architecture

- **Backend:** Node.js (Express), MongoDB (Mongoose), <PERSON>is (caching), Puppeteer & Cheerio (scraping), JWT (auth), node-schedule (cron jobs), <PERSON> (logging), Swagger (API docs).
- **AI Layer:** Modular service using OpenRouter for model selection (Claude, GPT-4o, Llama, Gemini, etc.).
- **Frontend (Planned):** Expo React Native app for iOS/Android.
- **Deployment:** Docker-ready, scalable, with monitoring and CI/CD in mind.

---

## Business Value

- **Comprehensive Coverage:** Aggregates from multiple platforms for maximum listing coverage.
- **Speed:** Delivers new listings and matches within minutes.
- **Intelligence:** AI-driven matching, contract review, and application generation.
- **User Experience:** Reduces manual effort, increases success rates, and supports international users with translation and legal guidance.

---

## Typical User Journey

1. **Sign Up:** User creates an account using google sso or email and password and sets preferences.
2. **Search & Match:** AI matches user to listings and sends real-time notifications.
3. **Apply:** User can generate and send applications manually or let the ai send the applications automatically.
4. **Contract Review:** AI analyzes rental contracts for legal risks.
5. **Success:** User secures a rental property.

---

## Scalability & Future-Proofing

- Pluggable scraper architecture for new sources
- Microservice-ready backend
- AI features can be expanded (image analysis, behavioral learning, etc.)
- Versioned API for backward compatibility

---

## Documentation & API

- **Swagger/OpenAPI:** Full API documentation at `/api-docs`
- **Environment:** Configurable via `.env` file (see `ENV_EXAMPLE.md`)
- **Testing:** Includes scripts for scraper and AI feature testing

---

**In short:** ZakMakelaar is building a next-generation, AI-powered rental assistant for the Dutch market, combining robust data aggregation, intelligent automation, and a user-friendly mobile experience to help users find and secure homes faster and smarter.
