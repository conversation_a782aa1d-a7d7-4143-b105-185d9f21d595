import React from 'react';
import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  SafeAreaView,
  ScrollView,
  Image,
} from 'react-native';
import { useRouter } from 'expo-router';

// Header Component
const Header = ({ title, showBackButton = false, onBack }: {
  title: string;
  showBackButton?: boolean;
  onBack?: () => void;
}) => (
  <View style={styles.header}>
    {showBackButton ? (
      <TouchableOpacity onPress={onBack} style={styles.backButton}>
        <Text style={styles.backButtonText}>←</Text>
      </TouchableOpacity>
    ) : (
      <View style={styles.placeholder} />
    )}
    <View style={styles.headerCenter}>
      <View style={styles.logoContainer}>
        <Text style={styles.logoText}>ZM</Text>
      </View>
      <Text style={styles.headerTitle}>ZakMakelaar</Text>
    </View>
    <View style={styles.placeholder} />
  </View>
);

export default function ListingDetailsScreen() {
  const router = useRouter();

  // Mock listing data for details
  const listing = {
    title: "Spacious Apartment in Amsterdam",
    price: "€1,800/month",
    location: "Amsterdam-Zuid",
    bedrooms: 2,
    bathrooms: 1,
    size: "85 sqm",
    description:
      "A beautiful, modern apartment located in the vibrant Amsterdam-Zuid area. Features two spacious bedrooms, a large living area, and a fully equipped kitchen. Close to public transport, parks, and amenities. Perfect for young professionals or a small family.",
    features: ["Balcony", "Washing Machine", "Furnished", "Central Heating"],
    imageUrl: "https://placehold.co/600x400/e0e0e0/333333?text=Apartment+Details",
  };

  return (
    <SafeAreaView style={styles.container}>
      <Header
        title="Listing Details"
        showBackButton={true}
        onBack={() => router.back()}
      />
      <ScrollView style={styles.scrollContainer} contentContainerStyle={styles.detailsContainer}>
        <Image
          source={{ uri: listing.imageUrl }}
          style={styles.listingImage}
        />
        <Text style={styles.listingTitle}>{listing.title}</Text>
        <Text style={styles.listingPrice}>{listing.price}</Text>

        <View style={styles.detailsGrid}>
          <View style={styles.detailItem}>
            <Text style={styles.detailIcon}>📍</Text>
            <Text style={styles.detailText}>{listing.location}</Text>
          </View>
          <View style={styles.detailItem}>
            <Text style={styles.detailIcon}>🛏️</Text>
            <Text style={styles.detailText}>{listing.bedrooms} Bedrooms</Text>
          </View>
          <View style={styles.detailItem}>
            <Text style={styles.detailIcon}>🚿</Text>
            <Text style={styles.detailText}>{listing.bathrooms} Bathroom</Text>
          </View>
          <View style={styles.detailItem}>
            <Text style={styles.detailIcon}>📐</Text>
            <Text style={styles.detailText}>{listing.size}</Text>
          </View>
        </View>

        <Text style={styles.sectionTitle}>Description</Text>
        <Text style={styles.descriptionText}>{listing.description}</Text>

        <Text style={styles.sectionTitle}>Features</Text>
        <View style={styles.featuresContainer}>
          {listing.features.map((feature, index) => (
            <Text key={index} style={styles.featureItem}>• {feature}</Text>
          ))}
        </View>

        <TouchableOpacity 
          style={styles.primaryButton}
          onPress={() => router.push('/application')}
        >
          <Text style={styles.primaryButtonText}>Apply for this Property</Text>
        </TouchableOpacity>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f3f4f6',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    width: '100%',
    padding: 16,
    backgroundColor: '#ffffff',
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
  },
  backButton: {
    padding: 8,
  },
  backButtonText: {
    fontSize: 20,
    color: '#f72585',
  },
  placeholder: {
    width: 24,
  },
  headerCenter: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  logoContainer: {
    width: 32,
    height: 32,
    backgroundColor: '#f72585',
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  logoText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#ffffff',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1f2937',
  },
  scrollContainer: {
    flex: 1,
  },
  detailsContainer: {
    padding: 24,
    backgroundColor: '#ffffff',
    margin: 16,
    marginTop: 0,
    borderBottomLeftRadius: 16,
    borderBottomRightRadius: 16,
  },
  listingImage: {
    width: '100%',
    height: 224,
    borderRadius: 12,
    marginBottom: 24,
  },
  listingTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 8,
  },
  listingPrice: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#f72585',
    marginBottom: 24,
  },
  detailsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 24,
  },
  detailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    width: '48%',
    marginBottom: 8,
  },
  detailIcon: {
    fontSize: 16,
    marginRight: 8,
    color: '#6b7280',
  },
  detailText: {
    fontSize: 14,
    color: '#374151',
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1f2937',
    marginBottom: 12,
    marginTop: 8,
  },
  descriptionText: {
    fontSize: 16,
    color: '#6b7280',
    lineHeight: 22,
    marginBottom: 24,
  },
  featuresContainer: {
    marginBottom: 32,
  },
  featureItem: {
    fontSize: 16,
    color: '#6b7280',
    marginBottom: 4,
  },
  primaryButton: {
    width: '100%',
    backgroundColor: '#f72585',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 12,
    alignItems: 'center',
  },
  primaryButtonText: {
    color: '#ffffff',
    fontSize: 18,
    fontWeight: '600',
  },
});
