{"name": "zakmakelaar-backend", "version": "1.0.0", "main": "index.js", "scripts": {"start": "node --no-deprecation src/index.js", "dev": "nodemon --no-deprecation src/index.js", "setup": "node setup-env.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:scraper": "node src/test-scraper.js", "test:huurwoningen": "node src/test-huurwoningen-scraper.js", "test:all-scrapers": "node src/test-all-scrapers.js", "test:ai": "node src/test-ai-features.js", "benchmark": "node src/benchmark-scraper.js", "monitor": "node src/start-monitoring.js", "debug:price": "node src/debug-price-extraction.js"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@sendgrid/mail": "^8.1.5", "bcrypt": "^6.0.0", "cheerio": "^1.1.0", "cors": "^2.8.5", "dotenv": "^17.0.1", "express": "^4.21.2", "express-rate-limit": "^7.5.1", "express-validator": "^7.2.1", "helmet": "^8.1.0", "ioredis": "^5.6.1", "jsonwebtoken": "^9.0.2", "mongoose": "^8.16.1", "morgan": "^1.10.0", "node-schedule": "^2.1.1", "openai": "^4.68.1", "puppeteer": "^24.11.1", "redis": "^5.5.6", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "twilio": "^5.7.1", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0"}, "devDependencies": {"jest": "^30.0.4", "nodemon": "^3.1.10"}, "jest": {"testEnvironment": "node", "testMatch": ["**/tests/**/*.test.js"], "collectCoverageFrom": ["src/**/*.js", "!src/test-*.js", "!src/debug-*.js"], "coverageDirectory": "coverage", "coverageReporters": ["text", "lcov", "html"]}}