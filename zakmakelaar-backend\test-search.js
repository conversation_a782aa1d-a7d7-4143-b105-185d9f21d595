const axios = require("axios");

async function testSearch() {
  try {
    console.log("🔍 Testing search functionality...");

    // Test 1: Search for "Amsterdam"
    console.log('\n📍 Test 1: Searching for "Amsterdam"');
    const response1 = await axios.get("http://localhost:3000/api/listings", {
      params: {
        search: "Amsterdam",
        limit: 3,
      },
    });

    console.log(`✅ Status: ${response1.status}`);
    console.log(`📊 Results: ${response1.data.results}`);
    console.log(
      `🏠 First listing: ${response1.data.data?.listings?.[0]?.title || "None"}`
    );

    // Test 2: Search for "Luxury"
    console.log('\n🏢 Test 2: Searching for "Luxury"');
    const response2 = await axios.get("http://localhost:3000/api/listings", {
      params: {
        search: "Luxury",
        limit: 3,
      },
    });

    console.log(`✅ Status: ${response2.status}`);
    console.log(`📊 Results: ${response2.data.results}`);
    console.log(
      `🏠 First listing: ${response2.data.data?.listings?.[0]?.title || "None"}`
    );

    // Test 3: Search for "Rotterdam"
    console.log('\n🏡 Test 3: Searching for "Rotterdam"');
    const response3 = await axios.get("http://localhost:3000/api/listings", {
      params: {
        search: "Rotterdam",
        limit: 3,
      },
    });

    console.log(`✅ Status: ${response3.status}`);
    console.log(`📊 Results: ${response3.data.results}`);
    console.log(
      `🏠 First listing: ${response3.data.data?.listings?.[0]?.title || "None"}`
    );

    // Test 4: Search for "Penthouse"
    console.log('\n🏙️ Test 4: Searching for "Penthouse"');
    const response4 = await axios.get("http://localhost:3000/api/listings", {
      params: {
        search: "Penthouse",
        limit: 3,
      },
    });

    console.log(`✅ Status: ${response4.status}`);
    console.log(`📊 Results: ${response4.data.results}`);
    console.log(
      `🏠 First listing: ${response4.data.data?.listings?.[0]?.title || "None"}`
    );

    // Test 5: Search for "appartement"
    console.log('\n🏠 Test 5: Searching for "appartement"');
    const response5 = await axios.get("http://localhost:3000/api/listings", {
      params: {
        search: "appartement",
        limit: 3,
      },
    });

    console.log(`✅ Status: ${response5.status}`);
    console.log(`📊 Results: ${response5.data.results}`);
    console.log(
      `🏠 First listing: ${response5.data.data?.listings?.[0]?.title || "None"}`
    );

    console.log("\n🎉 Search tests completed!");
  } catch (error) {
    console.error("❌ Search test failed:", error.message);
    if (error.response) {
      console.error("Response status:", error.response.status);
      console.error("Response data:", error.response.data);
    }
  }
}

testSearch();
