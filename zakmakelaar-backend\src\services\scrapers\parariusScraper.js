const cheerio = require("cheerio");
const Listing = require("../../models/Listing");
const { sendAlerts } = require("../alertService");
const {
  browserPool,
  validateAndNormalizeListing,
  setupPageStealth,
  getRandomDelay,
  scrapingMetrics,
  autoScroll
} = require("../scraperUtils");

// Helper function to fetch detailed listing information
const fetchListingDetails = async (browser, url) => {
  let detailPage = null;
  try {
    detailPage = await browser.newPage();
    await setupPageStealth(detailPage);

    await detailPage.goto(url, {
      waitUntil: "networkidle2",
      timeout: 30000,
    });

    // Wait for content to load
    await new Promise((r) => setTimeout(r, 2000));
    
    // Scroll to load all content
    await autoScroll(detailPage);

    const detailHtml = await detailPage.content();
    const $ = cheerio.load(detailHtml);

    // Initialize all possible fields
    let price = null;
    let size = null;
    let bedrooms = null;
    let rooms = null;
    let description = null;
    let year = null;
    let interior = null;
    let propertyType = "woning";
    let energyLabel = null;
    let availableFrom = null;
    let garden = null;
    let balcony = null;
    let parking = null;
    let heating = null;
    let isolation = null;

    // Extract price
    const priceElement = $(".listing-detail-summary__price");
    if (priceElement.length) {
      price = priceElement.text().trim();
      console.log(`Found price: ${price}`);
    }

    // Extract property details from specifications table
    $(".listing-features__list .listing-features__feature").each((i, el) => {
      const label = $(el).find(".listing-features__label").text().trim().toLowerCase();
      const value = $(el).find(".listing-features__value").text().trim();
      
      if (value) {
        // Match different property attributes based on the label
        if (label.includes("woonoppervlakte") || label.includes("oppervlakte") || label.includes("floor area")) {
          size = value;
          console.log(`Found size: ${size}`);
        } 
        else if (label.includes("slaapkamers") || label.includes("bedrooms")) {
          bedrooms = value;
          console.log(`Found bedrooms: ${bedrooms}`);
        }
        else if (label.includes("kamers") || label.includes("rooms")) {
          rooms = value;
          console.log(`Found rooms: ${rooms}`);
        }
        else if (label.includes("bouwjaar") || label.includes("construction year")) {
          year = value;
          console.log(`Found year: ${year}`);
        }
        else if (label.includes("interieur") || label.includes("interior") || label.includes("furnishing")) {
          interior = value;
          console.log(`Found interior: ${interior}`);
          
          // Normalize interior terms
          if (interior.toLowerCase().includes("furnished") || interior.toLowerCase().includes("gemeubileerd")) {
            interior = "Gemeubileerd";
          } else if (interior.toLowerCase().includes("unfurnished") || interior.toLowerCase().includes("kaal")) {
            interior = "Kaal";
          } else if (interior.toLowerCase().includes("semi-furnished") || interior.toLowerCase().includes("gestoffeerd")) {
            interior = "Gestoffeerd";
          }
        }
        else if (label.includes("beschikbaar vanaf") || label.includes("available from")) {
          availableFrom = value;
          console.log(`Found availability: ${availableFrom}`);
        }
        else if (label.includes("energielabel") || label.includes("energy label")) {
          energyLabel = value;
          console.log(`Found energy label: ${energyLabel}`);
        }
        else if (label.includes("tuin") || label.includes("garden")) {
          garden = value;
          console.log(`Found garden: ${garden}`);
        }
        else if (label.includes("balkon") || label.includes("balcony")) {
          balcony = value;
          console.log(`Found balcony: ${balcony}`);
        }
        else if (label.includes("parkeergelegenheid") || label.includes("parking")) {
          parking = value;
          console.log(`Found parking: ${parking}`);
        }
        else if (label.includes("verwarming") || label.includes("heating")) {
          heating = value;
          console.log(`Found heating: ${heating}`);
        }
        else if (label.includes("isolatie") || label.includes("insulation")) {
          isolation = value;
          console.log(`Found isolation: ${isolation}`);
        }
        else if (label.includes("soort") || label.includes("type")) {
          // Determine property type
          const typeText = value.toLowerCase();
          if (typeText.includes('appartement')) {
            propertyType = 'appartement';
          } else if (typeText.includes('huis') || typeText.includes('woning')) {
            propertyType = 'huis';
          } else if (typeText.includes('kamer')) {
            propertyType = 'kamer';
          } else if (typeText.includes('studio')) {
            propertyType = 'studio';
          }
          console.log(`Found property type: ${propertyType}`);
        }
      }
    });

    // Extract description
    const descriptionElement = $(".listing-detail-description__additional");
    if (descriptionElement.length) {
      description = descriptionElement.text().trim()
        .replace(/\s+/g, ' ')
        .substring(0, 1000); // Limit description length
      console.log(`Found description: ${description.substring(0, 50)}...`);
    }

    // If no description found in the main element, try alternative selectors
    if (!description) {
      const altDescriptionElement = $(".listing-detail-description__content");
      if (altDescriptionElement.length) {
        description = altDescriptionElement.text().trim()
          .replace(/\s+/g, ' ')
          .substring(0, 1000);
        console.log(`Found description (alt): ${description.substring(0, 50)}...`);
      }
    }

    return { 
      price, 
      size, 
      bedrooms, 
      rooms,
      description,
      year,
      interior,
      propertyType,
      energyLabel,
      availableFrom,
      garden,
      balcony,
      parking,
      heating,
      isolation
    };
  } catch (error) {
    console.log(`Error fetching details for ${url}:`, error.message);
    return { 
      price: null, 
      size: null, 
      bedrooms: null,
      rooms: null,
      description: null,
      year: null,
      interior: null
    };
  } finally {
    if (detailPage) {
      await detailPage.close();
    }
  }
};

const scrapePararius = async () => {
  scrapingMetrics.recordScrapeStart();
  let browser = null;
  let page = null;
  let listingsSaved = 0;
  let duplicatesSkipped = 0;

  try {
    browser = await browserPool.getBrowser();
    page = await browser.newPage();

    await setupPageStealth(page);
    await page.goto("https://www.pararius.nl/huurwoningen/nederland", {
      waitUntil: "networkidle2",
      timeout: 60000,
    });

    // Add random delay
    await new Promise((resolve) =>
      setTimeout(resolve, getRandomDelay(1000, 3000))
    );

    const html = await page.content();
    const $ = cheerio.load(html);

    const listings = [];
    const listingElements = $(".search-list__item--listing");
    console.log(`Found ${listingElements.length} listing elements on the page`);
    
    // Extract basic information from the search results page
    const basicListings = [];
    listingElements.each((index, element) => {
      const titleElement = $(element).find(".listing-search-item__title a");
      const title = titleElement.text().trim();

      // Get price and clean it up
      let price = $(element).find(".listing-search-item__price").text().trim();

      // Clean up price by taking only the first line (before any newlines)
      if (price) {
        const priceLines = price.split("\n");
        price = priceLines[0].trim();
        // Remove non-breaking spaces and extra whitespace
        price = price
          .replace(/\u00A0/g, " ")
          .replace(/\s+/g, " ")
          .trim();
      }

      const location = $(element)
        .find(".listing-search-item__sub-title")
        .text()
        .trim();

      const href = titleElement.attr("href");
      const url = href ? "https://www.pararius.nl" + href : null;

      // Determine basic property type from title
      let propertyType = "woning";
      if (title.toLowerCase().includes("appartement")) {
        propertyType = "appartement";
      } else if (title.toLowerCase().includes("huis")) {
        propertyType = "huis";
      } else if (title.toLowerCase().includes("kamer")) {
        propertyType = "kamer";
      } else if (title.toLowerCase().includes("studio")) {
        propertyType = "studio";
      }

      if (title && url && location) {
        basicListings.push({
          title,
          price: price || "Prijs op aanvraag",
          location,
          url,
          propertyType,
          source: "pararius.nl",
        });
      }
    });

    console.log(`Extracted ${basicListings.length} basic listings`);

    // Fetch detailed information for each listing
    for (const basicListing of basicListings) {
      try {
        console.log(`Fetching details for: ${basicListing.url}`);
        const details = await fetchListingDetails(browser, basicListing.url);
        
        // Merge basic listing with detailed information
        const enhancedListing = {
          ...basicListing,
          // Override with details if available
          price: details.price || basicListing.price,
          propertyType: details.propertyType || basicListing.propertyType,
        };
        
        // Add additional details if available
        if (details.size) enhancedListing.size = details.size;
        if (details.bedrooms) enhancedListing.bedrooms = details.bedrooms;
        if (details.rooms) enhancedListing.rooms = details.rooms;
        if (details.description) enhancedListing.description = details.description;
        if (details.year) enhancedListing.year = details.year;
        if (details.interior) enhancedListing.interior = details.interior;
        
        // Store additional details as extended properties
        const extendedDetails = {};
        if (details.energyLabel) extendedDetails.energyLabel = details.energyLabel;
        if (details.availableFrom) extendedDetails.availableFrom = details.availableFrom;
        if (details.garden) extendedDetails.garden = details.garden;
        if (details.balcony) extendedDetails.balcony = details.balcony;
        if (details.parking) extendedDetails.parking = details.parking;
        if (details.heating) extendedDetails.heating = details.heating;
        if (details.isolation) extendedDetails.isolation = details.isolation;
        
        // Add extended details if any were found
        if (Object.keys(extendedDetails).length > 0) {
          enhancedListing.extendedDetails = JSON.stringify(extendedDetails);
        }
        
        // Validate and normalize the enhanced listing
        const validatedListing = validateAndNormalizeListing(enhancedListing);
        
        if (validatedListing) {
          console.log(
            `Pararius found: ${validatedListing.title} - ${validatedListing.price} - ${validatedListing.location}`
          );
          listings.push(validatedListing);
        }
        
        // Add a small delay between requests to avoid overloading the server
        await new Promise(resolve => setTimeout(resolve, getRandomDelay(1000, 2000)));
      } catch (error) {
        console.error(`Error processing listing ${basicListing.url}:`, error.message);
      }
    }

    console.log(`Found ${listings.length} listings from Pararius.`);

    // Process listings with better error handling
    for (const listingData of listings) {
      try {
        const newListing = new Listing(listingData);
        await newListing.save();
        console.log(`Saved listing: ${newListing.title}`);
        listingsSaved++;
        sendAlerts(newListing);
      } catch (error) {
        if (error.code === 11000) {
          // Duplicate key error
          console.log(`Skipping duplicate listing: ${listingData.title}`);
          duplicatesSkipped++;
        } else {
          console.error(`Error saving listing ${listingData.title}:`, error);
        }
      }
    }

    scrapingMetrics.recordScrapeSuccess(
      listings.length,
      listingsSaved,
      duplicatesSkipped
    );
    return listings;
  } catch (error) {
    console.error("Error during Pararius scraping:", error);
    scrapingMetrics.recordScrapeFailure(error);
    return [];
  } finally {
    // Close the page to free up resources
    if (page) {
      try {
        await page.close();
      } catch (closeError) {
        console.error("Error closing Pararius page:", closeError);
      }
    }
    console.log(
      `Pararius scraping completed. Saved: ${listingsSaved}, Duplicates: ${duplicatesSkipped}`
    );
  }
};

module.exports = {
  scrapePararius
};
