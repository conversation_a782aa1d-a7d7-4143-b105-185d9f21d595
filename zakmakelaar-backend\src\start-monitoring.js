const PerformanceMonitor = require('./monitoring/performance-monitor');
const { getScrapingMetrics } = require('./services/scraper');

// Initialize performance monitor
const monitor = new PerformanceMonitor();

async function startMonitoring() {
  console.log('🚀 Starting Funda Scraper Performance Monitoring');
  console.log('='.repeat(50));
  
  // Display current metrics
  const metrics = getScrapingMetrics();
  console.log('📊 Current Metrics:');
  console.log(`   Total Scrapes: ${metrics.totalScrapes}`);
  console.log(`   Success Rate: ${metrics.successRate}`);
  console.log(`   Last Scrape: ${metrics.lastScrapeTime || 'Never'}`);
  console.log('');
  
  // Start monitoring with 5-minute intervals
  await monitor.startMonitoring(5);
  
  // Handle graceful shutdown
  process.on('SIGINT', () => {
    console.log('\n🛑 Shutting down performance monitoring...');
    monitor.stopMonitoring();
    process.exit(0);
  });
  
  process.on('SIGTERM', () => {
    console.log('\n🛑 Shutting down performance monitoring...');
    monitor.stopMonitoring();
    process.exit(0);
  });
  
  console.log('✅ Performance monitoring started successfully');
  console.log('   - Monitoring interval: 5 minutes');
  console.log('   - Logs: logs/performance.log');
  console.log('   - Alerts: logs/alerts.log');
  console.log('   - Press Ctrl+C to stop');
  console.log('');
}

// Start monitoring
startMonitoring().catch(console.error);
