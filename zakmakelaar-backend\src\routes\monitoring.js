const express = require("express");
const router = express.Router();
const { getScrapingMetrics } = require("../services/scraper");
const { catchAsync } = require("../middleware/errorHandler");

/**
 * @swagger
 * /api/monitoring/health:
 *   get:
 *     summary: Get scraper health status
 *     tags: [Monitoring]
 *     responses:
 *       200:
 *         description: Health status retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   enum: [healthy, degraded, unhealthy]
 *                 uptime:
 *                   type: number
 *                 timestamp:
 *                   type: string
 *                 metrics:
 *                   type: object
 */
router.get(
  "/health",
  catchAsync(async (req, res) => {
    const metrics = getScrapingMetrics();
    const uptime = process.uptime();

    // Determine health status based on metrics
    let status = "healthy";
    const successRate = parseFloat(metrics.successRate);

    if (successRate < 50) {
      status = "unhealthy";
    } else if (successRate < 80) {
      status = "degraded";
    }

    // Check if last scrape was too long ago (more than 1 hour)
    if (metrics.lastScrapeTime) {
      const timeSinceLastScrape =
        Date.now() - new Date(metrics.lastScrapeTime).getTime();
      if (timeSinceLastScrape > 3600000) {
        // 1 hour
        status = "degraded";
      }
    }

    res.json({
      status,
      uptime: Math.floor(uptime),
      timestamp: new Date().toISOString(),
      metrics: {
        totalScrapes: metrics.totalScrapes,
        successRate: metrics.successRate,
        lastScrapeTime: metrics.lastScrapeTime,
        errorsByType: metrics.errorsByType,
      },
    });
  })
);

/**
 * @swagger
 * /api/monitoring/metrics:
 *   get:
 *     summary: Get detailed scraping metrics
 *     tags: [Monitoring]
 *     responses:
 *       200:
 *         description: Metrics retrieved successfully
 */
router.get(
  "/metrics",
  catchAsync(async (req, res) => {
    const metrics = getScrapingMetrics();

    res.json({
      status: "success",
      data: {
        ...metrics,
        performance: {
          averageScrapingTime: metrics.averageScrapingTimeFormatted || "0s",
          listingsPerScrape:
            metrics.totalScrapes > 0
              ? (metrics.totalListingsFound / metrics.totalScrapes).toFixed(2)
              : "0",
          duplicateRate:
            metrics.totalListingsFound > 0 && metrics.duplicatesSkipped > 0
              ? (
                  (metrics.duplicatesSkipped /
                    (metrics.totalListingsFound + metrics.duplicatesSkipped)) *
                  100
                ).toFixed(2) + "%"
              : "0%",
        },
      },
    });
  })
);

/**
 * @swagger
 * /api/monitoring/dashboard:
 *   get:
 *     summary: Get dashboard data for monitoring UI
 *     tags: [Monitoring]
 *     responses:
 *       200:
 *         description: Dashboard data retrieved successfully
 */
router.get(
  "/dashboard",
  catchAsync(async (req, res) => {
    const metrics = getScrapingMetrics();
    const uptime = process.uptime();

    // Calculate additional dashboard metrics
    const successRate = parseFloat(metrics.successRate);
    const avgListingsPerScrape =
      metrics.totalScrapes > 0
        ? (metrics.totalListingsFound / metrics.totalScrapes).toFixed(1)
        : "0";

    const duplicateRate =
      metrics.totalListingsFound > 0
        ? (
            (metrics.duplicatesSkipped /
              (metrics.totalListingsFound + metrics.duplicatesSkipped)) *
            100
          ).toFixed(1)
        : "0";

    // Determine status indicators
    const indicators = {
      overall:
        successRate >= 80 ? "good" : successRate >= 50 ? "warning" : "critical",
      performance:
        parseFloat(metrics.averageScrapingTime) < 120000 ? "good" : "warning", // 2 minutes
      duplicates: parseFloat(duplicateRate) < 50 ? "good" : "warning",
    };

    // Recent activity summary
    const recentActivity = {
      lastScrapeTime: metrics.lastScrapeTime,
      timeSinceLastScrape: metrics.lastScrapeTime
        ? Math.floor(
            (Date.now() - new Date(metrics.lastScrapeTime).getTime()) /
              1000 /
              60
          ) // minutes
        : null,
      recentErrors: Object.entries(metrics.errorsByType)
        .sort(([, a], [, b]) => b - a)
        .slice(0, 5)
        .map(([type, count]) => ({ type, count })),
    };

    res.json({
      status: "success",
      data: {
        overview: {
          totalScrapes: metrics.totalScrapes,
          successfulScrapes: metrics.successfulScrapes,
          failedScrapes: metrics.failedScrapes,
          successRate: successRate + "%",
          uptime: Math.floor(uptime),
        },
        performance: {
          averageScrapingTime: metrics.averageScrapingTimeFormatted || "0s",
          avgListingsPerScrape,
          totalListingsFound: metrics.totalListingsFound || 0,
          totalListingsSaved: metrics.totalListingsSaved || 0,
          duplicatesSkipped: metrics.duplicatesSkipped || 0,
          duplicateRate: duplicateRate + "%",
        },
        indicators,
        recentActivity,
        charts: {
          errorDistribution: Object.entries(metrics.errorsByType).map(
            ([type, count]) => ({
              label: type,
              value: count,
            })
          ),
          successVsFailure: [
            { label: "Successful", value: metrics.successfulScrapes },
            { label: "Failed", value: metrics.failedScrapes },
          ],
        },
      },
    });
  })
);

/**
 * @swagger
 * /api/monitoring/alerts:
 *   get:
 *     summary: Get current system alerts
 *     tags: [Monitoring]
 *     responses:
 *       200:
 *         description: Alerts retrieved successfully
 */
router.get(
  "/alerts",
  catchAsync(async (req, res) => {
    const metrics = getScrapingMetrics();
    const alerts = [];

    const successRate = parseFloat(metrics.successRate);

    // Check for various alert conditions
    if (successRate < 50) {
      alerts.push({
        level: "critical",
        message: `Success rate is critically low: ${successRate}%`,
        timestamp: new Date().toISOString(),
        type: "performance",
      });
    } else if (successRate < 80) {
      alerts.push({
        level: "warning",
        message: `Success rate is below optimal: ${successRate}%`,
        timestamp: new Date().toISOString(),
        type: "performance",
      });
    }

    // Check if scraper hasn't run recently
    if (metrics.lastScrapeTime) {
      const timeSinceLastScrape =
        Date.now() - new Date(metrics.lastScrapeTime).getTime();
      if (timeSinceLastScrape > 3600000) {
        // 1 hour
        alerts.push({
          level: "warning",
          message: `No scraping activity for ${Math.floor(
            timeSinceLastScrape / 1000 / 60
          )} minutes`,
          timestamp: new Date().toISOString(),
          type: "activity",
        });
      }
    }

    // Check for high error rates
    const totalErrors = Object.values(metrics.errorsByType).reduce(
      (sum, count) => sum + count,
      0
    );
    if (totalErrors > metrics.totalScrapes * 0.5) {
      alerts.push({
        level: "warning",
        message: `High error rate detected: ${totalErrors} errors in ${metrics.totalScrapes} scrapes`,
        timestamp: new Date().toISOString(),
        type: "errors",
      });
    }

    res.json({
      status: "success",
      data: {
        alerts,
        alertCount: alerts.length,
        criticalCount: alerts.filter((a) => a.level === "critical").length,
        warningCount: alerts.filter((a) => a.level === "warning").length,
      },
    });
  })
);

module.exports = router;
