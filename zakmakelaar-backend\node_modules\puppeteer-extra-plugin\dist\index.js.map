{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": ";;;;;;AAAA,kDAAuC;AAGvC,eAAe;AACf,MAAM,KAAK,GAAG,OAAO,CAAC,YAAY,CAAC,CAAA;AAmBnC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAwCG;AACH,MAAsB,oBAAoB;IAQxC,YAAY,IAAoB;QAC9B,IAAI,CAAC,UAAU,GAAG,IAAA,eAAK,EAAC,+BAA+B,IAAI,CAAC,IAAI,EAAE,CAAC,CAAA;QACnE,IAAI,CAAC,kBAAkB,GAAG,EAAE,CAAA;QAE5B,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,IAAI,EAAE,CAAC,CAAA;QAE7C,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,CAAA;IACjC,CAAC;IAED;;;;;;;;;OASG;IACH,IAAI,IAAI;QACN,MAAM,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAA;IAChD,CAAC;IAED;;;;;;;;;;;;;;;;;;;;OAoBG;IACH,IAAI,QAAQ;QACV,OAAO,EAAE,CAAA;IACX,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;OAuBG;IACH,IAAI,YAAY;QACd,OAAO,IAAI,GAAG,CAAC,EAAE,CAAC,CAAA;IACpB,CAAC;IAED;;;;;;;;;;OAUG;IACH,IAAI,YAAY;QACd,OAAO,IAAI,GAAG,CAAC,EAAE,CAAC,CAAA;IACpB,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA+BG;IACH,IAAI,IAAI;QACN,OAAO,EAAE,CAAA;IACX,CAAC;IAED;;;;;;;;;;;;;;OAcG;IACH,IAAI,IAAI;QACN,OAAO,IAAI,CAAC,KAAK,CAAA;IACnB,CAAC;IAED;;;;;;;;;;;;;;;OAeG;IACH,IAAI,KAAK;QACP,OAAO,IAAA,eAAK,EAAC,0BAA0B,IAAI,CAAC,IAAI,EAAE,CAAC,CAAA;IACrD,CAAC;IAED;;;;;;;;;;;;;;;;OAgBG;IACH,KAAK,CAAC,YAAY,CAAC,OAAY;QAC7B,OAAO;IACT,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;OA0BG;IACH,KAAK,CAAC,WAAW,CACf,OAA0B,EAC1B,OAAO,EAAE,OAAO,EAAE,EAA6B,EAAE;QAEjD,OAAO;IACT,CAAC;IAED;;;;;;;;;;OAUG;IACH,KAAK,CAAC,aAAa,CAAC,OAAiC;QACnD,OAAO;IACT,CAAC;IAED;;;;;;;;;OASG;IACH,KAAK,CAAC,YAAY,CAAC,OAA0B,EAAE,IAAI,GAAG,EAAE;QACtD,OAAO;IACT,CAAC;IAED;;;;;;;;;;;OAWG;IACI,KAAK,CAAC,SAAS,CAAC,OAA0B,EAAE,IAAS;QAC1D,OAAO;IACT,CAAC;IAED;;;;;;;;OAQG;IACH,KAAK,CAAC,eAAe,CAAC,MAAwB;QAC5C,OAAO;IACT,CAAC;IAED;;;;;;;;;;;;;;;;;;OAkBG;IACH,KAAK,CAAC,aAAa,CAAC,IAAoB;QACtC,OAAO;IACT,CAAC;IAED;;;;;;;;OAQG;IACH,KAAK,CAAC,eAAe,CAAC,MAAwB;QAC5C,OAAO;IACT,CAAC;IAED;;;;;;;;OAQG;IACH,KAAK,CAAC,iBAAiB,CAAC,MAAwB;QAC9C,OAAO;IACT,CAAC;IAED;;;;;;OAMG;IACH,KAAK,CAAC,cAAc;QAClB,OAAO;IACT,CAAC;IAED;;;;;;;;;;;OAWG;IACH,KAAK,CAAC,OAAO;QACX,OAAO;IACT,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,kBAAkB;QACtB,OAAO;IACT,CAAC;IAED;;;;;;;;;;OAUG;IACH,kBAAkB,CAAC,IAAa;QAC9B,OAAO,EAAE,CAAA;IACX,CAAC;IAED;;;;;;;;OAQG;IACH,uBAAuB,CAAC,OAAY;QAClC,MAAM,WAAW,GAAG,IAAI,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAA;QAC5D,MAAM,OAAO,GAAG,IAAI,GAAG,CACrB,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CACxE,CAAA;QACD,OAAO,OAAO,CAAA;IAChB,CAAC;IAED;;;;;;;;;;;;;;OAcG;IACH,KAAK,CAAC,kBAAkB,CAAC,OAA0B,EAAE,OAAY,EAAE;QACjE,IACE,IAAI,CAAC,oBAAoB,CAAC,iBAAiB,CAAC;YAC5C,IAAI,CAAC,oBAAoB,CAAC,eAAe,CAAC,EAC1C;YACA,OAAO,CAAC,EAAE,CAAC,eAAe,EAAE,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAA;SAC9D;QACD,IAAI,IAAI,CAAC,oBAAoB,CAAC,iBAAiB,CAAC,IAAI,IAAI,CAAC,eAAe,EAAE;YACxE,OAAO,CAAC,EAAE,CAAC,eAAe,EAAE,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAA;SAC7D;QACD,IACE,IAAI,CAAC,oBAAoB,CAAC,mBAAmB,CAAC;YAC9C,IAAI,CAAC,iBAAiB,EACtB;YACA,OAAO,CAAC,EAAE,CAAC,iBAAiB,EAAE,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAA;SACjE;QACD,IAAI,IAAI,CAAC,oBAAoB,CAAC,gBAAgB,CAAC,IAAI,IAAI,CAAC,cAAc,EAAE;YACtE,OAAO,CAAC,EAAE,CAAC,cAAc,EAAE,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAA;SAC3D;QACD,IAAI,IAAI,CAAC,OAAO,KAAK,QAAQ,IAAI,IAAI,CAAC,oBAAoB,CAAC,SAAS,CAAC,EAAE;YACrE,gEAAgE;YAChE,kDAAkD;YAClD,IAAI,IAAI,CAAC,OAAO,EAAE;gBAChB,OAAO,CAAC,EAAE,CAAC,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAA;gBAC3C,OAAO,CAAC,EAAE,CAAC,cAAc,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAA;gBAEnD,IAAI,IAAI,CAAC,OAAO,CAAC,YAAY,KAAK,KAAK,EAAE;oBACvC,OAAO,CAAC,EAAE,CAAC,QAAQ,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAA;iBAC9C;gBACD,IAAI,IAAI,CAAC,OAAO,CAAC,aAAa,KAAK,KAAK,EAAE;oBACxC,OAAO,CAAC,EAAE,CAAC,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAA;iBAC/C;gBACD,IAAI,IAAI,CAAC,OAAO,CAAC,YAAY,KAAK,KAAK,EAAE;oBACvC,OAAO,CAAC,EAAE,CAAC,QAAQ,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAA;iBAC9C;aACF;SACF;QACD,IAAI,IAAI,CAAC,OAAO,KAAK,QAAQ,IAAI,IAAI,CAAC,WAAW,EAAE;YACjD,MAAM,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,IAAI,CAAC,CAAA;SACtC;QACD,IAAI,IAAI,CAAC,OAAO,KAAK,SAAS,IAAI,IAAI,CAAC,YAAY,EAAE;YACnD,MAAM,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,IAAI,CAAC,CAAA;SACvC;QACD,IAAI,IAAI,CAAC,SAAS;YAAE,MAAM,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,IAAI,CAAC,CAAA;IACzD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,gBAAgB,CAAC,MAAwB;QAC7C,IAAI,IAAI,CAAC,eAAe;YAAE,MAAM,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAA;QAC5D,qDAAqD;QACrD,IAAI,MAAM,CAAC,IAAI,EAAE,KAAK,MAAM,EAAE;YAC5B,IAAI;gBACF,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,IAAI,EAAE,CAAA;gBAChC,IAAI,CAAC,IAAI,EAAE;oBACT,OAAM;iBACP;gBACD,MAAM,SAAS,GAAG,UAAU,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAA;gBACxD,IAAI,IAAI,CAAC,aAAa,IAAI,SAAS,EAAE;oBACnC,MAAM,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAA;iBAC/B;aACF;YAAC,OAAO,GAAG,EAAE;gBACZ,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;aACnB;SACF;IACH,CAAC;IAED;;OAEG;IACH,SAAS,CAAC,SAAc;QACtB,IAAI,CAAC,0BAA0B,CAAC,SAAS,CAAC,CAAA;QAC1C,IAAI,IAAI,CAAC,kBAAkB;YAAE,IAAI,CAAC,kBAAkB,EAAE,CAAA;IACxD,CAAC;IAED;;OAEG;IACH,0BAA0B,CAAC,SAAc;QACvC,IAAI,CAAC,kBAAkB,GAAG,MAAM,CAAC,mBAAmB,CAAC,SAAS,CAAC,CAAA;IACjE,CAAC;IAED;;OAEG;IACH,oBAAoB,CAAC,IAAY;QAC/B,OAAO,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAA;IACjD,CAAC;IAED;;OAEG;IACH,IAAI,uBAAuB;QACzB,OAAO,IAAI,CAAA;IACb,CAAC;CACF;AAngBD,oDAmgBC"}