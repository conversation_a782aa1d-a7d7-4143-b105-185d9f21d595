{"name": "expo-secure-store", "version": "14.2.3", "description": "Provides a way to encrypt and securely store key-value pairs locally on the device.", "main": "build/SecureStore.js", "types": "build/SecureStore.d.ts", "sideEffects": false, "scripts": {"build": "expo-module build", "clean": "expo-module clean", "lint": "expo-module lint", "test": "expo-module test", "prepare": "expo-module prepare", "prepublishOnly": "expo-module prepublishOnly", "expo-module": "expo-module"}, "keywords": ["react-native", "expo", "expo-secure-store", "secure", "store"], "repository": {"type": "git", "url": "https://github.com/expo/expo.git", "directory": "packages/expo-secure-store"}, "bugs": {"url": "https://github.com/expo/expo/issues"}, "author": "650 Industries, Inc.", "license": "MIT", "homepage": "https://docs.expo.dev/versions/latest/sdk/securestore/", "jest": {"preset": "expo-module-scripts"}, "dependencies": {}, "devDependencies": {"expo-module-scripts": "^4.1.6"}, "peerDependencies": {"expo": "*"}, "gitHead": "84355076bc31a356aa3d23257f387f221885f53d"}