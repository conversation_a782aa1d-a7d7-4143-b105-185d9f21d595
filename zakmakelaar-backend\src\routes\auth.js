const express = require("express");
const userController = require("../controllers/userController");
const auth = require("../middleware/auth");
const { authLimiter } = require("../middleware/rateLimiter");
const {
  validateUserRegistration,
  validateUserLogin,
  validateUserPreferences,
  validateUserId,
} = require("../middleware/validation");

const router = express.Router();

// Apply auth rate limiting to all routes in this router
router.use(authLimiter);

// Get current authenticated user
router.get("/me", auth, userController.getMe);

/**
 * @swagger
 * components:
 *   schemas:
 *     User:
 *       type: object
 *       properties:
 *         _id:
 *           type: string
 *           description: Unique user identifier
 *         email:
 *           type: string
 *           format: email
 *           description: User's email address
 *         preferences:
 *           type: object
 *           properties:
 *             location:
 *               type: string
 *               description: Preferred location for listings
 *             budget:
 *               type: number
 *               description: Budget for property search
 *             rooms:
 *               type: number
 *               description: Preferred number of rooms
 *         profile:
 *           type: object
 *           properties:
 *             name:
 *               type: string
 *               description: User's full name
 *             income:
 *               type: number
 *               description: User's income
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: Account creation date
 *
 *     AuthResponse:
 *       type: object
 *       properties:
 *         status:
 *           type: string
 *           example: success
 *         token:
 *           type: string
 *           description: JWT authentication token
 *         data:
 *           type: object
 *           properties:
 *             user:
 *               $ref: '#/components/schemas/User'
 *
 *   securitySchemes:
 *     bearerAuth:
 *       type: http
 *       scheme: bearer
 *       bearerFormat: JWT
 *
 * tags:
 *   name: Authentication
 *   description: User authentication and management
 */

/**
 * @swagger
 * /api/auth/register:
 *   post:
 *     summary: Register a new user
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *               - password
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *                 description: User's email address (must be unique)
 *                 example: <EMAIL>
 *               password:
 *                 type: string
 *                 format: password
 *                 minLength: 8
 *                 description: Password (min 8 chars, must contain uppercase, lowercase, and number)
 *                 example: MyPassword123
 *     responses:
 *       201:
 *         description: User registered successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/AuthResponse'
 *       400:
 *         description: Validation failed or user already exists
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: error
 *                 message:
 *                   type: string
 *                   example: User with this email already exists
 *       429:
 *         description: Too many registration attempts
 *       500:
 *         description: Internal server error
 */
router.post("/register", validateUserRegistration, userController.register);

/**
 * @swagger
 * /api/auth/login:
 *   post:
 *     summary: Log in a user
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *               - password
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *               password:
 *                 type: string
 *                 format: password
 *     responses:
 *       200:
 *         description: User logged in successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/AuthResponse'
 *       400:
 *         description: Validation failed
 *       401:
 *         description: Invalid credentials
 *       429:
 *         description: Too many login attempts
 *       500:
 *         description: Internal server error
 */
router.post("/login", validateUserLogin, userController.login);

// Logout user
router.post("/logout", (req, res) => {
  res.status(200).json({
    status: "success",
    message: "Logged out successfully",
  });
});

/**
 * @swagger
 * /api/auth/users/{userId}/preferences:
 *   put:
 *     summary: Update user preferences
 *     tags: [Authentication]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: userId
 *         schema:
 *           type: string
 *         required: true
 *         description: The user ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               preferences:
 *                 type: object
 *                 properties:
 *                   location:
 *                     type: string
 *                     description: Preferred location for property search
 *                     example: Amsterdam
 *                   budget:
 *                     type: number
 *                     description: Budget for property search
 *                     example: 2000
 *                   rooms:
 *                     type: number
 *                     description: Preferred number of rooms
 *                     example: 3
 *               profile:
 *                 type: object
 *                 properties:
 *                   name:
 *                     type: string
 *                     description: User's full name
 *                     example: John Doe
 *                   income:
 *                     type: number
 *                     description: User's income
 *                     example: 50000
 *     responses:
 *       200:
 *         description: User preferences updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 data:
 *                   type: object
 *                   properties:
 *                     user:
 *                       $ref: '#/components/schemas/User'
 *       400:
 *         description: Validation failed
 *       401:
 *         description: Unauthorized - Invalid or missing token
 *       404:
 *         description: User not found
 *       500:
 *         description: Internal server error
 */
router.put(
  "/users/:userId/preferences",
  validateUserId,
  auth,
  validateUserPreferences,
  userController.updatePreferences
);

module.exports = router;
