// API Configuration
export const API_CONFIG = {
  // Development API URL - change this to your backend URL
  DEV_BASE_URL: "http://192.168.1.45:3000/api",

  // Production API URL - update this when you deploy
  PROD_BASE_URL: "https://your-production-api.com/api",

  // Timeout settings
  TIMEOUT: 10000, // 10 seconds

  // Retry settings
  MAX_RETRIES: 3,
  RETRY_DELAY: 1000, // 1 second
};

// Get the appropriate base URL based on environment
export const getApiBaseUrl = (): string => {
  return __DEV__ ? API_CONFIG.DEV_BASE_URL : API_CONFIG.PROD_BASE_URL;
};

// Common headers
export const DEFAULT_HEADERS = {
  "Content-Type": "application/json",
  Accept: "application/json",
};

// API endpoints
export const API_ENDPOINTS = {
  // Auth endpoints
  AUTH: {
    LOGIN: "/auth/login",
    REGISTER: "/auth/register",
    LOGOUT: "/auth/logout",
    ME: "/auth/me",
    REFRESH: "/auth/refresh",
    PREFERENCES: "/auth/preferences",
    PROFILE: "/auth/profile",
    CHANGE_PASSWORD: "/auth/change-password",
    FORGOT_PASSWORD: "/auth/forgot-password",
    RESET_PASSWORD: "/auth/reset-password",
  },

  // Listings endpoints
  LISTINGS: {
    BASE: "/listings",
    BY_ID: (id: string) => `/listings/${id}`,
    SAVED: "/listings/saved",
    SAVE: (id: string) => `/listings/${id}/save`,
    SIMILAR: (id: string) => `/listings/${id}/similar`,
    REPORT: (id: string) => `/listings/${id}/report`,
    STATS: "/listings/stats",
    CITIES: "/listings/cities",
    PROPERTY_TYPES: "/listings/property-types",
  },

  // AI endpoints
  AI: {
    MATCH: "/ai/match",
    CONTRACT_ANALYSIS: "/ai/contract-analysis",
    CONTRACT_UPLOAD: "/ai/contract-analysis/upload",
    APPLICATION_GEN: "/ai/application-gen",
    MARKET_ANALYSIS: "/ai/market-analysis",
    SUMMARIZE: "/ai/summarize",
    TRANSLATE: "/ai/translate",
    RECOMMENDATIONS: "/ai/recommendations",
    ANALYZE_DESCRIPTION: "/ai/analyze-description",
    SEARCH_SUGGESTIONS: "/ai/search-suggestions",
    COMPARE_PROPERTIES: "/ai/compare-properties",
    CHAT: "/ai/chat",
  },

  // Scraper endpoints
  SCRAPER: {
    SCRAPE: "/scraper/scrape",
    METRICS: "/scraper/metrics",
  },

  // Agent endpoints
  AGENT: {
    STATUS: "/agent/status",
    START: "/agent/start",
    STOP: "/agent/stop",
  },

  // Monitoring endpoints
  MONITORING: {
    DASHBOARD: "/monitoring/dashboard",
    ALERTS: "/monitoring/alerts",
  },

  // Health check
  HEALTH: "/health",
};

// Error codes
export const API_ERROR_CODES = {
  NETWORK_ERROR: "NETWORK_ERROR",
  TIMEOUT_ERROR: "TIMEOUT_ERROR",
  UNAUTHORIZED: "UNAUTHORIZED",
  FORBIDDEN: "FORBIDDEN",
  NOT_FOUND: "NOT_FOUND",
  VALIDATION_ERROR: "VALIDATION_ERROR",
  SERVER_ERROR: "SERVER_ERROR",
  UNKNOWN_ERROR: "UNKNOWN_ERROR",
};

// HTTP status codes
export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  VALIDATION_ERROR: 422,
  TOO_MANY_REQUESTS: 429,
  INTERNAL_SERVER_ERROR: 500,
  BAD_GATEWAY: 502,
  SERVICE_UNAVAILABLE: 503,
  GATEWAY_TIMEOUT: 504,
};

export default {
  API_CONFIG,
  getApiBaseUrl,
  DEFAULT_HEADERS,
  API_ENDPOINTS,
  API_ERROR_CODES,
  HTTP_STATUS,
};
