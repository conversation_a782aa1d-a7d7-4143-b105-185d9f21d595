{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-07-15 00:00:00'
}
{
  component: 'database',
  service: 'zakmakelaar-api',
  operation: 'search',
  collection: 'listings',
  duration: '4ms',
  level: 'info',
  message: 'Database Operation',
  timestamp: '2025-07-15 00:00:08'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/api/listings?limit=10&sortBy=dateAdded&sortOrder=desc',
  ip: '::ffff:************',
  userAgent: 'okhttp/4.12.0',
  statusCode: 200,
  responseTime: '15ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-15 00:00:08'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/api/search/stats',
  ip: '::ffff:************',
  userAgent: 'okhttp/4.12.0',
  statusCode: 304,
  responseTime: '2ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-15 00:00:08'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/api/listings/68758ad8cadc089245b0c503',
  ip: '::ffff:************',
  userAgent: 'okhttp/4.12.0',
  statusCode: 200,
  responseTime: '6ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-15 00:00:13'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/api/listings/68758ad8cadc089245b0c500',
  ip: '::ffff:************',
  userAgent: 'okhttp/4.12.0',
  statusCode: 200,
  responseTime: '5ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-15 00:00:29'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  duration: '329323ms',
  listingsProcessed: 39,
  level: 'info',
  message: 'Scheduled scraping completed',
  timestamp: '2025-07-15 00:00:29'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-15 00:00:30',
  level: 'info',
  category: 'ai_operation',
  operation: 'listing_matching',
  status: 'error',
  details: '402 This request requires more credits, or fewer max_tokens. You requested up to 4000 tokens, but can only afford 2711. To increase, visit https://openrouter.ai/settings/credits and upgrade to a paid account',
  environment: 'development',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-15 00:00:30',
  level: 'info',
  category: 'ai_operation',
  operation: 'listing_matching',
  status: 'error',
  details: '402 This request requires more credits, or fewer max_tokens. You requested up to 4000 tokens, but can only afford 2711. To increase, visit https://openrouter.ai/settings/credits and upgrade to a paid account',
  environment: 'development',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-15 00:00:30',
  level: 'info',
  category: 'ai_operation',
  operation: 'listing_matching',
  status: 'error',
  details: '402 This request requires more credits, or fewer max_tokens. You requested up to 4000 tokens, but can only afford 2711. To increase, visit https://openrouter.ai/settings/credits and upgrade to a paid account',
  environment: 'development',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-15 00:00:31',
  level: 'info',
  category: 'ai_operation',
  operation: 'listing_matching',
  status: 'error',
  details: '402 This request requires more credits, or fewer max_tokens. You requested up to 4000 tokens, but can only afford 2711. To increase, visit https://openrouter.ai/settings/credits and upgrade to a paid account',
  environment: 'development',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-15 00:00:31',
  level: 'info',
  category: 'ai_operation',
  operation: 'listing_matching',
  status: 'error',
  details: '402 This request requires more credits, or fewer max_tokens. You requested up to 4000 tokens, but can only afford 2711. To increase, visit https://openrouter.ai/settings/credits and upgrade to a paid account',
  environment: 'development',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-15 00:00:32',
  level: 'info',
  category: 'ai_operation',
  operation: 'listing_matching',
  status: 'error',
  details: '402 This request requires more credits, or fewer max_tokens. You requested up to 4000 tokens, but can only afford 2711. To increase, visit https://openrouter.ai/settings/credits and upgrade to a paid account',
  environment: 'development',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-15 00:00:32',
  level: 'info',
  category: 'ai_operation',
  operation: 'listing_matching',
  status: 'error',
  details: '402 This request requires more credits, or fewer max_tokens. You requested up to 4000 tokens, but can only afford 2711. To increase, visit https://openrouter.ai/settings/credits and upgrade to a paid account',
  environment: 'development',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-15 00:00:33',
  level: 'info',
  category: 'ai_operation',
  operation: 'listing_matching',
  status: 'error',
  details: '402 This request requires more credits, or fewer max_tokens. You requested up to 4000 tokens, but can only afford 2711. To increase, visit https://openrouter.ai/settings/credits and upgrade to a paid account',
  environment: 'development',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-15 00:00:33',
  level: 'info',
  category: 'ai_operation',
  operation: 'listing_matching',
  status: 'error',
  details: '402 This request requires more credits, or fewer max_tokens. You requested up to 4000 tokens, but can only afford 2711. To increase, visit https://openrouter.ai/settings/credits and upgrade to a paid account',
  environment: 'development',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-15 00:00:34',
  level: 'info',
  category: 'ai_operation',
  operation: 'listing_matching',
  status: 'error',
  details: '402 This request requires more credits, or fewer max_tokens. You requested up to 4000 tokens, but can only afford 2711. To increase, visit https://openrouter.ai/settings/credits and upgrade to a paid account',
  environment: 'development',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-15 00:00:34',
  level: 'info',
  category: 'ai_operation',
  operation: 'listing_matching',
  status: 'error',
  details: '402 This request requires more credits, or fewer max_tokens. You requested up to 4000 tokens, but can only afford 2711. To increase, visit https://openrouter.ai/settings/credits and upgrade to a paid account',
  environment: 'development',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-15 00:00:34',
  level: 'info',
  category: 'ai_operation',
  operation: 'listing_matching',
  status: 'error',
  details: '402 This request requires more credits, or fewer max_tokens. You requested up to 4000 tokens, but can only afford 2711. To increase, visit https://openrouter.ai/settings/credits and upgrade to a paid account',
  environment: 'development',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-15 00:00:35',
  level: 'info',
  category: 'ai_operation',
  operation: 'listing_matching',
  status: 'error',
  details: '402 This request requires more credits, or fewer max_tokens. You requested up to 4000 tokens, but can only afford 2711. To increase, visit https://openrouter.ai/settings/credits and upgrade to a paid account',
  environment: 'development',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-15 00:00:36',
  level: 'info',
  category: 'ai_operation',
  operation: 'listing_matching',
  status: 'error',
  details: '402 This request requires more credits, or fewer max_tokens. You requested up to 4000 tokens, but can only afford 2711. To increase, visit https://openrouter.ai/settings/credits and upgrade to a paid account',
  environment: 'development',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-15 00:00:36',
  level: 'info',
  category: 'ai_operation',
  operation: 'listing_matching',
  status: 'error',
  details: '402 This request requires more credits, or fewer max_tokens. You requested up to 4000 tokens, but can only afford 2711. To increase, visit https://openrouter.ai/settings/credits and upgrade to a paid account',
  environment: 'development',
  message: 'AI Operation'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/api/listings/68758ad8cadc089245b0c4fd',
  ip: '::ffff:************',
  userAgent: 'okhttp/4.12.0',
  statusCode: 200,
  responseTime: '6ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-15 00:00:37'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/api/listings/68758ad8cadc089245b0c4fa',
  ip: '::ffff:************',
  userAgent: 'okhttp/4.12.0',
  statusCode: 200,
  responseTime: '5ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-15 00:00:46'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/api/listings/68758ad8cadc089245b0c4f1',
  ip: '::ffff:************',
  userAgent: 'okhttp/4.12.0',
  statusCode: 200,
  responseTime: '4ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-15 00:00:56'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/api/listings/68758ad8cadc089245b0c4f1',
  ip: '::ffff:************',
  userAgent: 'okhttp/4.12.0',
  statusCode: 304,
  responseTime: '3ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-15 00:01:15'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/api/listings/68758ad8cadc089245b0c4e8',
  ip: '::ffff:************',
  userAgent: 'okhttp/4.12.0',
  statusCode: 200,
  responseTime: '4ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-15 00:01:30'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/api/listings?limit=10&sortBy=dateAdded&sortOrder=desc',
  ip: '::ffff:************',
  userAgent: 'okhttp/4.12.0',
  statusCode: 304,
  responseTime: '3ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-15 00:02:20'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/api/search/stats',
  ip: '::ffff:************',
  userAgent: 'okhttp/4.12.0',
  statusCode: 304,
  responseTime: '2ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-15 00:02:20'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/api/listings?limit=10&sortBy=dateAdded&sortOrder=desc',
  ip: '::ffff:************',
  userAgent: 'okhttp/4.12.0',
  statusCode: 304,
  responseTime: '3ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-15 00:02:58'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/api/search/stats',
  ip: '::ffff:************',
  userAgent: 'okhttp/4.12.0',
  statusCode: 304,
  responseTime: '2ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-15 00:02:58'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/api/listings?limit=10&sortBy=dateAdded&sortOrder=desc',
  ip: '::ffff:************',
  userAgent: 'okhttp/4.12.0',
  statusCode: 304,
  responseTime: '3ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-15 00:03:05'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/api/search/stats',
  ip: '::ffff:************',
  userAgent: 'okhttp/4.12.0',
  statusCode: 304,
  responseTime: '1ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-15 00:03:05'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-07-15 00:05:00'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  duration: '330577ms',
  listingsProcessed: 39,
  level: 'info',
  message: 'Scheduled scraping completed',
  timestamp: '2025-07-15 00:05:30'
}
{
  service: 'zakmakelaar-api',
  port: '3000',
  environment: 'development',
  endpoints: {
    api: 'http://localhost:3000',
    docs: 'http://localhost:3000/api-docs',
    health: 'http://localhost:3000/health'
  },
  level: 'info',
  message: '🚀 ZakMakelaar API Server Started',
  timestamp: '2025-07-15 00:06:58'
}
{
  service: 'zakmakelaar-api',
  port: '3000',
  environment: 'development',
  endpoints: {
    api: 'http://localhost:3000',
    docs: 'http://localhost:3000/api-docs',
    health: 'http://localhost:3000/health'
  },
  level: 'info',
  message: '🚀 ZakMakelaar API Server Started',
  timestamp: '2025-07-15 00:07:39'
}
{
  service: 'zakmakelaar-api',
  port: '3000',
  environment: 'development',
  endpoints: {
    api: 'http://localhost:3000',
    docs: 'http://localhost:3000/api-docs',
    health: 'http://localhost:3000/health'
  },
  level: 'info',
  message: '🚀 ZakMakelaar API Server Started',
  timestamp: '2025-07-15 00:07:51'
}
