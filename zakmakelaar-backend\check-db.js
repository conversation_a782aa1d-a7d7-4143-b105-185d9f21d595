const mongoose = require('mongoose');
const Listing = require('./src/models/Listing');

async function checkDatabase() {
  try {
    await mongoose.connect('mongodb://localhost:27017/zakmakelaar');
    console.log('✅ Connected to MongoDB');
    
    const count = await Listing.countDocuments();
    console.log(`📊 Total listings: ${count}`);
    
    if (count > 0) {
      const listings = await Listing.find().limit(5);
      console.log('\n📋 Current listings:');
      listings.forEach((listing, index) => {
        console.log(`${index + 1}. ${listing.title}`);
        console.log(`   Price: ${listing.price}`);
        console.log(`   Location: ${listing.location}`);
        console.log(`   Rooms: ${listing.rooms}`);
        console.log(`   ID: ${listing._id}`);
        console.log('');
      });
    }
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await mongoose.connection.close();
    console.log('🔌 Disconnected from MongoDB');
  }
}

checkDatabase();
