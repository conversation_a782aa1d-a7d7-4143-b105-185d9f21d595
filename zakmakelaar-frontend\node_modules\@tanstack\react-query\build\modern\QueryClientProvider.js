"use client";

// src/QueryClientProvider.tsx
import * as React from "react";
import { jsx } from "react/jsx-runtime";
var QueryClientContext = React.createContext(
  void 0
);
var useQueryClient = (queryClient) => {
  const client = React.useContext(QueryClientContext);
  if (queryClient) {
    return queryClient;
  }
  if (!client) {
    throw new Error("No QueryClient set, use QueryClientProvider to set one");
  }
  return client;
};
var QueryClientProvider = ({
  client,
  children
}) => {
  React.useEffect(() => {
    client.mount();
    return () => {
      client.unmount();
    };
  }, [client]);
  return /* @__PURE__ */ jsx(QueryClientContext.Provider, { value: client, children });
};
export {
  QueryClientContext,
  QueryClientProvider,
  useQueryClient
};
//# sourceMappingURL=QueryClientProvider.js.map