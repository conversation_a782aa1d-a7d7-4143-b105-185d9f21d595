const mongoose = require("mongoose");
const User = require("./src/models/User");
const config = require("./src/config/config");

async function makeUserAdmin(email) {
  try {
    await mongoose.connect(config.mongoURI);
    console.log("Connected to MongoDB");

    const user = await User.findOne({ email });
    if (!user) {
      console.log(`User with email ${email} not found`);
      return;
    }

    user.role = "admin";
    await user.save();
    console.log(`User ${email} is now an admin`);

    await mongoose.connection.close();
  } catch (error) {
    console.error("Error:", error);
  }
}

// Get email from command line argument
const email = process.argv[2];
if (!email) {
  console.log("Usage: node make-admin.js <email>");
  process.exit(1);
}

makeUserAdmin(email);
