import React, { useState } from "react";
import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  SafeAreaView,
  ScrollView,
  TextInput,
  Alert,
  ActivityIndicator,
} from "react-native";
import { useRouter } from "expo-router";
import { useSafeAreaInsets } from "react-native-safe-area-context";

// Header Component
const Header = ({
  title,
  showBackButton = false,
  onBack,
}: {
  title: string;
  showBackButton?: boolean;
  onBack?: () => void;
}) => {
  const insets = useSafeAreaInsets();

  return (
    <View style={[styles.header, { paddingTop: Math.max(insets.top, 16) }]}>
      {showBackButton ? (
        <TouchableOpacity onPress={onBack} style={styles.backButton}>
          <Text style={styles.backButtonText}>←</Text>
        </TouchableOpacity>
      ) : (
        <View style={styles.placeholder} />
      )}
      <View style={styles.headerCenter}>
        <View style={styles.logoContainer}>
          <Text style={styles.logoText}>ZM</Text>
        </View>
        <Text style={styles.headerTitle}>ZakMakelaar</Text>
      </View>
      <View style={styles.placeholder} />
    </View>
  );
};

export default function ApplicationScreen() {
  const router = useRouter();
  const [applicationText, setApplicationText] = useState("");
  const [isLoadingAI, setIsLoadingAI] = useState(false);

  const generateApplication = async () => {
    setIsLoadingAI(true);
    setApplicationText("Generating your personalized application letter...");

    // Simulate API call to AI layer
    try {
      // In a real app, you would call an AI API here
      await new Promise((resolve) => setTimeout(resolve, 2000)); // Simulate delay

      const generatedText = `Dear Property Manager,

I am writing to express my strong interest in renting the apartment you have listed. As a responsible and reliable tenant, I believe I would be an excellent fit for your property.

About me:
- Young professional with stable employment
- Excellent credit history and financial standing
- Non-smoker with a quiet, respectful lifestyle
- Previous positive rental references available

I am particularly drawn to this property because of its location and amenities. I am prepared to provide all necessary documentation including proof of income, references, and a security deposit.

I would welcome the opportunity to discuss my application further and arrange a viewing at your convenience.

Thank you for your consideration.

Best regards,
[Your Name]`;

      setApplicationText(generatedText);
    } catch (error) {
      console.error("Error generating application:", error);
      setApplicationText("Error generating application. Please try again.");
    } finally {
      setIsLoadingAI(false);
    }
  };

  const sendApplication = () => {
    Alert.alert(
      "Application Sent!",
      "Your application has been submitted successfully.",
      [{ text: "OK" }]
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <Header
        title="Rental Application"
        showBackButton={true}
        onBack={() => router.back()}
      />
      <ScrollView
        style={styles.scrollContainer}
        contentContainerStyle={styles.applicationContainer}
      >
        <Text style={styles.applicationTitle}>Generate Your Application</Text>
        <Text style={styles.applicationSubtitle}>
          Let our AI draft a personalized application letter for you.
        </Text>

        <TouchableOpacity
          style={[
            styles.generateButton,
            (isLoadingAI || !applicationText) && styles.disabledButton,
          ]}
          onPress={generateApplication}
          disabled={isLoadingAI}
        >
          {isLoadingAI ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="small" color="#ffffff" />
              <Text style={styles.generateButtonText}>Generating...</Text>
            </View>
          ) : (
            <Text style={styles.generateButtonText}>Generate Letter →</Text>
          )}
        </TouchableOpacity>

        <TextInput
          style={styles.textArea}
          placeholder="Your AI-generated application letter will appear here..."
          value={applicationText}
          onChangeText={setApplicationText}
          multiline
          numberOfLines={12}
          textAlignVertical="top"
        />

        <TouchableOpacity
          style={[
            styles.sendButton,
            (!applicationText || isLoadingAI) && styles.disabledButton,
          ]}
          onPress={sendApplication}
          disabled={!applicationText || isLoadingAI}
        >
          <Text style={styles.sendButtonText}>Send Application</Text>
        </TouchableOpacity>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f3f4f6",
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    width: "100%",
    paddingHorizontal: 16,
    paddingBottom: 16,
    backgroundColor: "#ffffff",
    borderBottomWidth: 1,
    borderBottomColor: "#e5e7eb",
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
  },
  backButton: {
    padding: 8,
  },
  backButtonText: {
    fontSize: 20,
    color: "#f72585",
  },
  placeholder: {
    width: 24,
  },
  headerCenter: {
    flexDirection: "row",
    alignItems: "center",
  },
  logoContainer: {
    width: 32,
    height: 32,
    backgroundColor: "#f72585",
    borderRadius: 8,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 8,
  },
  logoText: {
    fontSize: 16,
    fontWeight: "bold",
    color: "#ffffff",
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: "bold",
    color: "#1f2937",
  },
  scrollContainer: {
    flex: 1,
  },
  applicationContainer: {
    padding: 24,
    backgroundColor: "#ffffff",
    marginTop: 0,
    borderBottomLeftRadius: 16,
    borderBottomRightRadius: 16,
  },
  applicationTitle: {
    fontSize: 20,
    fontWeight: "600",
    color: "#1f2937",
    marginBottom: 16,
    marginTop: 16,
  },
  applicationSubtitle: {
    fontSize: 16,
    color: "#6b7280",
    marginBottom: 24,
    lineHeight: 22,
  },
  generateButton: {
    width: "100%",
    backgroundColor: "#f72585",
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 12,
    alignItems: "center",
    marginBottom: 24,
  },
  generateButtonText: {
    color: "#ffffff",
    fontSize: 18,
    fontWeight: "600",
  },
  loadingContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  textArea: {
    width: "100%",
    height: 256,
    padding: 16,
    borderWidth: 1,
    borderColor: "#e5e7eb",
    borderRadius: 12,
    fontSize: 16,
    color: "#374151",
    backgroundColor: "#ffffff",
    marginBottom: 24,
  },
  sendButton: {
    width: "100%",
    backgroundColor: "#16a34a",
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 12,
    alignItems: "center",
  },
  sendButtonText: {
    color: "#ffffff",
    fontSize: 18,
    fontWeight: "600",
  },
  disabledButton: {
    opacity: 0.5,
  },
});
