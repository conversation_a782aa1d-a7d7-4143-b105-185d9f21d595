// Test price formatting functionality
function formatPrice(price) {
  if (price === undefined || price === null || price === "") {
    return "€ --";
  }

  let numPrice;

  if (typeof price === "string") {
    // Remove all non-numeric characters except dots and commas
    const cleanPrice = price.replace(/[^\d.,]/g, "");

    // Handle different number formats:
    let normalizedPrice;

    // Check if it's European format (dot as thousands, comma as decimal)
    if (
      cleanPrice.includes(",") &&
      cleanPrice.includes(".") &&
      cleanPrice.lastIndexOf(".") < cleanPrice.lastIndexOf(",")
    ) {
      // European format: "1.500,50" -> "1500.50"
      normalizedPrice = cleanPrice.replace(/\./g, "").replace(",", ".");
    }
    // Check if it's US format (comma as thousands, dot as decimal)
    else if (
      cleanPrice.includes(",") &&
      cleanPrice.includes(".") &&
      cleanPrice.lastIndexOf(",") < cleanPrice.lastIndexOf(".")
    ) {
      // US format: "1,500.50" -> "1500.50"
      normalizedPrice = cleanPrice.replace(/,/g, "");
    }
    // Only comma (could be thousands separator or decimal)
    else if (cleanPrice.includes(",") && !cleanPrice.includes(".")) {
      // If comma is in thousands position (e.g., "1,500"), remove it
      // If comma is in decimal position (e.g., "15,50"), replace with dot
      const commaIndex = cleanPrice.indexOf(",");
      const digitsAfterComma = cleanPrice.length - commaIndex - 1;

      if (digitsAfterComma === 3 && commaIndex > 0) {
        // Thousands separator: "1,500" -> "1500"
        normalizedPrice = cleanPrice.replace(/,/g, "");
      } else {
        // Decimal separator: "15,50" -> "15.50"
        normalizedPrice = cleanPrice.replace(",", ".");
      }
    }
    // Only dot (could be thousands separator or decimal)
    else if (cleanPrice.includes(".") && !cleanPrice.includes(",")) {
      const dotIndex = cleanPrice.indexOf(".");
      const digitsAfterDot = cleanPrice.length - dotIndex - 1;

      if (digitsAfterDot === 3 && dotIndex > 0) {
        // Thousands separator: "1.500" -> "1500"
        normalizedPrice = cleanPrice.replace(/\./g, "");
      } else {
        // Decimal separator: "15.50" -> "15.50"
        normalizedPrice = cleanPrice;
      }
    }
    // No separators
    else {
      normalizedPrice = cleanPrice;
    }

    numPrice = parseFloat(normalizedPrice);
  } else {
    numPrice = price;
  }

  if (isNaN(numPrice) || numPrice <= 0) {
    return "€ --";
  }

  return new Intl.NumberFormat("nl-NL", {
    style: "currency",
    currency: "EUR",
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(numPrice);
}

// Test cases
console.log("🧪 Testing price formatting...\n");

const testCases = [
  { input: "€ 1,500 per maand", expected: "€ 1.500" },
  { input: "€ 2,800 per maand", expected: "€ 2.800" },
  { input: "€ 500 per maand", expected: "€ 500" },
  { input: "€ 15,000 per maand", expected: "€ 15.000" },
  { input: "€1,200", expected: "€ 1.200" },
  { input: "1500", expected: "€ 1.500" },
  { input: "€ 1.500 per maand", expected: "€ 1.500" },
  { input: "€ 1.500,50 per maand", expected: "€ 1.501" },
  { input: "€ 1,500.50 per maand", expected: "€ 1.501" },
  { input: "", expected: "€ --" },
  { input: null, expected: "€ --" },
  { input: undefined, expected: "€ --" },
];

testCases.forEach((testCase, index) => {
  const result = formatPrice(testCase.input);
  const passed = result === testCase.expected;

  console.log(`Test ${index + 1}: ${passed ? "✅" : "❌"}`);
  console.log(`  Input: "${testCase.input}"`);
  console.log(`  Expected: "${testCase.expected}"`);
  console.log(`  Got: "${result}"`);
  if (!passed) {
    console.log(`  ❌ FAILED!`);
  }
  console.log("");
});

console.log("🎉 Price formatting tests completed!");
