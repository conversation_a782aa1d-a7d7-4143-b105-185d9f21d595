const http = require('http');

function testAPI() {
  const options = {
    hostname: 'localhost',
    port: 3000,
    path: '/api/listings?limit=3',
    method: 'GET',
    headers: {
      'Content-Type': 'application/json'
    }
  };

  const req = http.request(options, (res) => {
    let data = '';

    res.on('data', (chunk) => {
      data += chunk;
    });

    res.on('end', () => {
      try {
        const response = JSON.parse(data);
        console.log('🔍 API Response Status:', res.statusCode);
        console.log('📊 Response Data:');
        console.log('Status:', response.status);
        console.log('Results:', response.results);
        
        if (response.data && response.data.listings) {
          console.log('\n📋 First 3 listings from API:');
          response.data.listings.forEach((listing, index) => {
            console.log(`${index + 1}. ${listing.title}`);
            console.log(`   ID: ${listing._id}`);
            console.log(`   Price: ${listing.price} (${typeof listing.price})`);
            console.log(`   Location: ${listing.location} (${typeof listing.location})`);
            console.log(`   Rooms: ${listing.rooms} (${typeof listing.rooms})`);
            console.log(`   Source: ${listing.source}`);
            console.log('');
          });
        } else {
          console.log('❌ No listings data in response');
        }
      } catch (error) {
        console.error('❌ Error parsing response:', error);
        console.log('Raw response:', data);
      }
    });
  });

  req.on('error', (error) => {
    console.error('❌ API request failed:', error);
  });

  req.end();
}

console.log('🧪 Testing API endpoint: http://localhost:3000/api/listings?limit=3');
testAPI();
