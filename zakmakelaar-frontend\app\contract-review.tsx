import React, { useState } from "react";
import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  SafeAreaView,
  ScrollView,
  TextInput,
  ActivityIndicator,
} from "react-native";
import { useRouter } from "expo-router";

// Header Component
const Header = ({
  title,
  showBackButton = false,
  onBack,
}: {
  title: string;
  showBackButton?: boolean;
  onBack?: () => void;
}) => (
  <View style={styles.header}>
    {showBackButton ? (
      <TouchableOpacity onPress={onBack} style={styles.backButton}>
        <Text style={styles.backButtonText}>←</Text>
      </TouchableOpacity>
    ) : (
      <View style={styles.placeholder} />
    )}
    <View style={styles.headerCenter}>
      <View style={styles.logoContainer}>
        <Text style={styles.logoText}>ZM</Text>
      </View>
      <Text style={styles.headerTitle}>ZakMakelaar</Text>
    </View>
    <View style={styles.placeholder} />
  </View>
);

export default function ContractReviewScreen() {
  const router = useRouter();
  const [contractText, setContractText] = useState("");
  const [analysisResult, setAnalysisResult] = useState("");
  const [isLoadingAI, setIsLoadingAI] = useState(false);

  const analyzeContract = async () => {
    setIsLoadingAI(true);
    setAnalysisResult("Analyzing your contract for legal risks...");

    // Simulate API call to AI layer
    try {
      // In a real app, you would call an AI API here
      await new Promise((resolve) => setTimeout(resolve, 3000)); // Simulate delay

      const analysisText = `Contract Analysis Results:

KEY TERMS IDENTIFIED:
• Rental Period: Standard 12-month lease agreement
• Monthly Rent: As specified in the contract
• Security Deposit: Typically 1-2 months rent
• Notice Period: Usually 1 month for termination

POTENTIAL CONCERNS:
⚠️ Check for excessive penalty clauses
⚠️ Verify maintenance responsibilities are clearly defined
⚠️ Ensure deposit return conditions are fair
⚠️ Review early termination clauses

RECOMMENDATIONS:
✅ The contract appears to follow Dutch rental law standards
✅ Most terms seem reasonable for the Dutch market
✅ Consider negotiating any unclear maintenance responsibilities
✅ Ensure all verbal agreements are documented

NEXT STEPS:
• Have a legal professional review if you have concerns
• Clarify any unclear terms with the landlord
• Document the property condition before signing
• Keep copies of all communications

This analysis is for informational purposes only and does not constitute legal advice.`;

      setAnalysisResult(analysisText);
    } catch (error) {
      console.error("Error analyzing contract:", error);
      setAnalysisResult(
        "Error analyzing contract. Please check your connection."
      );
    } finally {
      setIsLoadingAI(false);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <Header
        title="Contract Review"
        showBackButton={true}
        onBack={() => router.back()}
      />
      <ScrollView
        style={styles.scrollContainer}
        contentContainerStyle={styles.contractContainer}
      >
        <Text style={styles.contractTitle}>AI Contract Analysis</Text>
        <Text style={styles.contractSubtitle}>
          Paste your rental contract text below for an AI-powered legal review.
        </Text>

        <TextInput
          style={styles.textArea}
          placeholder="Paste your contract text here..."
          value={contractText}
          onChangeText={setContractText}
          multiline
          numberOfLines={12}
          textAlignVertical="top"
        />

        <TouchableOpacity
          style={[
            styles.analyzeButton,
            (!contractText || isLoadingAI) && styles.disabledButton,
          ]}
          onPress={analyzeContract}
          disabled={!contractText || isLoadingAI}
        >
          {isLoadingAI ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="small" color="#ffffff" />
              <Text style={styles.analyzeButtonText}>Analyzing...</Text>
            </View>
          ) : (
            <Text style={styles.analyzeButtonText}>Analyze Contract →</Text>
          )}
        </TouchableOpacity>

        {analysisResult ? (
          <View style={styles.analysisContainer}>
            <Text style={styles.analysisTitle}>Analysis Result:</Text>
            <Text style={styles.analysisText}>{analysisResult}</Text>
          </View>
        ) : null}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f3f4f6",
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    width: "100%",
    padding: 16,
    backgroundColor: "#ffffff",
    borderBottomWidth: 1,
    borderBottomColor: "#e5e7eb",
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
  },
  backButton: {
    padding: 8,
  },
  backButtonText: {
    fontSize: 20,
    color: "#f72585",
  },
  placeholder: {
    width: 24,
  },
  headerCenter: {
    flexDirection: "row",
    alignItems: "center",
  },
  logoContainer: {
    width: 32,
    height: 32,
    backgroundColor: "#f72585",
    borderRadius: 8,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 8,
  },
  logoText: {
    fontSize: 16,
    fontWeight: "bold",
    color: "#ffffff",
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: "bold",
    color: "#1f2937",
  },
  scrollContainer: {
    flex: 1,
  },
  contractContainer: {
    padding: 24,
    backgroundColor: "#ffffff",
    marginTop: 0,
    borderBottomLeftRadius: 16,
    borderBottomRightRadius: 16,
  },
  contractTitle: {
    fontSize: 20,
    fontWeight: "600",
    color: "#1f2937",
    marginBottom: 16,
    marginTop: 16,
  },
  contractSubtitle: {
    fontSize: 16,
    color: "#6b7280",
    marginBottom: 24,
    lineHeight: 22,
  },
  textArea: {
    width: "100%",
    height: 256,
    padding: 16,
    borderWidth: 1,
    borderColor: "#e5e7eb",
    borderRadius: 12,
    fontSize: 16,
    color: "#374151",
    backgroundColor: "#ffffff",
    marginBottom: 24,
  },
  analyzeButton: {
    width: "100%",
    backgroundColor: "#f72585",
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 12,
    alignItems: "center",
    marginBottom: 24,
  },
  analyzeButtonText: {
    color: "#ffffff",
    fontSize: 18,
    fontWeight: "600",
  },
  loadingContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  disabledButton: {
    opacity: 0.5,
  },
  analysisContainer: {
    backgroundColor: "#f9fafb",
    borderWidth: 1,
    borderColor: "#e5e7eb",
    padding: 16,
    borderRadius: 12,
  },
  analysisTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#1f2937",
    marginBottom: 8,
  },
  analysisText: {
    fontSize: 14,
    color: "#374151",
    lineHeight: 20,
  },
});
