const axios = require("axios");

async function testStats() {
  try {
    console.log("📊 Testing stats API...");

    // Test stats endpoint
    console.log("\n📈 Testing /api/search/stats");
    const response = await axios.get("http://localhost:3000/api/search/stats");

    console.log(`✅ Status: ${response.status}`);
    console.log(`📊 Stats data:`, JSON.stringify(response.data, null, 2));

    if (response.data.success && response.data.data) {
      const stats = response.data.data;
      console.log("\n📋 Parsed stats:");
      console.log(`Total listings: ${stats.total}`);
      console.log(`Average price: ${stats.averagePrice}`);
      console.log(
        `Price range: ${stats.priceRange?.min} - ${stats.priceRange?.max}`
      );
      console.log(`By source:`, stats.bySource);
      console.log(`By property type:`, stats.byPropertyType);
      console.log(`By city:`, stats.byCity);
    }

    console.log("\n🎉 Stats test completed!");
  } catch (error) {
    console.error("❌ Stats test failed:", error.message);
    if (error.response) {
      console.error("Response status:", error.response.status);
      console.error("Response data:", error.response.data);
    }
  }
}

testStats();
