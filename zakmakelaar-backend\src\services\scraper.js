const puppeteer = require("puppeteer");
const cheerio = require("cheerio");
const Listing = require("../models/Listing");
const { sendAlerts } = require("./alertService");
const {
  classifyError,
  handleErrorWithRecovery,
  logError,
  create<PERSON>ealthCheck,
  NetworkError,
  TimeoutError,
  BrowserError,
  ParsingError,
} = require("../utils/scraperErrors");

// Browser pool for better resource management
class BrowserPool {
  constructor(maxBrowsers = 2) {
    this.browsers = [];
    this.maxBrowsers = maxBrowsers;
    this.currentIndex = 0;
  }

  async getBrowser() {
    if (this.browsers.length < this.maxBrowsers) {
      const browser = await puppeteer.launch({
        headless: false,
        args: [
          "--no-sandbox",
          "--disable-setuid-sandbox",
          "--disable-infobars",
          "--window-position=0,0",
          "--ignore-certificate-errors",
          "--disable-background-timer-throttling",
          "--disable-backgrounding-occluded-windows",
          "--disable-renderer-backgrounding",
          "--disable-dev-shm-usage",
          "--disable-extensions",
        ],
      });
      this.browsers.push(browser);
      return browser;
    }

    // Round-robin selection
    const browser = this.browsers[this.currentIndex];
    this.currentIndex = (this.currentIndex + 1) % this.browsers.length;
    return browser;
  }

  async closeAll() {
    await Promise.all(this.browsers.map((browser) => browser.close()));
    this.browsers = [];
  }
}

const browserPool = new BrowserPool();

// Data validation and normalization utilities
const validateAndNormalizeListing = (listingData) => {
  if (!listingData.title || !listingData.url || !listingData.location) {
    return null; // Invalid listing
  }

  // Normalize title
  let normalizedTitle = listingData.title.trim();
  // Remove extra whitespace and normalize
  normalizedTitle = normalizedTitle.replace(/\s+/g, " ");
  // Ensure proper capitalization
  if (normalizedTitle.length > 0) {
    normalizedTitle =
      normalizedTitle.charAt(0).toUpperCase() + normalizedTitle.slice(1);
  }

  // Normalize price
  let normalizedPrice = listingData.price;
  if (normalizedPrice && typeof normalizedPrice === "string") {
    // Clean up HTML entities, non-breaking spaces, and extra content
    normalizedPrice = normalizedPrice
      .replace(/&nbsp;/g, " ")
      .replace(/\u00A0/g, " ") // Non-breaking space character (160)
      .replace(/\s+/g, " ")
      .trim();

    // Extract only the price part (before any newlines or extra content)
    const priceLineMatch = normalizedPrice.match(/^([^\\n]+)/);
    if (priceLineMatch) {
      normalizedPrice = priceLineMatch[1].trim();
    }

    // Handle common Dutch price formats
    if (
      normalizedPrice.toLowerCase().includes("op aanvraag") ||
      normalizedPrice.toLowerCase().includes("on request")
    ) {
      normalizedPrice = "Prijs op aanvraag";
    } else {
      // Extract numeric value and format consistently
      const priceMatch = normalizedPrice.match(/€\s*([\d.,]+)/); // Modified to capture numbers with thousands separators
      if (priceMatch) {
        // Handle European number formats where comma can be thousands separator
        let extractedPrice = priceMatch[1].trim();
        
        let numericPrice;
        
        // Special case for the format in the screenshot: €3,950 (treat as 3950, not 3.95)
        if (extractedPrice.match(/^\d{1,3},\d{3}$/) && !normalizedPrice.includes('.')) {
          // This is the format from the screenshot - comma is a thousands separator
          numericPrice = parseFloat(extractedPrice.replace(/,/g, ''));
        }
        // Case 1: Format with multiple thousands separators and decimal comma (e.g., 1.234.567,89)
        else if (extractedPrice.match(/\d{1,3}(\.\d{3})+,\d+$/)) {
          numericPrice = parseFloat(
            extractedPrice.replace(/\./g, '').replace(',', '.')
          );
        }
        // Case 2: Format with single thousands separator and decimal comma (e.g., 3.950,00)
        else if (extractedPrice.match(/\d{1,3}\.\d{3},\d+$/)) {
          numericPrice = parseFloat(
            extractedPrice.replace(/\./g, '').replace(',', '.')
          );
        }
        // Case 3: Format with comma as thousands separator (e.g., 3,950)
        else if (extractedPrice.match(/\d{1,3},\d{3}$/)) {
          numericPrice = parseFloat(extractedPrice.replace(/,/g, ''));
        }
        // Case 4: Format with period as thousands separator (e.g., 3.950)
        else if (extractedPrice.match(/\d{1,3}\.\d{3}$/)) {
          numericPrice = parseFloat(extractedPrice.replace(/\./g, ''));
        }
        // Case 5: Regular decimal format or other formats
        else {
          numericPrice = parseFloat(extractedPrice.replace(',', '.'));
        }
        if (numericPrice > 0) {
          normalizedPrice = `€ ${Math.round(numericPrice).toLocaleString(
            "nl-NL"
          )}`;
          // Add per month if it seems to be a rental price
          if (numericPrice < 10000) {
            normalizedPrice += " per maand";
          }
        }
      }
    }
  } else {
    normalizedPrice = "Prijs op aanvraag";
  }

  // Normalize location
  let normalizedLocation = listingData.location.replace(/\s+/g, " ").trim();

  // Remove common prefixes and clean up
  normalizedLocation = normalizedLocation
    .replace(/^(huis|appartement|kamer|woning)\s+/i, "")
    .toLowerCase()
    .replace(/\b\w/g, (l) => l.toUpperCase()); // Title case

  // Validate URL
  const isValidUrl =
    listingData.url.startsWith("http") &&
    (listingData.url.includes("funda.nl") ||
      listingData.url.includes("pararius.nl"));

  if (!isValidUrl) {
    return null;
  }

  // Validate property type
  const validPropertyTypes = [
    "huis",
    "appartement",
    "kamer",
    "parkeergelegenheid",
    "woning",
  ];
  let normalizedPropertyType = listingData.propertyType || "woning";
  if (!validPropertyTypes.includes(normalizedPropertyType)) {
    normalizedPropertyType = "woning";
  }

  return {
    ...listingData,
    title: normalizedTitle,
    price: normalizedPrice,
    location: normalizedLocation,
    propertyType: normalizedPropertyType,
    dateAdded: new Date(),
  };
};

// Anti-detection utilities
const getRandomUserAgent = () => {
  const userAgents = [
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.2 Safari/605.1.15",
  ];
  return userAgents[Math.floor(Math.random() * userAgents.length)];
};

const getRandomDelay = (min = 2000, max = 8000) => {
  return Math.floor(Math.random() * (max - min + 1)) + min;
};

const setupPageStealth = async (page) => {
  // Set random user agent
  await page.setUserAgent(getRandomUserAgent());

  // Set realistic viewport
  const viewports = [
    { width: 1920, height: 1080 },
    { width: 1366, height: 768 },
    { width: 1440, height: 900 },
    { width: 1536, height: 864 },
  ];
  const viewport = viewports[Math.floor(Math.random() * viewports.length)];
  await page.setViewport({ ...viewport, deviceScaleFactor: 1 });

  // Override webdriver detection
  await page.evaluateOnNewDocument(() => {
    Object.defineProperty(navigator, "webdriver", {
      get: () => undefined,
    });
  });

  // Add realistic headers
  await page.setExtraHTTPHeaders({
    "Accept-Language": "en-US,en;q=0.9,nl;q=0.8",
    "Accept-Encoding": "gzip, deflate, br",
    Accept:
      "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
    Connection: "keep-alive",
    "Upgrade-Insecure-Requests": "1",
  });
};

// Metrics and monitoring
class ScrapingMetrics {
  constructor() {
    this.metrics = {
      totalScrapes: 0,
      successfulScrapes: 0,
      failedScrapes: 0,
      totalListingsFound: 0,
      totalListingsSaved: 0,
      duplicatesSkipped: 0,
      averageScrapingTime: 0,
      lastScrapeTime: null,
      errorsByType: {},
    };
  }

  recordScrapeStart() {
    this.startTime = Date.now();
    this.metrics.totalScrapes++;
  }

  recordScrapeSuccess(listingsFound, listingsSaved, duplicatesSkipped) {
    const duration = Date.now() - this.startTime;
    this.metrics.successfulScrapes++;
    this.metrics.totalListingsFound += listingsFound;
    this.metrics.totalListingsSaved += listingsSaved;
    this.metrics.duplicatesSkipped += duplicatesSkipped;
    this.metrics.lastScrapeTime = new Date();

    // Update average scraping time
    this.metrics.averageScrapingTime =
      (this.metrics.averageScrapingTime * (this.metrics.successfulScrapes - 1) +
        duration) /
      this.metrics.successfulScrapes;
  }

  recordScrapeFailure(error) {
    this.metrics.failedScrapes++;

    // Classify and log the error
    const classifiedError = classifyError(error);

    const errorType = classifiedError.code || "UNKNOWN_ERROR";
    this.metrics.errorsByType[errorType] =
      (this.metrics.errorsByType[errorType] || 0) + 1;

    // Log the error with context
    logError(classifiedError, {
      context: "scrape_failure",
      totalScrapes: this.metrics.totalScrapes,
      failedScrapes: this.metrics.failedScrapes,
    });
  }

  getMetrics() {
    return {
      ...this.metrics,
      successRate:
        this.metrics.totalScrapes > 0
          ? (
              (this.metrics.successfulScrapes / this.metrics.totalScrapes) *
              100
            ).toFixed(2) + "%"
          : "0%",
      averageScrapingTimeFormatted: `${(
        this.metrics.averageScrapingTime / 1000
      ).toFixed(2)}s`,
    };
  }
}

const scrapingMetrics = new ScrapingMetrics();

const scrapePararius = async () => {
  scrapingMetrics.recordScrapeStart();
  let browser = null;
  let page = null;
  let listingsSaved = 0;
  let duplicatesSkipped = 0;

  try {
    browser = await browserPool.getBrowser();
    page = await browser.newPage();

    await setupPageStealth(page);
    await page.goto("https://www.pararius.nl/huurwoningen/nederland", {
      waitUntil: "networkidle2",
      timeout: 60000,
    });

    // Add random delay
    await new Promise((resolve) =>
      setTimeout(resolve, getRandomDelay(1000, 3000))
    );

    const html = await page.content();
    const $ = cheerio.load(html);

    const listings = [];
    $(".search-list__item--listing").each((index, element) => {
      const titleElement = $(element).find(".listing-search-item__title a");
      const title = titleElement.text().trim();

      // Get price and clean it up
      let price = $(element).find(".listing-search-item__price").text().trim();

      // Clean up price by taking only the first line (before any newlines)
      if (price) {
        const priceLines = price.split("\n");
        price = priceLines[0].trim();
        // Remove non-breaking spaces and extra whitespace
        price = price
          .replace(/\u00A0/g, " ")
          .replace(/\s+/g, " ")
          .trim();
      }

      const location = $(element)
        .find(".listing-search-item__sub-title")
        .text()
        .trim();

      const href = titleElement.attr("href");
      const url = href ? "https://www.pararius.nl" + href : null;

      if (title && url && location) {
        const listingData = validateAndNormalizeListing({
          title,
          price: price || "Prijs op aanvraag",
          location,
          url,
          propertyType: title.toLowerCase().includes("appartement")
            ? "appartement"
            : title.toLowerCase().includes("huis")
            ? "huis"
            : title.toLowerCase().includes("kamer")
            ? "kamer"
            : title.toLowerCase().includes("studio")
            ? "studio"
            : "woning",
          source: "pararius.nl",
        });

        if (listingData) {
          console.log(
            `Pararius found: ${listingData.title} - ${listingData.price} - ${listingData.location}`
          );
          listings.push(listingData);
        }
      }
    });

    console.log(`Found ${listings.length} listings from Pararius.`);

    // Process listings with better error handling
    for (const listingData of listings) {
      try {
        const newListing = new Listing(listingData);
        await newListing.save();
        console.log(`Saved listing: ${newListing.title}`);
        listingsSaved++;
        sendAlerts(newListing);
      } catch (error) {
        if (error.code === 11000) {
          // Duplicate key error
          console.log(`Skipping duplicate listing: ${listingData.title}`);
          duplicatesSkipped++;
        } else {
          console.error(`Error saving listing ${listingData.title}:`, error);
        }
      }
    }

    scrapingMetrics.recordScrapeSuccess(
      listings.length,
      listingsSaved,
      duplicatesSkipped
    );
    return listings;
  } catch (error) {
    console.error("Error during Pararius scraping:", error);
    scrapingMetrics.recordScrapeFailure(error);
    return [];
  } finally {
    // Close the page to free up resources
    if (page) {
      try {
        await page.close();
      } catch (closeError) {
        console.error("Error closing Pararius page:", closeError);
      }
    }
    console.log(
      `Pararius scraping completed. Saved: ${listingsSaved}, Duplicates: ${duplicatesSkipped}`
    );
  }
};

const scrapeFunda = async (retryCount = 0, maxRetries = 3) => {
  if (retryCount === 0) {
    scrapingMetrics.recordScrapeStart();
  }

  let browser = null;
  let page = null;
  let listingsSaved = 0;
  let duplicatesSkipped = 0;

  try {
    // Use browser pool instead of launching new browser each time
    browser = await browserPool.getBrowser();
    page = await browser.newPage();

    // Apply stealth settings
    await setupPageStealth(page);

    // Enable JavaScript
    await page.setJavaScriptEnabled(true);

    // Add cookies to make it look like a returning user
    await page.setCookie(
      {
        name: "OptanonAlertBoxClosed",
        value: new Date().toISOString(),
        domain: ".funda.nl",
        httpOnly: false,
        secure: true,
      },
      {
        name: "OptanonConsent",
        value:
          "isGpcEnabled=0&datestamp=" +
          new Date().toISOString() +
          "&version=6.26.0",
        domain: ".funda.nl",
        httpOnly: false,
        secure: true,
      },
      {
        name: "ajs_anonymous_id",
        value: "%22" + Math.random().toString(36).substring(2, 15) + "%22",
        domain: ".funda.nl",
        httpOnly: false,
        secure: true,
      }
    );

    // Navigate to the page with a realistic timeout and networkidle setting
    console.log("Navigating to Funda rental listings page...");
    await page.goto(
      "https://www.funda.nl/zoeken/huur/?selected_area=%5B%22nl%22%5D",
      {
        waitUntil: "networkidle0",
        timeout: 90000,
      }
    );

    // Add random delay to simulate human behavior (2-5 seconds)
    const randomDelay = Math.floor(Math.random() * 3000) + 2000;
    console.log(`Waiting for ${randomDelay}ms to simulate human behavior...`);
    await new Promise((r) => setTimeout(r, randomDelay));

    // Scroll down to trigger lazy loading content
    console.log("Scrolling through page to load all content...");
    await autoScroll(page);

    // Add another small delay after scrolling
    await new Promise((r) => setTimeout(r, 1000));

    // Save HTML for inspection
    console.log("Saving page HTML for inspection...");
    const html = await page.content();
    require("fs").writeFileSync("funda_page.html", html);

    // Method 1: Extract listings from JSON-LD metadata
    console.log("Extracting listings from JSON-LD metadata...");
    const listings = [];

    // Find the JSON-LD script containing listing data
    const jsonLdMatches = html.match(
      /\<script type="application\/ld\+json" data-hid="result-list-metadata"\>(.+?)\<\/script\>/s
    );

    if (jsonLdMatches && jsonLdMatches.length > 1) {
      try {
        const jsonLdData = JSON.parse(jsonLdMatches[1]);

        if (
          jsonLdData &&
          jsonLdData.itemListElement &&
          Array.isArray(jsonLdData.itemListElement)
        ) {
          console.log(
            `Found ${jsonLdData.itemListElement.length} total listings in JSON-LD data`
          );

          // Filter only rental listings
          const rentalListings = jsonLdData.itemListElement.filter(
            (item) => item.url && item.url.includes("/huur/")
          );

          console.log(`Filtered to ${rentalListings.length} rental listings`);

          // Process each rental listing URL to extract data
          for (const item of rentalListings) {
            const urlParts = item.url.split("/");
            const id = urlParts[urlParts.length - 1];

            // Extract property details from URL structure
            let propertyType = "woning";
            let city = "";
            let streetName = "";
            let title = "";

            // Parse URL parts: /detail/huur/[city]/[property-type]-[street-name]/[id]/
            const huurIndex = urlParts.indexOf("huur");
            if (huurIndex !== -1 && huurIndex + 1 < urlParts.length) {
              // City is the first segment after 'huur'
              if (huurIndex + 1 < urlParts.length) {
                city = urlParts[huurIndex + 1].replace(/-/g, " ");
                // Capitalize first letter of each word
                city = city.replace(/\b\w/g, (l) => l.toUpperCase());
              }

              // Property type and street name are combined in the next segment
              if (huurIndex + 2 < urlParts.length) {
                const propertyStreetSegment = urlParts[huurIndex + 2];

                // Check if it starts with a known property type
                if (propertyStreetSegment.startsWith("appartement-")) {
                  propertyType = "appartement";
                  streetName = propertyStreetSegment
                    .substring(12)
                    .replace(/-/g, " ");
                } else if (propertyStreetSegment.startsWith("huis-")) {
                  propertyType = "huis";
                  streetName = propertyStreetSegment
                    .substring(5)
                    .replace(/-/g, " ");
                } else if (propertyStreetSegment.startsWith("kamer-")) {
                  propertyType = "kamer";
                  streetName = propertyStreetSegment
                    .substring(6)
                    .replace(/-/g, " ");
                } else if (
                  propertyStreetSegment.startsWith("parkeergelegenheid-")
                ) {
                  propertyType = "parkeergelegenheid";
                  streetName = propertyStreetSegment
                    .substring(19)
                    .replace(/-/g, " ");
                } else {
                  // Default to house if no specific type found
                  propertyType = "huis";
                  streetName = propertyStreetSegment.replace(/-/g, " ");
                }

                // Capitalize street name properly
                streetName = streetName.replace(/\b\w/g, (l) =>
                  l.toUpperCase()
                );
                title = streetName;
              }
            }

            // Create a listing object with extracted data
            const listingData = {
              title: title || `${propertyType} in ${city}`,
              url: item.url,
              location: city,
              propertyType,
              price: "Prijs op aanvraag", // Will be updated if we can extract actual price
              source: "funda.nl",
              dateAdded: new Date(),
            };

            console.log(
              `Found listing: ${listingData.title} - ${listingData.location}`
            );

            // Try to fetch detailed information including price
            console.log(`Fetching details for: ${item.url}`);
            const details = await fetchListingDetails(browser, item.url);

            // Update listing with detailed information
            listingData.price = details.price;
            if (details.size) listingData.size = details.size;
            if (details.bedrooms) listingData.bedrooms = details.bedrooms;

            listings.push(listingData);
          }
        }
      } catch (jsonError) {
        console.error("Error parsing JSON-LD data:", jsonError);
      }
    }

    // Method 2: If JSON-LD doesn't work, try HTML parsing as fallback
    if (listings.length === 0) {
      console.log("Falling back to HTML parsing...");

      // Load into cheerio for parsing
      const $ = cheerio.load(html);

      // Look for listing cards - this is the most direct way
      $('ol[data-test-id="search-results"] > li').each((index, element) => {
        try {
          // Skip elements without data-test-id (those are usually ads)
          if (!$(element).attr("data-test-id")) return;

          // Extract key elements
          const titleElement = $(element).find(
            'h2[data-test-id="street-name-house-number"]'
          );
          const priceElement = $(element).find('p[data-test-id="price"]');
          const locationElement = $(element).find(
            'p[data-test-id="postal-code-city"]'
          );
          const linkElement = $(element).find(
            'a[data-test-id="object-image-link"]'
          );

          if (linkElement.length) {
            const url = "https://www.funda.nl" + linkElement.attr("href");

            // Skip duplicates
            const isDuplicate = listings.some((listing) => listing.url === url);
            if (isDuplicate) return;

            // Extract basic details
            const title = titleElement.length ? titleElement.text().trim() : "";
            const price = priceElement.length
              ? priceElement.text().trim()
              : "Prijs op aanvraag";
            const location = locationElement.length
              ? locationElement.text().trim()
              : "";

            // Extract property type from URL
            const urlParts = url.split("/");
            const propertyIndex = urlParts.indexOf("huur") + 1;
            let propertyType = "woning";
            if (
              propertyIndex < urlParts.length &&
              ["appartement", "huis", "parkeergelegenheid", "kamer"].includes(
                urlParts[propertyIndex]
              )
            ) {
              propertyType = urlParts[propertyIndex];
            }

            // Extract additional details if available
            let details = {};
            const detailsElement = $(element).find(
              'ul[data-test-id="kenmerken-highlightedkeypoints"]'
            );
            if (detailsElement.length) {
              detailsElement.find("li").each((i, detail) => {
                const detailText = $(detail).text().trim();
                if (detailText.includes("m²")) {
                  details.size = detailText;
                } else if (detailText.includes("slaapkamer")) {
                  details.bedrooms = detailText;
                }
              });
            }

            // Create listing object
            const listingData = {
              title: title || `${propertyType} in ${location}`,
              url,
              location,
              propertyType,
              price,
              source: "funda.nl",
              ...details,
              dateAdded: new Date(),
            };

            console.log(
              `Found listing via HTML: ${listingData.title} - ${listingData.price} - ${listingData.location}`
            );
            listings.push(listingData);
          }
        } catch (err) {
          console.error("Error processing listing element:", err);
        }
      });

      // If we still have no listings, try a more generic approach with all links
      if (listings.length === 0) {
        console.log("Trying generic link extraction approach...");
        $('a[href*="detail/huur"]').each((index, element) => {
          try {
            const url = $(element).attr("href");
            if (!url) return;

            const fullUrl = url.startsWith("http")
              ? url
              : "https://www.funda.nl" + url;

            // Skip duplicates
            const isDuplicate = listings.some(
              (listing) => listing.url === fullUrl
            );
            if (isDuplicate) return;

            const urlParts = fullUrl.split("/");
            const huurIndex = urlParts.indexOf("huur");

            // Extract property type and city
            let propertyType = "woning";
            let city = "";

            if (huurIndex !== -1) {
              if (
                huurIndex + 1 < urlParts.length &&
                ["appartement", "huis", "parkeergelegenheid", "kamer"].includes(
                  urlParts[huurIndex + 1]
                )
              ) {
                propertyType = urlParts[huurIndex + 1];
              }

              if (huurIndex + 2 < urlParts.length) {
                city = urlParts[huurIndex + 2].replace(/-/g, " ");
              }
            }

            // Try to find price nearby in the DOM
            let price = "Prijs op aanvraag";
            const parentEl = $(element).closest("li, div, article");
            if (parentEl.length) {
              const priceText = parentEl.text();
              const priceMatch = priceText.match(
                /(€\s?[\d.,]+\s?[\w\s]*\/?\s?\w+|op\s+aanvraag)/i
              );
              if (priceMatch) {
                price = priceMatch[1].trim();
              }
            }

            listings.push({
              title: `${propertyType} in ${city}`,
              url: fullUrl,
              location: city,
              propertyType,
              price,
              source: "funda.nl",
              dateAdded: new Date(),
            });
          } catch (err) {
            console.error("Error processing listing link:", err);
          }
        });
      }
    }

    // Note: Don't close browser here since we're using a pool
    console.log(
      `Successfully extracted ${listings.length} listings from Funda`
    );

    if (listings.length > 0) {
      // Validate and save listings to database
      for (const rawListingData of listings) {
        const listingData = validateAndNormalizeListing(rawListingData);
        if (!listingData) {
          console.log(
            `Funda: Skipping invalid listing: ${
              rawListingData.title || "Unknown"
            }`
          );
          continue;
        }

        console.log(
          `Funda: Processing listing: ${listingData.title} at ${listingData.url}`
        );
        try {
          const newListing = new Listing(listingData);
          await newListing.save();
          console.log(`Funda: Saved listing: ${newListing.title}`);
          listingsSaved++;
          sendAlerts(newListing);
        } catch (error) {
          if (error.code === 11000) {
            // Duplicate key error
            console.log(
              `Funda: Skipping duplicate listing: ${listingData.title}`
            );
            duplicatesSkipped++;
          } else {
            console.error(
              `Funda: Error saving listing ${listingData.title}:`,
              error
            );
          }
        }
      }
    } else {
      console.log("No listings found on Funda");
    }

    scrapingMetrics.recordScrapeSuccess(
      listings.length,
      listingsSaved,
      duplicatesSkipped
    );
    console.log(
      `Funda scraping completed. Saved: ${listingsSaved}, Duplicates: ${duplicatesSkipped}`
    );
    return listings;
  } catch (error) {
    console.error(
      `Error during Funda scraping (attempt ${retryCount + 1}/${
        maxRetries + 1
      }):`,
      error
    );

    // Record failure only on first attempt
    if (retryCount === 0) {
      scrapingMetrics.recordScrapeFailure(error);
    }

    // Note: Don't close browser here since we're using a pool

    // Retry logic for transient errors
    if (retryCount < maxRetries && isRetryableError(error)) {
      console.log(
        `Retrying Funda scraping in ${(retryCount + 1) * 5} seconds...`
      );
      await new Promise((resolve) =>
        setTimeout(resolve, (retryCount + 1) * 5000)
      );
      return scrapeFunda(retryCount + 1, maxRetries);
    }

    console.log(
      `Funda scraping failed after ${
        maxRetries + 1
      } attempts. Saved: ${listingsSaved}, Duplicates: ${duplicatesSkipped}`
    );
    return [];
  }
};

// Helper function to fetch detailed listing information
const fetchListingDetails = async (browser, url) => {
  let detailPage = null;
  try {
    detailPage = await browser.newPage();
    await setupPageStealth(detailPage);

    await detailPage.goto(url, {
      waitUntil: "networkidle2",
      timeout: 30000,
    });

    // Wait for content to load
    await new Promise((r) => setTimeout(r, 2000));

    const detailHtml = await detailPage.content();
    const $ = cheerio.load(detailHtml);

    let price = "Prijs op aanvraag";
    let size = null;
    let bedrooms = null;

    // Try to extract price from various selectors first
    const priceSelectors = [
      '[data-test-id="price-rent"]',
      '[data-test-id="price"]',
      ".object-header__price",
      ".price-label",
      ".fd-m-price",
      ".object-header__price-label",
      ".object-price",
      '[class*="price"]',
    ];

    for (const selector of priceSelectors) {
      const priceElement = $(selector);
      if (priceElement.length && priceElement.text().trim()) {
        price = priceElement.text().trim();
        console.log(`Found price with selector ${selector}: ${price}`);
        break;
      }
    }

    // If no price found with selectors, try regex patterns on the HTML content
    if (price === "Prijs op aanvraag") {
      const pricePatterns = [
        /€\s*[\d.,]+\s*per\s*maand/gi,
        /€\s*[\d.,]+\s*\/\s*maand/gi,
        /€\s*[\d.,]+\s*p\.m\./gi,
        /huurprijs[:\s]*€\s*[\d.,]+/gi,
      ];

      for (const pattern of pricePatterns) {
        const matches = detailHtml.match(pattern);
        if (matches && matches.length > 0) {
          price = matches[0].trim();
          console.log(`Found price with pattern ${pattern}: ${price}`);
          break;
        }
      }
    }

    // Try to extract price from JSON-LD data
    if (price === "Prijs op aanvraag") {
      const jsonLdScripts = $('script[type="application/ld+json"]');
      jsonLdScripts.each((i, script) => {
        try {
          const jsonData = JSON.parse($(script).html());

          // Look for price in various JSON-LD properties
          if (jsonData.offers && jsonData.offers.price) {
            price = `€ ${jsonData.offers.price}`;
            console.log(`Found price in JSON-LD offers: ${price}`);
            return false; // break out of each loop
          }

          if (
            jsonData.priceSpecification &&
            jsonData.priceSpecification.price
          ) {
            price = `€ ${jsonData.priceSpecification.price}`;
            console.log(`Found price in JSON-LD priceSpecification: ${price}`);
            return false;
          }

          // Search for price in the JSON string
          const jsonStr = JSON.stringify(jsonData);
          const priceMatch = jsonStr.match(/"price":\s*"?([^",}]+)"?/i);
          if (
            priceMatch &&
            priceMatch[1] &&
            !isNaN(parseFloat(priceMatch[1]))
          ) {
            price = `€ ${priceMatch[1]}`;
            console.log(`Found price in JSON-LD data: ${price}`);
            return false;
          }
        } catch (e) {
          // Invalid JSON, continue
        }
      });
    }

    // Extract size from various patterns
    const sizePatterns = [
      /(\d+)\s*m²/i,
      /(\d+)\s*vierkante\s*meter/i,
      /oppervlakte[:\s]*(\d+)\s*m²/i,
    ];

    for (const pattern of sizePatterns) {
      const sizeMatch = detailHtml.match(pattern);
      if (sizeMatch) {
        size = sizeMatch[1] + " m²";
        break;
      }
    }

    // Extract bedrooms
    const bedroomPatterns = [
      /(\d+)\s*slaapkamer/i,
      /(\d+)\s*bedroom/i,
      /slaapkamers[:\s]*(\d+)/i,
    ];

    for (const pattern of bedroomPatterns) {
      const bedroomMatch = detailHtml.match(pattern);
      if (bedroomMatch) {
        bedrooms = parseInt(bedroomMatch[1]);
        break;
      }
    }

    return { price, size, bedrooms };
  } catch (error) {
    console.log(`Error fetching details for ${url}:`, error.message);
    return { price: "Prijs op aanvraag", size: null, bedrooms: null };
  } finally {
    if (detailPage) {
      await detailPage.close();
    }
  }
};

// Helper function to determine if an error is retryable
const isRetryableError = (error) => {
  const retryableErrors = [
    "TimeoutError",
    "NetworkError",
    "ECONNRESET",
    "ENOTFOUND",
    "ECONNREFUSED",
    "ERR_NETWORK_CHANGED",
  ];

  return retryableErrors.some(
    (errorType) => error.message.includes(errorType) || error.name === errorType
  );
};

// Helper function to scroll the page to load all content
async function autoScroll(page) {
  await page.evaluate(async () => {
    await new Promise((resolve) => {
      let totalHeight = 0;
      const distance = 100;
      const timer = setInterval(() => {
        const scrollHeight = document.body.scrollHeight;
        window.scrollBy(0, distance);
        totalHeight += distance;

        // Add some random delay to make it look more human-like
        const randomDelay = Math.floor(Math.random() * 40) + 10;
        setTimeout(() => {}, randomDelay);

        if (totalHeight >= scrollHeight - window.innerHeight) {
          clearInterval(timer);
          resolve();
        }
      }, 100);
    });
  });
}

// Cleanup function for graceful shutdown
const cleanup = async () => {
  console.log("Cleaning up scraper resources...");
  await browserPool.closeAll();
  console.log("Scraper cleanup completed");
};

// Get scraping metrics
const getScrapingMetrics = () => {
  return scrapingMetrics.getMetrics();
};

// Huurwoningen.nl scraper
const scrapeHuurwoningen = async (retryCount = 0, maxRetries = 3) => {
  if (retryCount === 0) {
    scrapingMetrics.recordScrapeStart();
  }

  let browser = null;
  let page = null;
  let listingsSaved = 0;
  let duplicatesSkipped = 0;

  try {
    browser = await browserPool.getBrowser();
    page = await browser.newPage();

    await setupPageStealth(page);

    // Set cookies to avoid cookie banners
    await page.setCookie({
      name: "cookie_consent",
      value: "accepted",
      domain: ".huurwoningen.nl",
      httpOnly: false,
      secure: true,
    });

    const listings = [];

    // Scrape multiple cities for better coverage
    const searchUrls = [
      "https://www.huurwoningen.nl/in/amsterdam/",
      "https://www.huurwoningen.nl/in/rotterdam/",
      "https://www.huurwoningen.nl/in/den-haag/",
      "https://www.huurwoningen.nl/in/utrecht/",
      "https://www.huurwoningen.nl/in/eindhoven/",
      "https://www.huurwoningen.nl/in/groningen/",
    ];

    for (const searchUrl of searchUrls) {
      try {
        console.log(`Scraping Huurwoningen: ${searchUrl}`);

        await page.goto(searchUrl, {
          waitUntil: "networkidle2",
          timeout: 60000,
        });

        // Add random delay
        await new Promise((resolve) =>
          setTimeout(resolve, getRandomDelay(2000, 4000))
        );

        // Scroll to load more content
        await autoScroll(page);

        const html = await page.content();
        const $ = cheerio.load(html);

        // Extract listings from the page
        const cityListings = [];

        // Look for listing containers - they appear to be in a specific structure
        $("main section").each((sectionIndex, section) => {
          $(section)
            .find("div")
            .each((divIndex, element) => {
              const $element = $(element);

              // Check if this div contains a listing link
              const linkElement = $element.find('a[href*="/huren/"]').first();
              if (!linkElement.length) return;

              const href = linkElement.attr("href");
              if (!href) return;

              // Extract title from the link text or nearby heading
              let title = linkElement.text().trim();
              if (!title) {
                title = $element.find("h2, h3").first().text().trim();
              }
              if (!title) return;

              // Extract location - look for text patterns that indicate location
              let location = "";
              $element.find("*").each((i, el) => {
                const text = $(el).text().trim();
                // Look for postal code patterns (4 digits + 2 letters + city name)
                const locationMatch = text.match(
                  /(\d{4}\s*[A-Z]{2})\s+([A-Za-z\s\-]+)/
                );
                if (locationMatch && !location) {
                  location = `${locationMatch[1]} ${locationMatch[2]}`.trim();
                }
              });

              // Extract price
              let price = "";
              $element.find("*").each((i, el) => {
                const text = $(el).text().trim();
                const priceMatch = text.match(/€\s*[\d.,]+\s*per\s*maand/i);
                if (priceMatch && !price) {
                  price = priceMatch[0];
                }
              });

              // Extract additional details
              let size = null;
              let rooms = null;
              let year = null;
              let interior = null;

              $element.find("li, span, div").each((i, el) => {
                const text = $(el).text().trim();

                // Size in m²
                const sizeMatch = text.match(/(\d+)\s*m²/);
                if (sizeMatch && !size) {
                  size = `${sizeMatch[1]} m²`;
                }

                // Number of rooms/bedrooms
                const roomMatch = text.match(/(\d+)\s*kamer/);
                if (roomMatch && !rooms) {
                  rooms = `${roomMatch[1]} kamers`;
                }

                // Build year
                const yearMatch = text.match(/(19|20)\d{2}/);
                if (yearMatch && !year) {
                  year = yearMatch[0];
                }

                // Interior type
                if (text.includes("Kaal") && !interior) interior = "Kaal";
                if (text.includes("Gestoffeerd") && !interior)
                  interior = "Gestoffeerd";
                if (text.includes("Gemeubileerd") && !interior)
                  interior = "Gemeubileerd";
              });

              // Build full URL
              const url = href.startsWith("http")
                ? href
                : `https://www.huurwoningen.nl${href}`;

              // Determine property type from title
              let propertyType = "woning";
              const titleLower = title.toLowerCase();
              if (titleLower.includes("appartement"))
                propertyType = "appartement";
              else if (titleLower.includes("huis")) propertyType = "huis";
              else if (titleLower.includes("kamer")) propertyType = "kamer";
              else if (titleLower.includes("studio")) propertyType = "studio";

              if (title && url && location) {
                const listingData = validateAndNormalizeListing({
                  title,
                  price: price || "Prijs op aanvraag",
                  location,
                  url,
                  propertyType,
                  size,
                  rooms,
                  year,
                  interior,
                  source: "huurwoningen.nl",
                });

                if (listingData) {
                  console.log(
                    `Huurwoningen found: ${listingData.title} - ${listingData.price} - ${listingData.location}`
                  );
                  cityListings.push(listingData);
                }
              }
            });
        });

        listings.push(...cityListings);
        console.log(`Found ${cityListings.length} listings from ${searchUrl}`);

        // Add delay between cities
        await new Promise((resolve) =>
          setTimeout(resolve, getRandomDelay(3000, 6000))
        );
      } catch (cityError) {
        console.error(`Error scraping ${searchUrl}:`, cityError.message);
        // Continue with other cities even if one fails
      }
    }

    console.log(`Found ${listings.length} total listings from Huurwoningen.`);

    // Process listings with better error handling
    for (const listingData of listings) {
      try {
        const newListing = new Listing(listingData);
        await newListing.save();
        console.log(`Saved listing: ${newListing.title}`);
        listingsSaved++;
        sendAlerts(newListing);
      } catch (error) {
        if (error.code === 11000) {
          // Duplicate key error
          console.log(`Skipping duplicate listing: ${listingData.title}`);
          duplicatesSkipped++;
        } else {
          console.error(`Error saving listing ${listingData.title}:`, error);
        }
      }
    }

    scrapingMetrics.recordScrapeSuccess(
      listings.length,
      listingsSaved,
      duplicatesSkipped
    );
    return listings;
  } catch (error) {
    console.error(
      `Error during Huurwoningen scraping (attempt ${retryCount + 1}/${
        maxRetries + 1
      }):`,
      error
    );

    // Record failure only on first attempt
    if (retryCount === 0) {
      scrapingMetrics.recordScrapeFailure(error);
    }

    // Retry logic for transient errors
    if (retryCount < maxRetries && isRetryableError(error)) {
      console.log(
        `Retrying Huurwoningen scraping in ${(retryCount + 1) * 5} seconds...`
      );
      await new Promise((resolve) =>
        setTimeout(resolve, (retryCount + 1) * 5000)
      );
      return scrapeHuurwoningen(retryCount + 1, maxRetries);
    }

    console.log(
      `Huurwoningen scraping failed after ${
        maxRetries + 1
      } attempts. Saved: ${listingsSaved}, Duplicates: ${duplicatesSkipped}`
    );
    return [];
  } finally {
    // Close the page to free up resources
    if (page) {
      try {
        await page.close();
      } catch (closeError) {
        console.error("Error closing Huurwoningen page:", closeError);
      }
    }
    console.log(
      `Huurwoningen scraping completed. Saved: ${listingsSaved}, Duplicates: ${duplicatesSkipped}`
    );
  }
};

// Agent management methods
let agentStatus = {
  isRunning: false,
  currentTask: "idle",
  config: {
    scrapeInterval: 15 * 60 * 1000, // 15 minutes
    maxRetries: 3,
    timeout: 30000,
    activeScrapers: ["pararius", "funda", "huurwoningen"],
  },
};

const getAgentStatus = () => {
  return { ...agentStatus };
};

const startAgent = async () => {
  if (agentStatus.isRunning) {
    return { message: "Agent is already running" };
  }

  agentStatus.isRunning = true;
  agentStatus.currentTask = "starting";

  // Start the scraping process
  try {
    agentStatus.currentTask = "scraping pararius";
    await scrapePararius();

    agentStatus.currentTask = "scraping funda";
    await scrapeFunda();

    agentStatus.currentTask = "scraping huurwoningen";
    await scrapeHuurwoningen();

    agentStatus.currentTask = "idle";
    return {
      message: "Agent started successfully and completed initial scraping",
    };
  } catch (error) {
    agentStatus.isRunning = false;
    agentStatus.currentTask = "error";
    throw error;
  }
};

const stopAgent = async () => {
  if (!agentStatus.isRunning) {
    return { message: "Agent is not running" };
  }

  agentStatus.isRunning = false;
  agentStatus.currentTask = "stopping";

  // Clean up any running processes
  await cleanup();

  agentStatus.currentTask = "idle";
  return { message: "Agent stopped successfully" };
};

const updateAgentConfig = async (newConfig) => {
  // Validate the new configuration
  const validScrapers = ["pararius", "funda", "huurwoningen"];
  const validScrapersInConfig = newConfig.activeScrapers?.filter((scraper) =>
    validScrapers.includes(scraper)
  );

  if (validScrapersInConfig && validScrapersInConfig.length === 0) {
    throw new Error("At least one valid scraper must be active");
  }

  // Update the configuration
  agentStatus.config = {
    ...agentStatus.config,
    ...newConfig,
  };

  return { message: "Agent configuration updated successfully" };
};

// Export functions
module.exports = {
  scrapePararius,
  scrapeFunda,
  scrapeHuurwoningen,
  cleanup,
  getScrapingMetrics,
  getAgentStatus,
  startAgent,
  stopAgent,
  updateAgentConfig,
};
