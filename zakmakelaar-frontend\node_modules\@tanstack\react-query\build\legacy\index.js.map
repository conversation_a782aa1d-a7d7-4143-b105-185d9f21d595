{"version": 3, "sources": ["../../src/index.ts"], "sourcesContent": ["/* istanbul ignore file */\n\n// Re-export core\nexport * from '@tanstack/query-core'\n\n// React Query\nexport * from './types'\nexport { useQueries } from './useQueries'\nexport type { QueriesResults, QueriesOptions } from './useQueries'\nexport { useQuery } from './useQuery'\nexport { useSuspenseQuery } from './useSuspenseQuery'\nexport { useSuspenseInfiniteQuery } from './useSuspenseInfiniteQuery'\nexport { useSuspenseQueries } from './useSuspenseQueries'\nexport type {\n  SuspenseQueriesResults,\n  SuspenseQueriesOptions,\n} from './useSuspenseQueries'\nexport { usePrefetchQuery } from './usePrefetchQuery'\nexport { usePrefetchInfiniteQuery } from './usePrefetchInfiniteQuery'\nexport { queryOptions } from './queryOptions'\nexport type {\n  DefinedInitialDataOptions,\n  UndefinedInitialDataOptions,\n  UnusedSkipTokenOptions,\n} from './queryOptions'\nexport { infiniteQueryOptions } from './infiniteQueryOptions'\nexport type {\n  DefinedInitialDataInfiniteOptions,\n  UndefinedInitialDataInfiniteOptions,\n  UnusedSkipTokenInfiniteOptions,\n} from './infiniteQueryOptions'\nexport {\n  QueryClientContext,\n  QueryClientProvider,\n  useQueryClient,\n} from './QueryClientProvider'\nexport type { QueryClientProviderProps } from './QueryClientProvider'\nexport type { QueryErrorResetBoundaryProps } from './QueryErrorResetBoundary'\nexport { HydrationBoundary } from './HydrationBoundary'\nexport type { HydrationBoundaryProps } from './HydrationBoundary'\nexport type {\n  QueryErrorClearResetFunction,\n  QueryErrorIsResetFunction,\n  QueryErrorResetBoundaryFunction,\n  QueryErrorResetFunction,\n} from './QueryErrorResetBoundary'\nexport {\n  QueryErrorResetBoundary,\n  useQueryErrorResetBoundary,\n} from './QueryErrorResetBoundary'\nexport { useIsFetching } from './useIsFetching'\nexport { useIsMutating, useMutationState } from './useMutationState'\nexport { useMutation } from './useMutation'\nexport { mutationOptions } from './mutationOptions'\nexport { useInfiniteQuery } from './useInfiniteQuery'\nexport { useIsRestoring, IsRestoringProvider } from './IsRestoringProvider'\n"], "mappings": ";AAGA,cAAc;AAGd,cAAc;AACd,SAAS,kBAAkB;AAE3B,SAAS,gBAAgB;AACzB,SAAS,wBAAwB;AACjC,SAAS,gCAAgC;AACzC,SAAS,0BAA0B;AAKnC,SAAS,wBAAwB;AACjC,SAAS,gCAAgC;AACzC,SAAS,oBAAoB;AAM7B,SAAS,4BAA4B;AAMrC;AAAA,EACE;AAAA,EACA;AAAA,EACA;AAAA,OACK;AAGP,SAAS,yBAAyB;AAQlC;AAAA,EACE;AAAA,EACA;AAAA,OACK;AACP,SAAS,qBAAqB;AAC9B,SAAS,eAAe,wBAAwB;AAChD,SAAS,mBAAmB;AAC5B,SAAS,uBAAuB;AAChC,SAAS,wBAAwB;AACjC,SAAS,gBAAgB,2BAA2B;", "names": []}