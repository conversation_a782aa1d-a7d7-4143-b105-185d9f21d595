import React, { useState } from 'react';
import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  SafeAreaView,
  ScrollView,
  TextInput,
} from 'react-native';
import { useRouter } from 'expo-router';

// Header Component
const Header = ({ title, showBackButton = false, onBack }: {
  title: string;
  showBackButton?: boolean;
  onBack?: () => void;
}) => (
  <View style={styles.header}>
    {showBackButton ? (
      <TouchableOpacity onPress={onBack} style={styles.backButton}>
        <Text style={styles.backButtonText}>←</Text>
      </TouchableOpacity>
    ) : (
      <View style={styles.placeholder} />
    )}
    <View style={styles.headerCenter}>
      <View style={styles.logoContainer}>
        <Text style={styles.logoText}>ZM</Text>
      </View>
      <Text style={styles.headerTitle}>ZakMakelaar</Text>
    </View>
    <View style={styles.placeholder} />
  </View>
);

export default function LoginSignUpScreen() {
  const router = useRouter();
  const [isLogin, setIsLogin] = useState(true);
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");

  const handleAuth = () => {
    console.log(`Attempting to ${isLogin ? "log in" : "sign up"} with:`, {
      email,
      password,
    });
    router.push('/preferences');
  };

  return (
    <SafeAreaView style={styles.container}>
      <Header
        title="Login/Sign Up"
        showBackButton={true}
        onBack={() => router.back()}
      />
      <ScrollView style={styles.scrollContainer} contentContainerStyle={styles.loginContainer}>
        <View style={styles.logoContainer}>
          <Text style={styles.logoText}>ZM</Text>
        </View>
        <Text style={styles.headerTitle}>ZakMakelaar</Text>
        
        <Text style={styles.loginTitle}>Nice to see you!</Text>
        <Text style={styles.loginSubtitle}>
          {isLogin
            ? "Log in and discover the galactically good possibilities"
            : "Create an account to unlock all features"}
        </Text>

        <View style={styles.formContainer}>
          <Text style={styles.inputLabel}>E-Mail</Text>
          <TextInput
            style={styles.textInput}
            placeholder="<EMAIL>"
            value={email}
            onChangeText={setEmail}
            keyboardType="email-address"
            autoCapitalize="none"
          />

          <Text style={styles.inputLabel}>Password</Text>
          <TextInput
            style={styles.textInput}
            placeholder="************"
            value={password}
            onChangeText={setPassword}
            secureTextEntry
          />
          
          {!isLogin && (
            <Text style={styles.passwordHint}>
              Password must be at least 6 characters.
            </Text>
          )}
          
          {isLogin && (
            <TouchableOpacity style={styles.forgotPassword}>
              <Text style={styles.forgotPasswordText}>Forgot password?</Text>
            </TouchableOpacity>
          )}

          <TouchableOpacity 
            style={styles.primaryButton}
            onPress={handleAuth}
          >
            <Text style={styles.primaryButtonText}>
              {isLogin ? "Continue" : "Sign Up"} →
            </Text>
          </TouchableOpacity>
        </View>

        <View style={styles.dividerContainer}>
          <View style={styles.dividerLine} />
          <Text style={styles.dividerText}>
            Or {isLogin ? "log in" : "sign up"} with
          </Text>
          <View style={styles.dividerLine} />
        </View>

        <TouchableOpacity style={styles.googleButton}>
          <Text style={styles.googleButtonText}>Google</Text>
        </TouchableOpacity>

        <View style={styles.switchAuthContainer}>
          <Text style={styles.switchAuthText}>
            {isLogin ? "Don't have an account yet?" : "Already have an account?"}{" "}
          </Text>
          <TouchableOpacity onPress={() => setIsLogin(!isLogin)}>
            <Text style={styles.switchAuthLink}>
              {isLogin ? "Create one here!" : "Log in here!"}
            </Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f3f4f6',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    width: '100%',
    padding: 16,
    backgroundColor: '#ffffff',
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
  },
  backButton: {
    padding: 8,
  },
  backButtonText: {
    fontSize: 20,
    color: '#f72585',
  },
  placeholder: {
    width: 24,
  },
  headerCenter: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  logoContainer: {
    width: 32,
    height: 32,
    backgroundColor: '#f72585',
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  logoText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#ffffff',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1f2937',
  },
  scrollContainer: {
    flex: 1,
  },
  loginContainer: {
    alignItems: 'center',
    padding: 32,
    backgroundColor: '#ffffff',
    margin: 16,
    marginTop: 0,
    borderBottomLeftRadius: 16,
    borderBottomRightRadius: 16,
  },
  loginTitle: {
    fontSize: 24,
    fontWeight: '600',
    color: '#1f2937',
    marginBottom: 16,
    marginTop: 16,
  },
  loginSubtitle: {
    fontSize: 16,
    color: '#6b7280',
    textAlign: 'center',
    marginBottom: 32,
    lineHeight: 22,
  },
  formContainer: {
    width: '100%',
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#374151',
    marginBottom: 8,
    marginTop: 16,
  },
  textInput: {
    width: '100%',
    padding: 12,
    borderWidth: 1,
    borderColor: '#e5e7eb',
    borderRadius: 12,
    fontSize: 16,
    color: '#374151',
    backgroundColor: '#ffffff',
  },
  passwordHint: {
    fontSize: 12,
    color: '#6b7280',
    marginTop: 4,
  },
  forgotPassword: {
    alignSelf: 'flex-end',
    marginTop: 8,
  },
  forgotPasswordText: {
    fontSize: 14,
    color: '#6b7280',
  },
  primaryButton: {
    width: '100%',
    backgroundColor: '#f72585',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 12,
    alignItems: 'center',
    marginTop: 24,
    marginBottom: 24,
  },
  primaryButtonText: {
    color: '#ffffff',
    fontSize: 18,
    fontWeight: '600',
  },
  dividerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    width: '100%',
    marginBottom: 24,
  },
  dividerLine: {
    flex: 1,
    height: 1,
    backgroundColor: '#d1d5db',
  },
  dividerText: {
    fontSize: 14,
    color: '#6b7280',
    paddingHorizontal: 8,
    backgroundColor: '#ffffff',
  },
  googleButton: {
    width: '100%',
    backgroundColor: '#f3f4f6',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 12,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#e5e7eb',
    marginBottom: 32,
  },
  googleButtonText: {
    color: '#374151',
    fontSize: 18,
    fontWeight: '600',
  },
  switchAuthContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  switchAuthText: {
    fontSize: 16,
    color: '#6b7280',
  },
  switchAuthLink: {
    fontSize: 16,
    color: '#f72585',
    fontWeight: '500',
  },
});
