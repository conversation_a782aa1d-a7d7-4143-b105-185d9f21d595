const express = require("express");
const scraperController = require("../controllers/scraperController");
const { scrapingLimiter } = require("../middleware/rateLimiter");

const router = express.Router();

/**
 * @swagger
 * components:
 *   schemas:
 *     ScrapeResponse:
 *       type: object
 *       properties:
 *         status:
 *           type: string
 *           example: success
 *         message:
 *           type: string
 *           description: Status message about the scraping operation
 *         data:
 *           type: object
 *           properties:
 *             newListings:
 *               type: number
 *               description: Number of new listings found
 *             totalProcessed:
 *               type: number
 *               description: Total number of listings processed
 *             duplicatesSkipped:
 *               type: number
 *               description: Number of duplicate listings skipped
 *             duration:
 *               type: string
 *               description: Time taken for the scraping operation
 *               example: 15.2s
 *             sites:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   name:
 *                     type: string
 *                     description: Name of the scraped site
 *                   status:
 *                     type: string
 *                     description: Scraping status for this site
 *                   listings:
 *                     type: number
 *                     description: Number of listings found on this site
 *
 * tags:
 *   name: Scraper
 *   description: Web scraping operations for property listings
 */

/**
 * @swagger
 * /api/scrape:
 *   post:
 *     summary: Manually trigger the web scraper
 *     description: Triggers manual scraping of property listings from configured real estate websites. This operation is rate-limited to prevent abuse.
 *     tags: [Scraper]
 *     responses:
 *       200:
 *         description: Scraping completed successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ScrapeResponse'
 *       429:
 *         description: Too many scraping requests - rate limit exceeded
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: error
 *                 message:
 *                   type: string
 *                   example: Too many scraping requests. Please try again later.
 *       500:
 *         description: An error occurred while scraping
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: error
 *                 message:
 *                   type: string
 *                   example: Scraping failed
 *                 error:
 *                   type: string
 *                   description: Detailed error message
 */
router.post(
  "/scrape",
  scrapingLimiter,
  (req, res, next) => {
    console.log("[/api/scrape] route hit. Request body:", req.body);
    next();
  },
  (req, res, next) => scraperController.scrape(req, res, next)
);

/**
 * @swagger
 * /api/scraper/metrics:
 *   get:
 *     summary: Get scraping performance metrics
 *     description: Returns detailed metrics about scraping performance, success rates, and error statistics
 *     tags: [Scraper]
 *     responses:
 *       200:
 *         description: Scraping metrics retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 data:
 *                   type: object
 *                   properties:
 *                     metrics:
 *                       type: object
 *                       properties:
 *                         totalScrapes:
 *                           type: number
 *                           description: Total number of scraping attempts
 *                         successfulScrapes:
 *                           type: number
 *                           description: Number of successful scraping operations
 *                         failedScrapes:
 *                           type: number
 *                           description: Number of failed scraping operations
 *                         successRate:
 *                           type: string
 *                           description: Success rate percentage
 *                         totalListingsFound:
 *                           type: number
 *                           description: Total listings discovered
 *                         totalListingsSaved:
 *                           type: number
 *                           description: Total listings successfully saved
 *                         duplicatesSkipped:
 *                           type: number
 *                           description: Number of duplicate listings skipped
 *                         averageScrapingTimeFormatted:
 *                           type: string
 *                           description: Average time per scraping operation
 *                         lastScrapeTime:
 *                           type: string
 *                           format: date-time
 *                           description: Timestamp of last scraping operation
 */
router.get("/metrics", scraperController.getMetrics);

module.exports = router;
