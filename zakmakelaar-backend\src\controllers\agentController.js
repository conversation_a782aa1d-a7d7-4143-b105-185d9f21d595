// Debug logging
console.log('Initializing agent controller...');

const { catchAsync } = require('../middleware/errorHandler');

// Import utils with error handling
let utils;
try {
  utils = require('../utils/agentUtils');
  console.log('Agent utils loaded successfully');
} catch (error) {
  console.error('Error loading agent utils:', error);
  throw error;
}

const { 
  formatAgentStatus, 
  validateAgentConfig,
  calculateAutonomyLevel,
  calculateAutonomyRate 
} = utils;

// Directly import the scraper service
let scraperService;
try {
  scraperService = require('../services/scraper');
  console.log('Scraper service loaded successfully');
} catch (error) {
  console.error('Error loading scraper service:', error);
  throw error;
}

// Get current agent status
const getAgentStatus = catchAsync(async (req, res) => {
  const status = scraperService.getAgentStatus();
  const metrics = scraperService.getScrapingMetrics();
  
  const formattedStatus = formatAgentStatus(
    metrics,
    status.isRunning,
    status.currentTask
  );
  
  res.status(200).json({
    status: 'success',
    data: formattedStatus,
  });
});

// Start the agent
const startAgent = catchAsync(async (req, res) => {
  const result = await scraperService.startAgent();
  const status = scraperService.getAgentStatus();
  const metrics = scraperService.getScrapingMetrics();
  
  const formattedStatus = formatAgentStatus(
    metrics,
    status.isRunning,
    status.currentTask
  );
  
  res.status(200).json({
    status: 'success',
    message: result.message,
    data: formattedStatus,
  });
});

// Stop the agent
const stopAgent = catchAsync(async (req, res) => {
  const result = await scraperService.stopAgent();
  const status = scraperService.getAgentStatus();
  const metrics = scraperService.getScrapingMetrics();
  
  const formattedStatus = formatAgentStatus(
    metrics,
    status.isRunning,
    status.currentTask
  );
  
  res.status(200).json({
    status: 'success',
    message: result.message,
    data: formattedStatus,
  });
});

// Update agent configuration
const updateAgentConfig = catchAsync(async (req, res) => {
  const { config } = req.body;
  
  if (!config) {
    return res.status(400).json({
      status: 'error',
      message: 'No configuration provided',
    });
  }
  
  // Validate the configuration
  const { valid, errors } = validateAgentConfig(config);
  if (!valid) {
    return res.status(400).json({
      status: 'error',
      message: 'Invalid configuration',
      errors,
    });
  }
  
  // Update the configuration
  const result = await scraperService.updateAgentConfig(config);
  const status = scraperService.getAgentStatus();
  const metrics = scraperService.getScrapingMetrics();
  
  const formattedStatus = formatAgentStatus(
    metrics,
    status.isRunning,
    status.currentTask
  );
  
  res.status(200).json({
    status: 'success',
    message: result.message,
    data: formattedStatus,
  });
});

// Get agent metrics
const getAgentMetrics = catchAsync(async (req, res) => {
  const metrics = scraperService.getScrapingMetrics();
  
  res.status(200).json({
    status: 'success',
    data: {
      ...metrics,
      autonomyLevel: calculateAutonomyLevel(metrics),
      autonomyRate: calculateAutonomyRate(metrics),
    },
  });
});

// Health check endpoint
const healthCheck = catchAsync(async (req, res) => {
  const status = scraperService.getAgentStatus();
  const metrics = scraperService.getScrapingMetrics();
  
  const autonomyLevel = calculateAutonomyLevel(metrics);
  const autonomyRate = calculateAutonomyRate(metrics);
  
  res.status(200).json({
    status: 'success',
    data: {
      status: status.isRunning ? 'active' : 'inactive',
      uptime: process.uptime(),
      timestamp: new Date().toISOString(),
      metrics: {
        ...metrics,
        autonomyLevel,
        autonomyRate,
      },
    },
  });
});

// Export all controller functions
module.exports = {
  getAgentStatus,
  startAgent,
  stopAgent,
  updateAgentConfig,
  getAgentMetrics,
  healthCheck
};