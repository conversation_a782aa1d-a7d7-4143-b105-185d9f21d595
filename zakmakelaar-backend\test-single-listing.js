const http = require('http');

// First get a listing ID, then test the single listing endpoint
async function testSingleListing() {
  try {
    // First, get a listing ID from the listings endpoint
    console.log('🔍 Getting listing ID from listings endpoint...');
    
    const listingId = await new Promise((resolve, reject) => {
      const options = {
        hostname: 'localhost',
        port: 3000,
        path: '/api/listings?limit=1',
        method: 'GET',
        headers: { 'Content-Type': 'application/json' }
      };

      const req = http.request(options, (res) => {
        let data = '';
        res.on('data', (chunk) => { data += chunk; });
        res.on('end', () => {
          try {
            const response = JSON.parse(data);
            if (response.data && response.data.listings && response.data.listings.length > 0) {
              const firstListing = response.data.listings[0];
              console.log(`✅ Found listing: ${firstListing.title} (ID: ${firstListing._id})`);
              resolve(firstListing._id);
            } else {
              reject(new Error('No listings found'));
            }
          } catch (error) {
            reject(error);
          }
        });
      });

      req.on('error', reject);
      req.end();
    });

    // Now test the single listing endpoint
    console.log(`\n🧪 Testing single listing endpoint: /api/listings/${listingId}`);
    
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: `/api/listings/${listingId}`,
      method: 'GET',
      headers: { 'Content-Type': 'application/json' }
    };

    const req = http.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => { data += chunk; });
      res.on('end', () => {
        try {
          console.log('🔍 Single Listing API Response Status:', res.statusCode);
          
          if (res.statusCode !== 200) {
            console.log('❌ Non-200 status code');
            console.log('Raw response:', data);
            return;
          }

          const response = JSON.parse(data);
          console.log('📊 Response Structure:');
          console.log('Status:', response.status);
          
          if (response.data && response.data.listing) {
            const listing = response.data.listing;
            console.log('\n📋 Listing Details:');
            console.log(`Title: ${listing.title}`);
            console.log(`ID: ${listing._id}`);
            console.log(`Price: ${listing.price} (${typeof listing.price})`);
            console.log(`Location: ${listing.location} (${typeof listing.location})`);
            console.log(`Rooms: ${listing.rooms} (${typeof listing.rooms})`);
            console.log(`Size: ${listing.size} (${typeof listing.size})`);
            console.log(`Property Type: ${listing.propertyType}`);
            console.log(`Interior: ${listing.interior}`);
            console.log(`Year: ${listing.year}`);
            console.log(`Description: ${listing.description ? listing.description.substring(0, 100) + '...' : 'None'}`);
            console.log(`Images: ${listing.images ? listing.images.length : 0}`);
            console.log(`Source: ${listing.source}`);
            console.log(`Date Added: ${listing.dateAdded}`);
          } else {
            console.log('❌ No listing data in response');
            console.log('Response structure:', Object.keys(response));
          }
        } catch (error) {
          console.error('❌ Error parsing response:', error);
          console.log('Raw response:', data);
        }
      });
    });

    req.on('error', (error) => {
      console.error('❌ API request failed:', error);
    });

    req.end();

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

testSingleListing();
