const mongoose = require('mongoose');
const Listing = require('./src/models/Listing');

// Connect to MongoDB
mongoose.connect('mongodb://localhost:27017/zakmakelaar', {
  useNewUrlParser: true,
  useUnifiedTopology: true,
});

const sampleListings = [
  {
    title: "Modern Apartment in Amsterdam Center",
    price: "€ 2,500",
    location: "Amsterdam, Noord-Holland",
    url: "https://example.com/listing1",
    size: "85 m²",
    bedrooms: "2",
    rooms: "3",
    propertyType: "Apartment",
    description: "Beautiful modern apartment in the heart of Amsterdam with great amenities.",
    year: "2018",
    interior: "Gestoffeerd",
    source: "funda.nl"
  },
  {
    title: "Spacious House in Utrecht",
    price: "€ 3,200",
    location: "Utrecht, Utrecht",
    url: "https://example.com/listing2",
    size: "120 m²",
    bedrooms: "3",
    rooms: "5",
    propertyType: "House",
    description: "Family-friendly house with garden in quiet neighborhood.",
    year: "2015",
    interior: "Gemeubileerd",
    source: "pararius.nl"
  },
  {
    title: "Cozy Studio in Rotterdam",
    price: "€ 1,800",
    location: "Rotterdam, Zuid-Holland",
    url: "https://example.com/listing3",
    size: "45 m²",
    bedrooms: "1",
    rooms: "2",
    propertyType: "Studio",
    description: "Perfect studio for young professionals near public transport.",
    year: "2020",
    interior: "Kaal",
    source: "kamernet.nl"
  },
  {
    title: "Luxury Penthouse in The Hague",
    price: "€ 4,500",
    location: "Den Haag, Zuid-Holland",
    url: "https://example.com/listing4",
    size: "150 m²",
    bedrooms: "3",
    rooms: "6",
    propertyType: "Penthouse",
    description: "Stunning penthouse with panoramic city views and rooftop terrace.",
    year: "2019",
    interior: "Gemeubileerd",
    source: "funda.nl"
  },
  {
    title: "Student Room in Groningen",
    price: "€ 650",
    location: "Groningen, Groningen",
    url: "https://example.com/listing5",
    size: "20 m²",
    bedrooms: "1",
    rooms: "1",
    propertyType: "Room",
    description: "Affordable student room near university campus.",
    year: "1995",
    interior: "Gestoffeerd",
    source: "kamernet.nl"
  },
  {
    title: "Family Home in Eindhoven",
    price: "€ 2,800",
    location: "Eindhoven, Noord-Brabant",
    url: "https://example.com/listing6",
    size: "110 m²",
    bedrooms: "4",
    rooms: "6",
    propertyType: "House",
    description: "Comfortable family home with large garden and parking.",
    year: "2010",
    interior: "Gestoffeerd",
    source: "pararius.nl"
  },
  {
    title: "Waterfront Apartment in Almere",
    price: "€ 2,200",
    location: "Almere, Flevoland",
    url: "https://example.com/listing7",
    size: "75 m²",
    bedrooms: "2",
    rooms: "3",
    propertyType: "Apartment",
    description: "Beautiful apartment with water views and modern finishes.",
    year: "2017",
    interior: "Gestoffeerd",
    source: "funda.nl"
  },
  {
    title: "Historic Canal House in Leiden",
    price: "€ 3,800",
    location: "Leiden, Zuid-Holland",
    url: "https://example.com/listing8",
    size: "140 m²",
    bedrooms: "3",
    rooms: "7",
    propertyType: "House",
    description: "Charming historic house along the famous Leiden canals.",
    year: "1650",
    interior: "Gemeubileerd",
    source: "pararius.nl"
  }
];

async function createSampleListings() {
  try {
    console.log('🏠 Creating sample listings...');
    
    // Clear existing listings
    await Listing.deleteMany({});
    console.log('🗑️ Cleared existing listings');
    
    // Insert sample listings
    const createdListings = await Listing.insertMany(sampleListings);
    console.log(`✅ Created ${createdListings.length} sample listings`);
    
    // Verify creation
    const count = await Listing.countDocuments();
    console.log(`📊 Total listings in database: ${count}`);
    
    // Show first few listings
    const firstFew = await Listing.find().limit(3);
    console.log('\n📋 Sample listings created:');
    firstFew.forEach((listing, index) => {
      console.log(`${index + 1}. ${listing.title} - ${listing.price} in ${listing.location}`);
    });
    
    console.log('\n🎉 Sample data creation completed!');
    
  } catch (error) {
    console.error('❌ Error creating sample listings:', error);
  } finally {
    mongoose.connection.close();
  }
}

createSampleListings();
