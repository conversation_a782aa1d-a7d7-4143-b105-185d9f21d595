import React, { useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, ScrollView } from 'react-native';
import { useListingsStore } from '../store/listingsStore';
import { listingsService } from '../services/listingsService';

export default function ListingsDebugTest() {
  const [results, setResults] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const { recentListings, fetchRecentListings } = useListingsStore();

  const addResult = (result: string) => {
    setResults(prev => [...prev, result]);
    console.log(result);
  };

  const testListingsData = async () => {
    setIsLoading(true);
    setResults([]);
    
    try {
      addResult('🔍 Testing Listings Data...');
      
      // Test 1: Direct API call
      addResult('📡 Testing direct API call...');
      try {
        const directResponse = await listingsService.getListings({ limit: 5 });
        addResult(`✅ Direct API: ${directResponse.success ? 'Success' : 'Failed'}`);
        if (directResponse.success && directResponse.data) {
          addResult(`📊 Direct listings count: ${directResponse.data.listings.length}`);
          if (directResponse.data.listings.length > 0) {
            const firstListing = directResponse.data.listings[0];
            addResult(`🏠 First listing: ${firstListing.title}`);
            addResult(`💰 Price: ${firstListing.price} (${typeof firstListing.price})`);
            addResult(`📍 Location: ${firstListing.location?.city || 'N/A'}`);
            addResult(`🏠 Rooms: ${firstListing.rooms || 'N/A'}`);
            addResult(`📸 Images: ${firstListing.images?.length || 0}`);
          }
        } else {
          addResult(`❌ Direct API error: ${directResponse.message}`);
        }
      } catch (error: any) {
        addResult(`❌ Direct API exception: ${error.message}`);
      }
      
      // Test 2: Store method
      addResult('🏪 Testing store method...');
      await fetchRecentListings(5);
      addResult(`📊 Store listings count: ${recentListings.length}`);
      
      if (recentListings.length > 0) {
        const firstListing = recentListings[0];
        addResult(`🏠 Store first listing: ${firstListing.title}`);
        addResult(`💰 Store price: ${firstListing.price} (${typeof firstListing.price})`);
        addResult(`📍 Store location: ${firstListing.location?.city || 'N/A'}`);
      }
      
      // Test 3: Test price formatting
      addResult('💰 Testing price formatting...');
      const testPrices = [1500, '2000', undefined, null, 'invalid', 0];
      testPrices.forEach((price, index) => {
        try {
          const formatted = listingsService.formatPrice(price as number);
          addResult(`Price ${index + 1}: ${price} → ${formatted}`);
        } catch (error: any) {
          addResult(`Price ${index + 1}: ${price} → ERROR: ${error.message}`);
        }
      });
      
      addResult('🎉 Listings debug test completed!');
      
    } catch (error: any) {
      addResult('💥 Test failed with exception');
      addResult(`Error: ${error.message}`);
      console.error('Listings debug test error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const clearResults = () => {
    setResults([]);
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Listings Debug Test</Text>
      
      <View style={styles.statusContainer}>
        <Text style={styles.statusText}>
          Current Listings: {recentListings.length}
        </Text>
        <Text style={styles.statusText}>
          First Listing: {recentListings[0]?.title || 'None'}
        </Text>
        <Text style={styles.statusText}>
          First Price: {recentListings[0]?.price || 'None'} ({typeof recentListings[0]?.price})
        </Text>
      </View>
      
      <View style={styles.buttonContainer}>
        <TouchableOpacity 
          style={[styles.button, styles.primaryButton, isLoading && styles.buttonDisabled]} 
          onPress={testListingsData}
          disabled={isLoading}
        >
          <Text style={styles.buttonText}>
            {isLoading ? 'Testing...' : 'Test Listings Data'}
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={[styles.button, styles.clearButton]} 
          onPress={clearResults}
          disabled={isLoading}
        >
          <Text style={styles.buttonText}>Clear Results</Text>
        </TouchableOpacity>
      </View>
      
      <ScrollView style={styles.resultsContainer}>
        {results.map((result, index) => (
          <Text key={index} style={styles.resultText}>
            {result}
          </Text>
        ))}
        {results.length === 0 && (
          <Text style={styles.emptyText}>
            No results yet. Tap "Test Listings Data" to start.
          </Text>
        )}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#f3f4f6',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 16,
    textAlign: 'center',
  },
  statusContainer: {
    backgroundColor: '#ffffff',
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
  },
  statusText: {
    fontSize: 14,
    color: '#374151',
    marginBottom: 4,
  },
  buttonContainer: {
    flexDirection: 'row',
    gap: 8,
    marginBottom: 16,
  },
  button: {
    flex: 1,
    paddingVertical: 10,
    paddingHorizontal: 12,
    borderRadius: 6,
    alignItems: 'center',
  },
  primaryButton: {
    backgroundColor: '#f72585',
  },
  clearButton: {
    backgroundColor: '#6b7280',
  },
  buttonDisabled: {
    backgroundColor: '#9ca3af',
  },
  buttonText: {
    color: '#ffffff',
    fontSize: 14,
    fontWeight: '600',
  },
  resultsContainer: {
    flex: 1,
    backgroundColor: '#ffffff',
    borderRadius: 8,
    padding: 16,
  },
  resultText: {
    fontSize: 13,
    color: '#374151',
    marginBottom: 6,
    fontFamily: 'monospace',
  },
  emptyText: {
    fontSize: 14,
    color: '#6b7280',
    fontStyle: 'italic',
    textAlign: 'center',
    marginTop: 20,
  },
});
