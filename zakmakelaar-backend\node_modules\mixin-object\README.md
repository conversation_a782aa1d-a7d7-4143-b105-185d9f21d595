# mixin-object [![NPM version](https://badge.fury.io/js/mixin-object.svg)](http://badge.fury.io/js/mixin-object)

> Mixin the own and inherited properties of other objects onto the first object. Pass an empty object as the first arg to shallow clone.

If you only want to combine own-properties, use [extend-shallow](https://github.com/jonschlinkert/extend-shallow).

## Install

Install with [npm](https://www.npmjs.com/)

```sh
$ npm i mixin-object --save
```

Install with [bower](http://bower.io/)

```sh
$ bower install mixin-object --save
```

## Usage

```js
var mixin = require('mixin-object');

var obj = {c: 'c'};
var foo = mixin({a: 'a'}, {b: 'b'});
console.log(foo);
//=> {c: 'c', a: 'a', b: 'b'}
console.log(obj);
//=> {c: 'c'}

mixin({}, {a: 'a'}, {b: 'b'});
//=> {a: 'a', b: 'b'}
```

## Related

* [assign-deep](https://github.com/jonschlinkert/assign-deep): Deeply assign the enumerable properties of source objects to a destination object.
* [defaults-deep](https://github.com/jonschlinkert/defaults-deep): Like `extend` but recursively copies only the missing properties/values to the target object.
* [extend-shallow](https://github.com/jonschlinkert/extend-shallow): Extend an object with the properties of additional objects. node.js/javascript util.
* [for-own](https://github.com/jonschlinkert/for-own): Iterate over the own enumerable properties of an object, and return an object with properties… [more](https://github.com/jonschlinkert/for-own)
* [for-in](https://github.com/jonschlinkert/for-in): Iterate over the own and inherited enumerable properties of an objecte, and return an object… [more](https://github.com/jonschlinkert/for-in)
* [isobject](https://github.com/jonschlinkert/isobject): Returns true if the value is an object and not an array or null.
* [is-plain-object](https://github.com/jonschlinkert/is-plain-object): Returns true if an object was created by the `Object` constructor.
* [mixin-deep](https://github.com/jonschlinkert/mixin-deep): Deeply mix the properties of objects into the first object. Like merge-deep, but doesn't clone.
* [merge-deep](https://github.com/jonschlinkert/merge-deep): Recursively merge values in a javascript object.

## Running tests

Install dev dependencies:

```sh
$ npm i -d && npm test
```

## Contributing

Pull requests and stars are always welcome. For bugs and feature requests, [please create an issue](https://github.com/jonschlinkert/mixin-object/issues/new)

## Author

**Jon Schlinkert**

+ [github/jonschlinkert](https://github.com/jonschlinkert)
+ [twitter/jonschlinkert](http://twitter.com/jonschlinkert)

## License

Copyright © 2014-2015 [Jon Schlinkert](https://github.com/jonschlinkert)
Released under the MIT license.

***

_This file was generated by [verb-cli](https://github.com/assemble/verb-cli) on July 05, 2015._