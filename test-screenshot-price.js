// Test script specifically for the screenshot price format
const price = '€3,950 per month';

function extractPrice(priceString) {
  console.log(`Testing price: "${priceString}"`);
  
  // Clean up HTML entities, non-breaking spaces, and extra content
  let normalizedPrice = priceString
    .replace(/&nbsp;/g, " ")
    .replace(/\u00A0/g, " ") // Non-breaking space character (160)
    .replace(/\s+/g, " ")
    .trim();

  // Extract only the price part (before any newlines or extra content)
  const priceLineMatch = normalizedPrice.match(/^([^\\n]+)/);
  if (priceLineMatch) {
    normalizedPrice = priceLineMatch[1].trim();
  }

  // Extract numeric value and format consistently
  const priceMatch = normalizedPrice.match(/€\s*([\d.,]+)/);
  if (priceMatch) {
    // Handle European number formats where comma can be thousands separator
    let extractedPrice = priceMatch[1].trim();
    
    console.log(`Extracted: ${extractedPrice}`);
    
    let numericPrice;
    
    // Special case for the format in the screenshot: €3,950 (treat as 3950, not 3.95)
    if (extractedPrice.match(/^\d{1,3},\d{3}$/) && !normalizedPrice.includes('.')) {
      console.log('Format detected: Special case - comma as thousands separator');
      // This is the format from the screenshot - comma is a thousands separator
      numericPrice = parseFloat(extractedPrice.replace(/,/g, ''));
    } else {
      console.log('Format detected: Other format');
      numericPrice = parseFloat(extractedPrice.replace(',', '.'));
    }
    
    console.log(`Numeric: ${numericPrice}`);
    
    if (numericPrice > 0) {
      const formattedPrice = `€ ${Math.round(numericPrice).toLocaleString('nl-NL')} per maand`;
      console.log(`Result: ${formattedPrice}`);
      return formattedPrice;
    }
  }
  
  console.log("Failed to extract price");
  return "Prijs op aanvraag";
}

// Test the screenshot price format
extractPrice(price);
