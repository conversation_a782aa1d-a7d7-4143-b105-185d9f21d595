// Test script for price extraction with the fixed logic
const testPrices = [
  '€3,950 per month',
  '€ 4 per maand',
  '€3.950 per maand',
  '€3,950',
  '€ 3,950 per month',
  '€ 3.950,00 per maand'
];

function extractPrice(priceString) {
  console.log(`\nTesting price: "${priceString}"`);
  
  // Clean up HTML entities, non-breaking spaces, and extra content
  let normalizedPrice = priceString
    .replace(/&nbsp;/g, " ")
    .replace(/\u00A0/g, " ") // Non-breaking space character (160)
    .replace(/\s+/g, " ")
    .trim();

  // Extract only the price part (before any newlines or extra content)
  const priceLineMatch = normalizedPrice.match(/^([^\\n]+)/);
  if (priceLineMatch) {
    normalizedPrice = priceLineMatch[1].trim();
  }

  // Handle common Dutch price formats
  if (
    normalizedPrice.toLowerCase().includes("op aanvraag") ||
    normalizedPrice.toLowerCase().includes("on request")
  ) {
    normalizedPrice = "Prijs op aanvraag";
    console.log(`Result: ${normalizedPrice}`);
    return normalizedPrice;
  } else {
    // Extract numeric value and format consistently
    const priceMatch = normalizedPrice.match(/€\s*([\d.,]+)/);
    if (priceMatch) {
      // Handle European number formats where comma can be thousands separator
      let extractedPrice = priceMatch[1].trim();
      
      console.log(`Extracted: ${extractedPrice}`);
      
      // Determine if this is likely a price in thousands (e.g., 3,950)
      const isLikelyThousands = extractedPrice.match(/\d{1,3}[,.]\d{3}/);
      console.log(`Is likely thousands format: ${!!isLikelyThousands}`);
      
      let numericPrice;
      if (isLikelyThousands) {
        // For formats like 3,950 or 3.950 (thousands separator)
        numericPrice = parseFloat(extractedPrice.replace(/[.,]/g, ''));
      } else {
        // For regular decimal formats
        numericPrice = parseFloat(extractedPrice.replace(',', '.'));
      }
      
      console.log(`Numeric: ${numericPrice}`);
      
      if (numericPrice > 0) {
        const formattedPrice = `€ ${Math.round(numericPrice).toLocaleString('nl-NL')} per maand`;
        console.log(`Result: ${formattedPrice}`);
        return formattedPrice;
      }
    }
  }
  
  console.log("Failed to extract price");
  return "Prijs op aanvraag";
}

// Test all price formats
testPrices.forEach(price => extractPrice(price));
