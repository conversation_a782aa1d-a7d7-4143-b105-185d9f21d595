import { apiService, ApiResponse } from './api';

// Auth types
export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterData {
  email: string;
  password: string;
  firstName?: string;
  lastName?: string;
}

export interface User {
  _id: string;
  email: string;
  firstName?: string;
  lastName?: string;
  role: 'user' | 'admin';
  preferences?: UserPreferences;
  createdAt: string;
  updatedAt: string;
}

export interface UserPreferences {
  maxRent?: number;
  minRooms?: number;
  maxRooms?: number;
  preferredLocations?: string[];
  propertyTypes?: string[];
  amenities?: string[];
  notifications?: {
    email: boolean;
    push: boolean;
    sms: boolean;
  };
}

export interface AuthResponse {
  user: User;
  token: string;
  refreshToken?: string;
}

export interface UpdatePreferencesData {
  maxRent?: number;
  minRooms?: number;
  maxRooms?: number;
  preferredLocations?: string[];
  propertyTypes?: string[];
  amenities?: string[];
  notifications?: {
    email?: boolean;
    push?: boolean;
    sms?: boolean;
  };
}

class AuthService {
  /**
   * Login user with email and password
   */
  async login(credentials: LoginCredentials): Promise<ApiResponse<AuthResponse>> {
    try {
      const response = await apiService.post<AuthResponse>('/auth/login', credentials);
      
      if (response.success && response.data) {
        // Save auth data to secure storage
        await apiService.saveAuthData(
          response.data.token,
          response.data.refreshToken,
          response.data.user
        );
      }
      
      return response;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Register new user
   */
  async register(userData: RegisterData): Promise<ApiResponse<AuthResponse>> {
    try {
      const response = await apiService.post<AuthResponse>('/auth/register', userData);
      
      if (response.success && response.data) {
        // Save auth data to secure storage
        await apiService.saveAuthData(
          response.data.token,
          response.data.refreshToken,
          response.data.user
        );
      }
      
      return response;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Logout user
   */
  async logout(): Promise<void> {
    try {
      // Call logout endpoint to invalidate token on server
      await apiService.post('/auth/logout');
    } catch (error) {
      // Continue with logout even if server call fails
      console.warn('Logout API call failed:', error);
    } finally {
      // Always clear local auth data
      await apiService.clearAuthData();
    }
  }

  /**
   * Get current user profile
   */
  async getCurrentUser(): Promise<ApiResponse<User>> {
    try {
      return await apiService.get<User>('/auth/me');
    } catch (error) {
      throw error;
    }
  }

  /**
   * Update user preferences
   */
  async updatePreferences(preferences: UpdatePreferencesData): Promise<ApiResponse<User>> {
    try {
      const response = await apiService.put<User>('/auth/preferences', preferences);
      
      if (response.success && response.data) {
        // Update cached user data
        await apiService.saveAuthData(
          await apiService.getAuthToken() || '',
          undefined,
          response.data
        );
      }
      
      return response;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Get user preferences
   */
  async getPreferences(): Promise<ApiResponse<UserPreferences>> {
    try {
      return await apiService.get<UserPreferences>('/auth/preferences');
    } catch (error) {
      throw error;
    }
  }

  /**
   * Update user profile
   */
  async updateProfile(profileData: Partial<User>): Promise<ApiResponse<User>> {
    try {
      const response = await apiService.put<User>('/auth/profile', profileData);
      
      if (response.success && response.data) {
        // Update cached user data
        await apiService.saveAuthData(
          await apiService.getAuthToken() || '',
          undefined,
          response.data
        );
      }
      
      return response;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Change password
   */
  async changePassword(currentPassword: string, newPassword: string): Promise<ApiResponse> {
    try {
      return await apiService.put('/auth/change-password', {
        currentPassword,
        newPassword,
      });
    } catch (error) {
      throw error;
    }
  }

  /**
   * Request password reset
   */
  async requestPasswordReset(email: string): Promise<ApiResponse> {
    try {
      return await apiService.post('/auth/forgot-password', { email });
    } catch (error) {
      throw error;
    }
  }

  /**
   * Reset password with token
   */
  async resetPassword(token: string, newPassword: string): Promise<ApiResponse> {
    try {
      return await apiService.post('/auth/reset-password', {
        token,
        newPassword,
      });
    } catch (error) {
      throw error;
    }
  }

  /**
   * Check if user is authenticated
   */
  async isAuthenticated(): Promise<boolean> {
    return await apiService.isAuthenticated();
  }

  /**
   * Get cached user data
   */
  async getCachedUser(): Promise<User | null> {
    return await apiService.getUserData();
  }

  /**
   * Refresh authentication token
   */
  async refreshToken(): Promise<boolean> {
    try {
      // This is handled automatically by the API service interceptors
      // Just check if we're still authenticated after any refresh attempts
      return await this.isAuthenticated();
    } catch (error) {
      console.error('Token refresh failed:', error);
      return false;
    }
  }

  /**
   * Validate email format
   */
  validateEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Validate password strength
   */
  validatePassword(password: string): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    if (password.length < 6) {
      errors.push('Password must be at least 6 characters long');
    }
    
    if (!/[A-Z]/.test(password)) {
      errors.push('Password must contain at least one uppercase letter');
    }
    
    if (!/[a-z]/.test(password)) {
      errors.push('Password must contain at least one lowercase letter');
    }
    
    if (!/\d/.test(password)) {
      errors.push('Password must contain at least one number');
    }
    
    return {
      isValid: errors.length === 0,
      errors,
    };
  }
}

// Export singleton instance
export const authService = new AuthService();
export default authService;
