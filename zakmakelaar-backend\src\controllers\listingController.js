const Listing = require("../models/Listing");
const { catchAsync, AppError } = require("../middleware/errorHandler");
const searchService = require("../services/searchService");

exports.getListings = catchAsync(async (req, res, next) => {
  // Use advanced search service
  const searchResult = await searchService.search(req.query);

  res.status(200).json({
    status: "success",
    results: searchResult.listings.length,
    pagination: searchResult.pagination,
    searchParams: searchResult.searchParams,
    performance: searchResult.performance,
    data: {
      listings: searchResult.listings,
    },
  });
});

exports.getListingById = catchAsync(async (req, res, next) => {
  const listing = await Listing.findById(req.params.id);

  if (!listing) {
    return next(new AppError("No listing found with that ID", 404));
  }

  res.status(200).json({
    status: "success",
    data: {
      listing,
    },
  });
});

// Get search suggestions
exports.getSearchSuggestions = catchAsync(async (req, res, next) => {
  const { q, type = "location" } = req.query;

  if (!q) {
    return next(new AppError("Query parameter 'q' is required", 400));
  }

  const suggestions = await searchService.getSearchSuggestions(q, type);

  res.status(200).json({
    status: "success",
    results: suggestions.length,
    data: {
      suggestions,
    },
  });
});

// Get search statistics
exports.getSearchStats = catchAsync(async (req, res, next) => {
  const stats = await searchService.getSearchStats();

  res.status(200).json({
    status: "success",
    data: {
      stats,
    },
  });
});
