[2025-07-14T20:29:50.886Z] Starting Funda scraper test with file logging...
[2025-07-14T20:29:50.887Z] Launching browser with stealth options...
[2025-07-14T20:29:51.224Z] Browser launched successfully
[2025-07-14T20:29:51.224Z] Testing URL: https://www.funda.nl/huur/amsterdam/appartement-88667170-van-ostadestraat-446-3/
[2025-07-14T20:29:51.225Z] Creating verification page...
[2025-07-14T20:29:51.290Z] Navigating to test URL to verify accessibility...
[2025-07-14T20:29:53.331Z] Verification screenshot saved to C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\tests\funda-verification-screenshot.png
[2025-07-14T20:29:53.335Z] Verification HTML saved to C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\tests\funda-verification-page.html
[2025-07-14T20:29:53.336Z] Page title: "Pagina niet gevonden - funda"
[2025-07-14T20:29:53.337Z] WARNING: Page not found or access blocked by Funda
[2025-07-14T20:29:53.351Z] 
--- Testing fetchListingDetails function ---
[2025-07-14T20:29:53.352Z] Calling fetchListingDetails...
[2025-07-14T20:30:00.294Z] fetchListingDetails completed in 6.941 seconds
[2025-07-14T20:30:00.304Z] Listing details saved to C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\tests\funda-scraper-result.json
[2025-07-14T20:30:00.313Z] 
=== EXTRACTED LISTING DETAILS ===
[2025-07-14T20:30:00.315Z] Price: Prijs op aanvraag
[2025-07-14T20:30:00.318Z] Size: Not found
[2025-07-14T20:30:00.321Z] Rooms: Not found
[2025-07-14T20:30:00.322Z] Bedrooms: Not found
[2025-07-14T20:30:00.323Z] 
All available fields:
[2025-07-14T20:30:00.324Z] - price: Prijs op aanvraag
[2025-07-14T20:30:00.325Z] 
WARNING: Failed to extract meaningful listing details
[2025-07-14T20:30:00.326Z] This might be due to anti-scraping measures or changes in the website structure
[2025-07-14T20:30:00.655Z] Browser closed
[2025-07-14T20:30:00.656Z] Test execution completed
