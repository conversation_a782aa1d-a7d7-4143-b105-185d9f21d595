import React, { useEffect, useState } from "react";
import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  SafeAreaView,
  ScrollView,
  Image,
  ActivityIndicator,
  RefreshControl,
  Alert,
  TextInput,
  Dimensions,
} from "react-native";
import { useRouter } from "expo-router";
import { useAuthStore } from "../store/authStore";
import { useListingsStore } from "../store/listingsStore";
import { listingsService } from "../services/listingsService";
import { Ionicons, MaterialIcons } from "@expo/vector-icons";
import { useSafeAreaInsets } from "react-native-safe-area-context";

const { width } = Dimensions.get("window");

// Header Component
const Header = ({ title }: { title: string }) => {
  const insets = useSafeAreaInsets();

  return (
    <View style={[styles.header, { paddingTop: Math.max(insets.top, 16) }]}>
      <View style={styles.headerCenter}>
        <View style={styles.logoContainer}>
          <Text style={styles.logoText}>ZM</Text>
        </View>
        <Text style={styles.headerTitle}>ZakMakelaar</Text>
      </View>
    </View>
  );
};

export default function DashboardScreen() {
  const router = useRouter();
  const { user, isAuthenticated, logout } = useAuthStore();
  const {
    listings,
    recentListings,
    isLoading,
    error,
    fetchRecentListings,
    clearError,
    searchListings,
    totalCount,
    searchQuery: storeSearchQuery,
  } = useListingsStore();

  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [quickStats, setQuickStats] = useState({
    totalListings: 0,
    averagePrice: 0,
    newToday: 0,
  });

  // Get the correct listings to display based on search state
  const displayListings = searchQuery.trim() ? listings : recentListings;

  // Debug logging
  if (__DEV__) {
    console.log("📋 Display state:", {
      searchQuery: searchQuery.trim(),
      hasSearchQuery: !!searchQuery.trim(),
      listingsCount: listings.length,
      recentListingsCount: recentListings.length,
      displayListingsCount: displayListings.length,
      isLoading,
    });
  }

  // Redirect if not authenticated
  useEffect(() => {
    if (!isAuthenticated) {
      router.replace("/login");
    }
  }, [isAuthenticated]);

  // Load initial data
  useEffect(() => {
    if (isAuthenticated) {
      loadDashboardData();
    }
  }, [isAuthenticated]);

  const loadDashboardData = async () => {
    try {
      console.log("🏠 Loading dashboard data...");
      await fetchRecentListings(10);
      console.log("📊 Recent listings loaded:", recentListings.length);

      // Load stats after listings are loaded
      setTimeout(() => {
        loadQuickStats();
      }, 100);

      console.log("✅ Dashboard data loaded successfully");
    } catch (error) {
      console.error("❌ Failed to load dashboard data:", error);
    }
  };

  const loadQuickStats = async () => {
    try {
      console.log("📊 Loading quick stats...");
      const statsResponse = await listingsService.getListingsStats();
      console.log("📈 Stats response:", statsResponse);

      if (statsResponse.success && statsResponse.data) {
        // Calculate new today from recent listings (more reliable)
        const today = new Date().toDateString();
        const newTodayCount = recentListings.filter((listing) => {
          const listingDate = new Date(listing.dateAdded).toDateString();
          return today === listingDate;
        }).length;

        // Handle the correct response structure from /search/stats
        const stats = statsResponse.data.stats || statsResponse.data;

        // Calculate average price from recent listings if not provided by API
        let avgPrice = stats.avgPrice || 0;
        if (!avgPrice && recentListings.length > 0) {
          const prices = recentListings
            .map((listing) => {
              const priceStr = listing.price?.toString() || "";
              // Match numbers with commas (e.g., "1,500" or "2,800") and dots (e.g., "1.500")
              const match = priceStr.match(/(\d{1,3}(?:[,\.]\d{3})*(?:\.\d{2})?)/);
              if (match) {
                // Remove commas and convert to number
                const cleanPrice = match[1].replace(/,/g, '');
                return parseFloat(cleanPrice);
              }
              return 0;
            })
            .filter((price) => price > 0);

          if (prices.length > 0) {
            avgPrice =
              prices.reduce((sum, price) => sum + price, 0) / prices.length;
          }
        }

        setQuickStats({
          totalListings: stats.totalListings || recentListings.length,
          averagePrice: avgPrice,
          newToday: newTodayCount,
        });

        console.log("✅ Quick stats loaded:", {
          total: stats.totalListings,
          avgPrice: stats.avgPrice,
          newToday: newTodayCount,
        });
      } else {
        // Fallback to basic stats from recent listings
        console.log("⚠️ Using fallback stats from recent listings");
        const today = new Date().toDateString();
        const newTodayCount = recentListings.filter((listing) => {
          const listingDate = new Date(listing.dateAdded).toDateString();
          return today === listingDate;
        }).length;

        setQuickStats({
          totalListings: recentListings.length,
          averagePrice: 0,
          newToday: newTodayCount,
        });
      }
    } catch (error) {
      console.warn("❌ Failed to load stats:", error);
      // Fallback stats
      const today = new Date().toDateString();
      const newTodayCount = recentListings.filter((listing) => {
        const listingDate = new Date(listing.dateAdded).toDateString();
        return today === listingDate;
      }).length;

      setQuickStats({
        totalListings: recentListings.length,
        averagePrice: 0,
        newToday: newTodayCount,
      });
    }
  };

  const handleSearch = async () => {
    if (searchQuery.trim()) {
      try {
        console.log("🔍 Searching for:", searchQuery.trim());
        await searchListings(searchQuery.trim());
        console.log("📊 Search results:", listings.length);
      } catch (error) {
        console.error("Search failed:", error);
      }
    } else {
      console.log("🔄 Clearing search, loading recent listings");
      await fetchRecentListings(10);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      await loadDashboardData();
    } finally {
      setRefreshing(false);
    }
  };

  const handleLogout = async () => {
    Alert.alert("Logout", "Are you sure you want to logout?", [
      { text: "Cancel", style: "cancel" },
      {
        text: "Logout",
        style: "destructive",
        onPress: async () => {
          await logout();
          router.replace("/");
        },
      },
    ]);
  };

  const formatPrice = (price: number | string | undefined) => {
    if (price === undefined || price === null || price === "") {
      return "€ --";
    }

    let numPrice: number;

    if (typeof price === "string") {
      // Remove all non-numeric characters except dots and commas
      const cleanPrice = price.replace(/[^\d.,]/g, "");
      // Replace comma with dot for decimal parsing
      const normalizedPrice = cleanPrice.replace(",", ".");
      numPrice = parseFloat(normalizedPrice);
    } else {
      numPrice = price;
    }

    if (isNaN(numPrice) || numPrice <= 0) {
      return "€ --";
    }

    return listingsService.formatPrice(numPrice);
  };

  const getListingImage = (listing: any) => {
    if (listing.images && listing.images.length > 0) {
      return { uri: listing.images[0] };
    }
    return { uri: "https://placehold.co/300x200/e0e0e0/333333?text=No+Image" };
  };

  return (
    <SafeAreaView style={styles.container}>
      <Header
        title={`Welcome${user?.firstName ? `, ${user.firstName}` : ""}!`}
      />
      <ScrollView
        style={styles.scrollContainer}
        contentContainerStyle={styles.dashboardContainer}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
        }
      >
        {/* Welcome Section */}
        <View style={styles.welcomeContainer}>
          <View style={styles.welcomeTextContainer}>
            <Text style={styles.welcomeText}>
              Welcome back{user?.firstName ? `, ${user.firstName}` : ""}!
            </Text>
            <Text style={styles.welcomeSubtext}>
              Find your perfect home today
            </Text>
          </View>
        </View>

        {/* Search Bar */}
        <View style={styles.searchContainer}>
          <TextInput
            style={styles.searchInput}
            placeholder="Search for properties..."
            value={searchQuery}
            onChangeText={setSearchQuery}
            onSubmitEditing={handleSearch}
            returnKeyType="search"
          />
          <TouchableOpacity onPress={handleSearch} style={styles.searchButton}>
            <Ionicons name="search" size={20} color="#ffffff" />
          </TouchableOpacity>
        </View>

        {/* Quick Stats */}
        <View style={styles.statsContainer}>
          <View style={styles.statCard}>
            <Text style={styles.statNumber}>{quickStats.totalListings}</Text>
            <Text style={styles.statLabel}>Total Listings</Text>
          </View>
          <View style={styles.statCard}>
            <Text style={styles.statNumber}>
              {quickStats.averagePrice > 0
                ? `€${Math.round(quickStats.averagePrice)}`
                : "-"}
            </Text>
            <Text style={styles.statLabel}>Avg Price</Text>
          </View>
          <View style={styles.statCard}>
            <Text style={styles.statNumber}>{quickStats.newToday}</Text>
            <Text style={styles.statLabel}>New Today</Text>
          </View>
        </View>

        {/* Quick Actions */}
        <View style={styles.quickActionsContainer}>
          <TouchableOpacity
            style={styles.quickActionButton}
            onPress={() => router.push("/preferences")}
          >
            <Ionicons name="settings-outline" size={24} color="#6b7280" />
            <Text style={styles.quickActionText}>Preferences</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.quickActionButton}
            onPress={() => router.push("/application")}
          >
            <Ionicons name="document-text-outline" size={24} color="#6b7280" />
            <Text style={styles.quickActionText}>Apply</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.quickActionButton}
            onPress={() => router.push("/contract-review")}
          >
            <Ionicons name="clipboard-outline" size={24} color="#6b7280" />
            <Text style={styles.quickActionText}>Review</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.quickActionButton}
            onPress={() => {
              // Navigate to saved listings
              router.push("/dashboard"); // For now, just refresh
              handleRefresh();
            }}
          >
            <Ionicons name="heart-outline" size={24} color="#6b7280" />
            <Text style={styles.quickActionText}>Saved</Text>
          </TouchableOpacity>
        </View>

        {/* Error Display */}
        {error && (
          <View style={styles.errorContainer}>
            <Text style={styles.errorText}>{error}</Text>
            <TouchableOpacity onPress={clearError} style={styles.errorButton}>
              <Text style={styles.errorButtonText}>Dismiss</Text>
            </TouchableOpacity>
          </View>
        )}

        {/* Loading State */}
        {isLoading && displayListings.length === 0 && (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#f72585" />
            <Text style={styles.loadingText}>Loading listings...</Text>
          </View>
        )}

        {/* Empty State */}
        {!isLoading && displayListings.length === 0 && !error && (
          <View style={styles.emptyContainer}>
            <Ionicons name="home-outline" size={64} color="#d1d5db" />
            <Text style={styles.emptyTitle}>No listings found</Text>
            <Text style={styles.emptyText}>
              We couldn&apos;t find any listings at the moment. Try refreshing
              or check back later.
            </Text>
            <TouchableOpacity
              onPress={handleRefresh}
              style={styles.refreshButton}
            >
              <Ionicons
                name="refresh"
                size={16}
                color="#ffffff"
                style={{ marginRight: 8 }}
              />
              <Text style={styles.refreshButtonText}>Refresh</Text>
            </TouchableOpacity>
          </View>
        )}

        {/* Section Title */}
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>
            {searchQuery ? `Search Results` : "Recent Listings"}
          </Text>
          {searchQuery && (
            <TouchableOpacity
              onPress={() => {
                setSearchQuery("");
                fetchRecentListings(10);
              }}
              style={styles.clearSearchButton}
            >
              <Text style={styles.clearSearchText}>Clear</Text>
            </TouchableOpacity>
          )}
        </View>

        {/* Listings Grid */}
        {displayListings.length > 0 && (
          <View style={styles.listingsContainer}>
            {displayListings.map((listing) => {
              // Debug logging
              if (__DEV__) {
                console.log("🏠 Rendering listing:", {
                  id: listing._id,
                  title: listing.title,
                  price: listing.price,
                  priceType: typeof listing.price,
                  location:
                    typeof listing.location === "string"
                      ? listing.location
                      : listing.location?.city,
                  rooms: listing.bedrooms || listing.rooms,
                });
              }

              return (
                <TouchableOpacity
                  key={listing._id}
                  style={styles.listingCard}
                  onPress={() =>
                    router.push(`/listing-details?id=${listing._id}`)
                  }
                >
                  <View style={styles.listingImageContainer}>
                    <Image
                      source={getListingImage(listing)}
                      style={styles.listingImage}
                      defaultSource={{
                        uri: "https://placehold.co/300x200/e0e0e0/333333?text=Loading...",
                      }}
                    />
                    <TouchableOpacity
                      style={styles.favoriteButton}
                      onPress={(e) => {
                        e.stopPropagation();
                        // TODO: Implement favorite functionality
                        Alert.alert(
                          "Favorite",
                          "Favorite functionality coming soon!"
                        );
                      }}
                    >
                      <Ionicons
                        name="heart-outline"
                        size={20}
                        color="#374151"
                      />
                    </TouchableOpacity>
                  </View>
                  <View style={styles.listingContent}>
                    <View style={styles.listingHeader}>
                      <Text style={styles.listingTitle} numberOfLines={2}>
                        {listing.title}
                      </Text>
                      <Text style={styles.listingPrice}>
                        {formatPrice(listing.price)}
                      </Text>
                    </View>
                    <View style={styles.listingFooter}>
                      <View style={styles.listingDetailsContainer}>
                        <Ionicons
                          name="location-outline"
                          size={14}
                          color="#6b7280"
                        />
                        <Text style={styles.listingDetails} numberOfLines={1}>
                          {typeof listing.location === "string"
                            ? listing.location
                            : listing.location?.city || "Unknown location"}{" "}
                          • {listing.rooms || listing.bedrooms || "N/A"} rooms
                        </Text>
                      </View>
                      <Text style={styles.listingSource}>
                        via {listing.source}
                      </Text>
                    </View>
                  </View>
                </TouchableOpacity>
              );
            })}
          </View>
        )}
      </ScrollView>

      {/* Bottom Navigation */}
      <View style={styles.bottomNavigation}>
        <TouchableOpacity
          onPress={() => router.push("/dashboard")}
          style={styles.navButton}
        >
          <Ionicons name="home" size={24} color="#f72585" />
          <Text style={[styles.navLabel, styles.navLabelActive]}>Home</Text>
        </TouchableOpacity>
        <TouchableOpacity
          onPress={() => router.push("/application")}
          style={styles.navButton}
        >
          <Ionicons name="document-text-outline" size={24} color="#6b7280" />
          <Text style={styles.navLabel}>Apply</Text>
        </TouchableOpacity>
        <TouchableOpacity
          onPress={() => router.push("/contract-review")}
          style={styles.navButton}
        >
          <MaterialIcons name="gavel" size={24} color="#6b7280" />
          <Text style={styles.navLabel}>Contract</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={styles.navButton}
          onPress={() => router.push("/profile")}
        >
          <Ionicons name="person-outline" size={24} color="#6b7280" />
          <Text style={styles.navLabel}>Profile</Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f3f4f6",
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    width: "100%",
    paddingHorizontal: 16,
    paddingBottom: 16,
    backgroundColor: "#ffffff",
    borderBottomWidth: 1,
    borderBottomColor: "#e5e7eb",
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
  },
  headerCenter: {
    flexDirection: "row",
    alignItems: "center",
  },
  logoContainer: {
    width: 32,
    height: 32,
    backgroundColor: "#f72585",
    borderRadius: 8,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 8,
  },
  logoText: {
    fontSize: 16,
    fontWeight: "bold",
    color: "#ffffff",
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: "bold",
    color: "#1f2937",
  },
  scrollContainer: {
    flex: 1,
  },
  dashboardContainer: {
    padding: 24,
    backgroundColor: "#ffffff",
    margin: 0,
    marginTop: 0,
    borderBottomLeftRadius: 16,
    borderBottomRightRadius: 16,
  },
  dashboardTitle: {
    fontSize: 24,
    fontWeight: "600",
    color: "#1f2937",
    marginBottom: 24,
  },
  notificationContainer: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#dbeafe",
    borderWidth: 1,
    borderColor: "#93c5fd",
    padding: 16,
    borderRadius: 12,
    marginBottom: 24,
  },
  notificationIcon: {
    fontSize: 16,
    marginRight: 12,
  },
  notificationText: {
    fontSize: 14,
    color: "#1e40af",
    flex: 1,
  },
  listingsContainer: {
    gap: 24,
  },
  listingCard: {
    backgroundColor: "#ffffff",
    borderRadius: 16,
    overflow: "hidden",
    borderWidth: 1,
    borderColor: "#e5e7eb",
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  listingImageContainer: {
    position: "relative",
  },
  listingImage: {
    width: "100%",
    height: 180,
  },
  favoriteButton: {
    position: "absolute",
    top: 12,
    right: 12,
    backgroundColor: "rgba(255, 255, 255, 0.9)",
    borderRadius: 20,
    width: 36,
    height: 36,
    justifyContent: "center",
    alignItems: "center",
  },

  listingContent: {
    padding: 16,
  },
  listingHeader: {
    marginBottom: 12,
  },
  listingTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#1f2937",
    marginBottom: 8,
    lineHeight: 24,
  },
  listingPrice: {
    fontSize: 22,
    fontWeight: "bold",
    color: "#f72585",
  },
  listingFooter: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  listingDetailsContainer: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
    gap: 4,
  },
  listingDetails: {
    fontSize: 14,
    color: "#6b7280",
    flex: 1,
  },
  listingSource: {
    fontSize: 12,
    color: "#9ca3af",
    fontStyle: "italic",
  },
  // New styles for dashboard updates
  titleContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 24,
  },
  logoutButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    backgroundColor: "#f3f4f6",
    borderRadius: 6,
  },
  logoutText: {
    fontSize: 14,
    color: "#6b7280",
  },
  errorContainer: {
    backgroundColor: "#fef2f2",
    borderColor: "#fecaca",
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  errorText: {
    color: "#dc2626",
    fontSize: 14,
    flex: 1,
  },
  errorButton: {
    paddingHorizontal: 8,
    paddingVertical: 4,
  },
  errorButtonText: {
    color: "#dc2626",
    fontSize: 12,
    fontWeight: "500",
  },
  loadingContainer: {
    alignItems: "center",
    justifyContent: "center",
    padding: 40,
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: "#6b7280",
  },
  emptyContainer: {
    alignItems: "center",
    justifyContent: "center",
    padding: 40,
  },
  emptyIcon: {
    fontSize: 48,
    marginBottom: 16,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#1f2937",
    marginBottom: 8,
  },
  emptyText: {
    fontSize: 14,
    color: "#6b7280",
    textAlign: "center",
    marginBottom: 16,
    lineHeight: 20,
  },
  refreshButton: {
    backgroundColor: "#f72585",
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 6,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
  },
  refreshButtonText: {
    color: "#ffffff",
    fontSize: 14,
    fontWeight: "500",
  },
  bottomNavigation: {
    flexDirection: "row",
    justifyContent: "space-around",
    padding: 16,
    backgroundColor: "#ffffff",
    borderTopWidth: 1,
    borderTopColor: "#e5e7eb",
    borderBottomLeftRadius: 16,
    borderBottomRightRadius: 16,
  },
  navButton: {
    alignItems: "center",
  },
  navIcon: {
    fontSize: 20,
    color: "#6b7280",
    marginBottom: 4,
  },
  navIconActive: {
    color: "#f72585",
  },
  navLabel: {
    fontSize: 12,
    color: "#6b7280",
  },
  navLabelActive: {
    color: "#f72585",
  },
  // Enhanced home screen styles
  welcomeContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 24,
    paddingHorizontal: 4,
  },
  welcomeTextContainer: {
    flex: 1,
  },
  welcomeText: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#1f2937",
    marginBottom: 4,
  },
  welcomeSubtext: {
    fontSize: 16,
    color: "#6b7280",
  },
  searchContainer: {
    flexDirection: "row",
    marginBottom: 24,
    gap: 12,
  },
  searchInput: {
    flex: 1,
    backgroundColor: "#ffffff",
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    borderWidth: 1,
    borderColor: "#e5e7eb",
  },
  searchButton: {
    backgroundColor: "#f72585",
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    justifyContent: "center",
    alignItems: "center",
  },

  statsContainer: {
    flexDirection: "row",
    marginBottom: 24,
    gap: 12,
  },
  statCard: {
    flex: 1,
    backgroundColor: "#ffffff",
    borderRadius: 12,
    padding: 16,
    alignItems: "center",
    borderWidth: 1,
    borderColor: "#e5e7eb",
  },
  statNumber: {
    fontSize: 20,
    fontWeight: "bold",
    color: "#f72585",
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: "#6b7280",
    textAlign: "center",
  },
  sectionHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: "600",
    color: "#1f2937",
  },
  clearSearchButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    backgroundColor: "#f3f4f6",
    borderRadius: 6,
  },
  clearSearchText: {
    fontSize: 14,
    color: "#6b7280",
  },
  quickActionsContainer: {
    flexDirection: "row",
    marginBottom: 24,
    gap: 12,
  },
  quickActionButton: {
    flex: 1,
    backgroundColor: "#ffffff",
    borderRadius: 12,
    padding: 16,
    alignItems: "center",
    borderWidth: 1,
    borderColor: "#e5e7eb",
    gap: 8,
  },

  quickActionText: {
    fontSize: 12,
    color: "#374151",
    fontWeight: "500",
  },
  fab: {
    position: "absolute",
    bottom: 100,
    right: 20,
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: "#f72585",
    justifyContent: "center",
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 4.65,
    elevation: 8,
  },
});
