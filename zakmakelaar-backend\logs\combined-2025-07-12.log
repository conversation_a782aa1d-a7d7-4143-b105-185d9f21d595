{
  service: 'zakmakelaar-api',
  port: '3000',
  environment: 'development',
  endpoints: {
    api: 'http://localhost:3000',
    docs: 'http://localhost:3000/api-docs',
    health: 'http://localhost:3000/health'
  },
  level: 'info',
  message: '🚀 ZakMakelaar API Server Started',
  timestamp: '2025-07-12 12:13:20'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 12:15:00'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 12:15:10',
  level: 'info',
  category: 'ai_operation',
  operation: 'listing_matching',
  status: 'success',
  details: 80,
  environment: 'development',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 12:15:10',
  level: 'info',
  category: 'ai_operation',
  operation: 'listing_matching',
  status: 'success',
  details: 80,
  environment: 'development',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 12:15:10',
  level: 'info',
  category: 'ai_operation',
  operation: 'listing_matching',
  status: 'success',
  details: 80,
  environment: 'development',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 12:15:10',
  level: 'info',
  category: 'ai_operation',
  operation: 'listing_matching',
  status: 'success',
  details: 80,
  environment: 'development',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 12:15:10',
  level: 'info',
  category: 'ai_operation',
  operation: 'listing_matching',
  status: 'success',
  details: 80,
  environment: 'development',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 12:15:10',
  level: 'info',
  category: 'ai_operation',
  operation: 'listing_matching',
  status: 'success',
  details: 80,
  environment: 'development',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 12:15:10',
  level: 'info',
  category: 'ai_operation',
  operation: 'listing_matching',
  status: 'success',
  details: 80,
  environment: 'development',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 12:15:10',
  level: 'info',
  category: 'ai_operation',
  operation: 'listing_matching',
  status: 'success',
  details: 80,
  environment: 'development',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 12:15:10',
  level: 'info',
  category: 'ai_operation',
  operation: 'listing_matching',
  status: 'success',
  details: 80,
  environment: 'development',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 12:15:10',
  level: 'info',
  category: 'ai_operation',
  operation: 'listing_matching',
  status: 'success',
  details: 80,
  environment: 'development',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 12:15:10',
  level: 'info',
  category: 'ai_operation',
  operation: 'listing_matching',
  status: 'success',
  details: 80,
  environment: 'development',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 12:15:10',
  level: 'info',
  category: 'ai_operation',
  operation: 'listing_matching',
  status: 'success',
  details: 80,
  environment: 'development',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 12:15:10',
  level: 'info',
  category: 'ai_operation',
  operation: 'listing_matching',
  status: 'success',
  details: 100,
  environment: 'development',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 12:15:10',
  level: 'info',
  category: 'ai_operation',
  operation: 'listing_matching',
  status: 'success',
  details: 80,
  environment: 'development',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 12:15:10',
  level: 'info',
  category: 'ai_operation',
  operation: 'listing_matching',
  status: 'success',
  details: 90,
  environment: 'development',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 12:15:10',
  level: 'info',
  category: 'ai_operation',
  operation: 'listing_matching',
  status: 'success',
  details: 80,
  environment: 'development',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 12:15:11',
  level: 'info',
  category: 'ai_operation',
  operation: 'listing_matching',
  status: 'success',
  details: 80,
  environment: 'development',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 12:15:11',
  level: 'info',
  category: 'ai_operation',
  operation: 'listing_matching',
  status: 'success',
  details: 80,
  environment: 'development',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 12:15:11',
  level: 'info',
  category: 'ai_operation',
  operation: 'listing_matching',
  status: 'success',
  details: 80,
  environment: 'development',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 12:15:11',
  level: 'info',
  category: 'ai_operation',
  operation: 'listing_matching',
  status: 'success',
  details: 80,
  environment: 'development',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 12:15:11',
  level: 'info',
  category: 'ai_operation',
  operation: 'listing_matching',
  status: 'success',
  details: 80,
  environment: 'development',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 12:15:11',
  level: 'info',
  category: 'ai_operation',
  operation: 'listing_matching',
  status: 'success',
  details: 80,
  environment: 'development',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 12:15:11',
  level: 'info',
  category: 'ai_operation',
  operation: 'listing_matching',
  status: 'success',
  details: 80,
  environment: 'development',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 12:15:11',
  level: 'info',
  category: 'ai_operation',
  operation: 'listing_matching',
  status: 'success',
  details: 80,
  environment: 'development',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 12:15:11',
  level: 'info',
  category: 'ai_operation',
  operation: 'listing_matching',
  status: 'success',
  details: 80,
  environment: 'development',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 12:15:11',
  level: 'info',
  category: 'ai_operation',
  operation: 'listing_matching',
  status: 'success',
  details: 80,
  environment: 'development',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 12:15:11',
  level: 'info',
  category: 'ai_operation',
  operation: 'listing_matching',
  status: 'success',
  details: 80,
  environment: 'development',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 12:15:11',
  level: 'info',
  category: 'ai_operation',
  operation: 'listing_matching',
  status: 'success',
  details: 80,
  environment: 'development',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 12:15:11',
  level: 'info',
  category: 'ai_operation',
  operation: 'listing_summarization',
  status: 'success',
  details: 'english',
  environment: 'development',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 12:15:11',
  level: 'info',
  category: 'ai_operation',
  operation: 'listing_summarization',
  status: 'success',
  details: 'english',
  environment: 'development',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 12:15:11',
  level: 'info',
  category: 'ai_operation',
  operation: 'listing_matching',
  status: 'success',
  details: 80,
  environment: 'development',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 12:15:11',
  level: 'info',
  category: 'ai_operation',
  operation: 'listing_matching',
  status: 'success',
  details: 100,
  environment: 'development',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 12:15:12',
  level: 'info',
  category: 'ai_operation',
  operation: 'listing_summarization',
  status: 'success',
  details: 'english',
  environment: 'development',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 12:15:12',
  level: 'info',
  category: 'ai_operation',
  operation: 'listing_summarization',
  status: 'success',
  details: 'english',
  environment: 'development',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 12:15:12',
  level: 'info',
  category: 'ai_operation',
  operation: 'listing_summarization',
  status: 'success',
  details: 'english',
  environment: 'development',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 12:15:12',
  level: 'info',
  category: 'ai_operation',
  operation: 'listing_summarization',
  status: 'success',
  details: 'english',
  environment: 'development',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 12:15:12',
  level: 'info',
  category: 'ai_operation',
  operation: 'listing_summarization',
  status: 'success',
  details: 'english',
  environment: 'development',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 12:15:12',
  level: 'info',
  category: 'ai_operation',
  operation: 'listing_summarization',
  status: 'success',
  details: 'english',
  environment: 'development',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 12:15:12',
  level: 'info',
  category: 'ai_operation',
  operation: 'listing_summarization',
  status: 'success',
  details: 'english',
  environment: 'development',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 12:15:12',
  level: 'info',
  category: 'ai_operation',
  operation: 'listing_summarization',
  status: 'success',
  details: 'english',
  environment: 'development',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 12:15:12',
  level: 'info',
  category: 'ai_operation',
  operation: 'listing_summarization',
  status: 'success',
  details: 'english',
  environment: 'development',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 12:15:12',
  level: 'info',
  category: 'ai_operation',
  operation: 'listing_summarization',
  status: 'success',
  details: 'english',
  environment: 'development',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 12:15:12',
  level: 'info',
  category: 'ai_operation',
  operation: 'listing_summarization',
  status: 'success',
  details: 'english',
  environment: 'development',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 12:15:12',
  level: 'info',
  category: 'ai_operation',
  operation: 'listing_summarization',
  status: 'success',
  details: 'english',
  environment: 'development',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 12:15:12',
  level: 'info',
  category: 'ai_operation',
  operation: 'listing_summarization',
  status: 'success',
  details: 'english',
  environment: 'development',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 12:15:12',
  level: 'info',
  category: 'ai_operation',
  operation: 'listing_summarization',
  status: 'success',
  details: 'english',
  environment: 'development',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 12:15:12',
  level: 'info',
  category: 'ai_operation',
  operation: 'listing_summarization',
  status: 'success',
  details: 'english',
  environment: 'development',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 12:15:12',
  level: 'info',
  category: 'ai_operation',
  operation: 'listing_summarization',
  status: 'success',
  details: 'english',
  environment: 'development',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 12:15:12',
  level: 'info',
  category: 'ai_operation',
  operation: 'listing_summarization',
  status: 'success',
  details: 'english',
  environment: 'development',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 12:15:12',
  level: 'info',
  category: 'ai_operation',
  operation: 'listing_summarization',
  status: 'success',
  details: 'english',
  environment: 'development',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 12:15:12',
  level: 'info',
  category: 'ai_operation',
  operation: 'listing_summarization',
  status: 'success',
  details: 'english',
  environment: 'development',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 12:15:12',
  level: 'info',
  category: 'ai_operation',
  operation: 'listing_summarization',
  status: 'success',
  details: 'english',
  environment: 'development',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 12:15:12',
  level: 'info',
  category: 'ai_operation',
  operation: 'listing_summarization',
  status: 'success',
  details: 'english',
  environment: 'development',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 12:15:12',
  level: 'info',
  category: 'ai_operation',
  operation: 'listing_summarization',
  status: 'success',
  details: 'english',
  environment: 'development',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 12:15:13',
  level: 'info',
  category: 'ai_operation',
  operation: 'listing_summarization',
  status: 'success',
  details: 'english',
  environment: 'development',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 12:15:13',
  level: 'info',
  category: 'ai_operation',
  operation: 'listing_summarization',
  status: 'success',
  details: 'english',
  environment: 'development',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 12:15:13',
  level: 'info',
  category: 'ai_operation',
  operation: 'listing_summarization',
  status: 'success',
  details: 'english',
  environment: 'development',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 12:15:13',
  level: 'info',
  category: 'ai_operation',
  operation: 'listing_summarization',
  status: 'success',
  details: 'english',
  environment: 'development',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 12:15:13',
  level: 'info',
  category: 'ai_operation',
  operation: 'listing_summarization',
  status: 'success',
  details: 'english',
  environment: 'development',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 12:15:14',
  level: 'info',
  category: 'ai_operation',
  operation: 'market_analysis',
  status: 'success',
  details: '2285 Vv Rijswijk (Ministerbuurt)',
  environment: 'development',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 12:15:14',
  level: 'info',
  category: 'ai_operation',
  operation: 'market_analysis',
  status: 'success',
  details: '3521 Hb Utrecht (Hoog-Catharijne Ns En Jaarbeurs)',
  environment: 'development',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 12:15:14',
  level: 'info',
  category: 'ai_operation',
  operation: 'market_analysis',
  status: 'success',
  details: '1075 Hm Amsterdam (Willemspark)',
  environment: 'development',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 12:15:14',
  level: 'info',
  category: 'ai_operation',
  operation: 'market_analysis',
  status: 'success',
  details: '1082 Ev Amsterdam (Buitenveldert-West)',
  environment: 'development',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 12:15:15',
  level: 'info',
  category: 'ai_operation',
  operation: 'market_analysis',
  status: 'success',
  details: '3511 Mz Utrecht (Hooch Boulandt)',
  environment: 'development',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 12:15:15',
  level: 'info',
  category: 'ai_operation',
  operation: 'market_analysis',
  status: 'success',
  details: '1015 Ar Amsterdam (Grachtengordel-West)',
  environment: 'development',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 12:15:15',
  level: 'info',
  category: 'ai_operation',
  operation: 'market_analysis',
  status: 'success',
  details: '9611 Aw Sappemeer (Polder De Nijverheid)',
  environment: 'development',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 12:15:15',
  level: 'info',
  category: 'ai_operation',
  operation: 'market_analysis',
  status: 'success',
  details: '6224 Jx Maastricht (Scharn)',
  environment: 'development',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 12:15:15',
  level: 'info',
  category: 'ai_operation',
  operation: 'market_analysis',
  status: 'success',
  details: '9712 Jb Groningen (Binnenstad-Noord)',
  environment: 'development',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 12:15:15',
  level: 'info',
  category: 'ai_operation',
  operation: 'market_analysis',
  status: 'success',
  details: '3581 Aa Utrecht (Buiten Wittevrouwen)',
  environment: 'development',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 12:15:15',
  level: 'info',
  category: 'ai_operation',
  operation: 'market_analysis',
  status: 'success',
  details: '1059 Tk Amsterdam (Hoofddorppleinbuurt)',
  environment: 'development',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 12:15:15',
  level: 'info',
  category: 'ai_operation',
  operation: 'market_analysis',
  status: 'success',
  details: '1014 Ze Amsterdam (Houthavens)',
  environment: 'development',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 12:15:15',
  level: 'info',
  category: 'ai_operation',
  operation: 'market_analysis',
  status: 'success',
  details: '1052 Rn Amsterdam (Bellamybuurt)',
  environment: 'development',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 12:15:15',
  level: 'info',
  category: 'ai_operation',
  operation: 'market_analysis',
  status: 'success',
  details: '2071 Cm Santpoort-Noord (Biezenbuurt)',
  environment: 'development',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 12:15:15',
  level: 'info',
  category: 'ai_operation',
  operation: 'market_analysis',
  status: 'success',
  details: '3526 Sm Utrecht (Kanaleneiland-Zuid)',
  environment: 'development',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 12:15:15',
  level: 'info',
  category: 'ai_operation',
  operation: 'market_analysis',
  status: 'success',
  details: '1018 Mn Amsterdam (Oostelijke Eilanden/Kadijken)',
  environment: 'development',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 12:15:15',
  level: 'info',
  category: 'ai_operation',
  operation: 'market_analysis',
  status: 'success',
  details: '2014 Cb Haarlem (Natuurkundigenbuurt-West)',
  environment: 'development',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 12:15:15',
  level: 'info',
  category: 'ai_operation',
  operation: 'market_analysis',
  status: 'success',
  details: '2141 Bk Vijfhuizen (Vijfhuizen Nieuwebrug)',
  environment: 'development',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 12:15:15',
  level: 'info',
  category: 'ai_operation',
  operation: 'market_analysis',
  status: 'success',
  details: '2547 Ah Den Haag (Leyenburg)',
  environment: 'development',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 12:15:15',
  level: 'info',
  category: 'ai_operation',
  operation: 'market_analysis',
  status: 'success',
  details: '1017 Lv Amsterdam (Grachtengordel-Zuid)',
  environment: 'development',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 12:15:15',
  level: 'info',
  category: 'ai_operation',
  operation: 'market_analysis',
  status: 'success',
  details: '9742 Sl Groningen (Paddepoel-Zuid)',
  environment: 'development',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 12:15:15',
  level: 'info',
  category: 'ai_operation',
  operation: 'market_analysis',
  status: 'success',
  details: '1012 Jd Amsterdam (Burgwallen-Oude Zijde)',
  environment: 'development',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 12:15:15',
  level: 'info',
  category: 'ai_operation',
  operation: 'market_analysis',
  status: 'success',
  details: '6229 Em Maastricht (Randwyck)',
  environment: 'development',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 12:15:16',
  level: 'info',
  category: 'ai_operation',
  operation: 'market_analysis',
  status: 'success',
  details: '5062 Te Oisterwijk (Buitengebied Zuid)',
  environment: 'development',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 12:15:16',
  level: 'info',
  category: 'ai_operation',
  operation: 'market_analysis',
  status: 'success',
  details: '1032 Ll Amsterdam (Noordelijke Ij-Oevers-West)',
  environment: 'development',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 12:15:16',
  level: 'info',
  category: 'ai_operation',
  operation: 'market_analysis',
  status: 'success',
  details: '2562 Ez Den Haag (Koningsplein En Omgeving)',
  environment: 'development',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 12:15:16',
  level: 'info',
  category: 'ai_operation',
  operation: 'market_analysis',
  status: 'success',
  details: '1187 Vz Amstelveen (Schrijversbuurt)',
  environment: 'development',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 12:15:16',
  level: 'info',
  category: 'ai_operation',
  operation: 'listing_summarization',
  status: 'success',
  details: 'english',
  environment: 'development',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 12:15:16',
  level: 'info',
  category: 'ai_operation',
  operation: 'market_analysis',
  status: 'success',
  details: '2521 Az Den Haag (Laakhaven-West)',
  environment: 'development',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 12:15:16',
  level: 'info',
  category: 'ai_operation',
  operation: 'market_analysis',
  status: 'success',
  details: '1782 Xc Den Helder (Oud Den Helder)',
  environment: 'development',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 12:15:19',
  level: 'info',
  category: 'ai_operation',
  operation: 'market_analysis',
  status: 'success',
  details: '1017 Ce Amsterdam (Grachtengordel-Zuid)',
  environment: 'development',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 12:16:01',
  level: 'info',
  category: 'ai_operation',
  operation: 'listing_matching',
  status: 'success',
  details: 100,
  environment: 'development',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 12:16:03',
  level: 'info',
  category: 'ai_operation',
  operation: 'listing_summarization',
  status: 'success',
  details: 'english',
  environment: 'development',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 12:16:05',
  level: 'info',
  category: 'ai_operation',
  operation: 'market_analysis',
  status: 'success',
  details: 'Den Haag',
  environment: 'development',
  message: 'AI Operation'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  duration: '123821ms',
  listingsProcessed: 39,
  level: 'info',
  message: 'Scheduled scraping completed',
  timestamp: '2025-07-12 12:17:03'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 12:20:00'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  duration: '115078ms',
  listingsProcessed: 39,
  level: 'info',
  message: 'Scheduled scraping completed',
  timestamp: '2025-07-12 12:21:55'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 12:25:00'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  duration: '121323ms',
  listingsProcessed: 39,
  level: 'info',
  message: 'Scheduled scraping completed',
  timestamp: '2025-07-12 12:27:01'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 12:30:00'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 12:30:06',
  level: 'info',
  category: 'ai_operation',
  operation: 'listing_matching',
  status: 'error',
  details: 'No valid JSON found in AI response',
  environment: 'development',
  message: 'AI Operation'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  duration: '118409ms',
  listingsProcessed: 39,
  level: 'info',
  message: 'Scheduled scraping completed',
  timestamp: '2025-07-12 12:31:58'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 12:35:00'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  duration: '114343ms',
  listingsProcessed: 39,
  level: 'info',
  message: 'Scheduled scraping completed',
  timestamp: '2025-07-12 12:36:54'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 12:40:00'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  duration: '115808ms',
  listingsProcessed: 39,
  level: 'info',
  message: 'Scheduled scraping completed',
  timestamp: '2025-07-12 12:41:55'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 12:45:00'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  duration: '114517ms',
  listingsProcessed: 39,
  level: 'info',
  message: 'Scheduled scraping completed',
  timestamp: '2025-07-12 12:46:54'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 12:50:00'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  duration: '111716ms',
  listingsProcessed: 39,
  level: 'info',
  message: 'Scheduled scraping completed',
  timestamp: '2025-07-12 12:51:51'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 12:55:00'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  duration: '114961ms',
  listingsProcessed: 39,
  level: 'info',
  message: 'Scheduled scraping completed',
  timestamp: '2025-07-12 12:56:54'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 13:00:00'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  duration: '115608ms',
  listingsProcessed: 39,
  level: 'info',
  message: 'Scheduled scraping completed',
  timestamp: '2025-07-12 13:01:55'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/api-docs/',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  statusCode: 304,
  responseTime: '3ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-12 13:03:52'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/api-docs/swagger-ui.css',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  statusCode: 304,
  responseTime: '1ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-12 13:03:52'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/api-docs/swagger-ui-init.js',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  statusCode: 304,
  responseTime: '1ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-12 13:03:52'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/api-docs/swagger-ui-bundle.js',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  statusCode: 304,
  responseTime: '3ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-12 13:03:52'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/api-docs/swagger-ui-standalone-preset.js',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  statusCode: 304,
  responseTime: '3ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-12 13:03:52'
}
{
  component: 'database',
  service: 'zakmakelaar-api',
  operation: 'search',
  collection: 'listings',
  duration: '19ms',
  level: 'info',
  message: 'Database Operation',
  timestamp: '2025-07-12 13:04:06'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/api/listings?sortBy=dateAdded&sortOrder=desc&page=1&limit=20',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  statusCode: 200,
  responseTime: '32ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-12 13:04:06'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 13:05:00'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  duration: '115923ms',
  listingsProcessed: 39,
  level: 'info',
  message: 'Scheduled scraping completed',
  timestamp: '2025-07-12 13:06:55'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 13:10:00'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 13:10:07',
  level: 'info',
  category: 'ai_operation',
  operation: 'listing_matching',
  status: 'success',
  details: 80,
  environment: 'development',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 13:10:08',
  level: 'info',
  category: 'ai_operation',
  operation: 'listing_matching',
  status: 'success',
  details: 80,
  environment: 'development',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 13:10:08',
  level: 'info',
  category: 'ai_operation',
  operation: 'listing_summarization',
  status: 'success',
  details: 'english',
  environment: 'development',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 13:10:09',
  level: 'info',
  category: 'ai_operation',
  operation: 'listing_summarization',
  status: 'success',
  details: 'english',
  environment: 'development',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 13:10:12',
  level: 'info',
  category: 'ai_operation',
  operation: 'market_analysis',
  status: 'success',
  details: '1104 Gm Amsterdam (Geerdinkhof/Kantershof)',
  environment: 'development',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 13:10:12',
  level: 'info',
  category: 'ai_operation',
  operation: 'market_analysis',
  status: 'success',
  details: '1093 Jp Amsterdam (Dapperbuurt)',
  environment: 'development',
  message: 'AI Operation'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  duration: '114537ms',
  listingsProcessed: 39,
  level: 'info',
  message: 'Scheduled scraping completed',
  timestamp: '2025-07-12 13:11:54'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 13:15:00'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  duration: '114970ms',
  listingsProcessed: 39,
  level: 'info',
  message: 'Scheduled scraping completed',
  timestamp: '2025-07-12 13:16:54'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 13:20:00'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  duration: '116275ms',
  listingsProcessed: 39,
  level: 'info',
  message: 'Scheduled scraping completed',
  timestamp: '2025-07-12 13:21:56'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 13:25:00'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  duration: '119071ms',
  listingsProcessed: 39,
  level: 'info',
  message: 'Scheduled scraping completed',
  timestamp: '2025-07-12 13:26:59'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 13:30:00'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 13:30:08',
  level: 'info',
  category: 'ai_operation',
  operation: 'listing_matching',
  status: 'success',
  details: 80,
  environment: 'development',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 13:30:09',
  level: 'info',
  category: 'ai_operation',
  operation: 'listing_summarization',
  status: 'success',
  details: 'english',
  environment: 'development',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 13:30:11',
  level: 'info',
  category: 'ai_operation',
  operation: 'market_analysis',
  status: 'success',
  details: '1079 Sk Amsterdam (Rijnbuurt)',
  environment: 'development',
  message: 'AI Operation'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  duration: '118546ms',
  listingsProcessed: 39,
  level: 'info',
  message: 'Scheduled scraping completed',
  timestamp: '2025-07-12 13:31:58'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 13:35:00'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 13:35:07',
  level: 'info',
  category: 'ai_operation',
  operation: 'listing_matching',
  status: 'success',
  details: 100,
  environment: 'development',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 13:35:09',
  level: 'info',
  category: 'ai_operation',
  operation: 'listing_summarization',
  status: 'success',
  details: 'english',
  environment: 'development',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 13:35:11',
  level: 'info',
  category: 'ai_operation',
  operation: 'market_analysis',
  status: 'success',
  details: '6262 Nw Banholt (Banholt)',
  environment: 'development',
  message: 'AI Operation'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  duration: '118855ms',
  listingsProcessed: 39,
  level: 'info',
  message: 'Scheduled scraping completed',
  timestamp: '2025-07-12 13:36:58'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 13:40:00'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  duration: '118287ms',
  listingsProcessed: 39,
  level: 'info',
  message: 'Scheduled scraping completed',
  timestamp: '2025-07-12 13:41:58'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 13:45:00'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  duration: '116714ms',
  listingsProcessed: 39,
  level: 'info',
  message: 'Scheduled scraping completed',
  timestamp: '2025-07-12 13:46:56'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 13:50:00'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  duration: '116252ms',
  listingsProcessed: 39,
  level: 'info',
  message: 'Scheduled scraping completed',
  timestamp: '2025-07-12 13:51:56'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 13:55:00'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  duration: '120773ms',
  listingsProcessed: 39,
  level: 'info',
  message: 'Scheduled scraping completed',
  timestamp: '2025-07-12 13:57:00'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 14:00:00'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  duration: '115042ms',
  listingsProcessed: 39,
  level: 'info',
  message: 'Scheduled scraping completed',
  timestamp: '2025-07-12 14:01:55'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 14:05:00'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  duration: '115408ms',
  listingsProcessed: 39,
  level: 'info',
  message: 'Scheduled scraping completed',
  timestamp: '2025-07-12 14:06:55'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 14:10:00'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  duration: '112511ms',
  listingsProcessed: 39,
  level: 'info',
  message: 'Scheduled scraping completed',
  timestamp: '2025-07-12 14:11:52'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 14:15:00'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  duration: '113097ms',
  listingsProcessed: 39,
  level: 'info',
  message: 'Scheduled scraping completed',
  timestamp: '2025-07-12 14:16:53'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 14:20:00'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  duration: '112414ms',
  listingsProcessed: 39,
  level: 'info',
  message: 'Scheduled scraping completed',
  timestamp: '2025-07-12 14:21:52'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 14:25:00'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 14:25:07',
  level: 'info',
  category: 'ai_operation',
  operation: 'listing_matching',
  status: 'success',
  details: 80,
  environment: 'development',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 14:25:09',
  level: 'info',
  category: 'ai_operation',
  operation: 'listing_summarization',
  status: 'success',
  details: 'english',
  environment: 'development',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 14:25:11',
  level: 'info',
  category: 'ai_operation',
  operation: 'market_analysis',
  status: 'success',
  details: '1013 Bd Amsterdam (Houthavens)',
  environment: 'development',
  message: 'AI Operation'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  duration: '117277ms',
  listingsProcessed: 39,
  level: 'info',
  message: 'Scheduled scraping completed',
  timestamp: '2025-07-12 14:26:57'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 14:30:00'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  duration: '119627ms',
  listingsProcessed: 39,
  level: 'info',
  message: 'Scheduled scraping completed',
  timestamp: '2025-07-12 14:31:59'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 14:35:00'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  duration: '114244ms',
  listingsProcessed: 39,
  level: 'info',
  message: 'Scheduled scraping completed',
  timestamp: '2025-07-12 14:36:54'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 14:40:00'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  duration: '113636ms',
  listingsProcessed: 39,
  level: 'info',
  message: 'Scheduled scraping completed',
  timestamp: '2025-07-12 14:41:53'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 14:45:00'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  duration: '117599ms',
  listingsProcessed: 39,
  level: 'info',
  message: 'Scheduled scraping completed',
  timestamp: '2025-07-12 14:46:57'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 14:50:00'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  duration: '118838ms',
  listingsProcessed: 39,
  level: 'info',
  message: 'Scheduled scraping completed',
  timestamp: '2025-07-12 14:51:58'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 14:55:00'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  duration: '116465ms',
  listingsProcessed: 39,
  level: 'info',
  message: 'Scheduled scraping completed',
  timestamp: '2025-07-12 14:56:56'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 15:00:00'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  duration: '113650ms',
  listingsProcessed: 39,
  level: 'info',
  message: 'Scheduled scraping completed',
  timestamp: '2025-07-12 15:01:53'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 15:05:00'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  duration: '115325ms',
  listingsProcessed: 39,
  level: 'info',
  message: 'Scheduled scraping completed',
  timestamp: '2025-07-12 15:06:55'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 15:10:00'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  duration: '117388ms',
  listingsProcessed: 39,
  level: 'info',
  message: 'Scheduled scraping completed',
  timestamp: '2025-07-12 15:11:57'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 15:15:00'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  duration: '114728ms',
  listingsProcessed: 39,
  level: 'info',
  message: 'Scheduled scraping completed',
  timestamp: '2025-07-12 15:16:54'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 15:20:00'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  duration: '116183ms',
  listingsProcessed: 39,
  level: 'info',
  message: 'Scheduled scraping completed',
  timestamp: '2025-07-12 15:21:56'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 15:25:00'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  duration: '113225ms',
  listingsProcessed: 39,
  level: 'info',
  message: 'Scheduled scraping completed',
  timestamp: '2025-07-12 15:26:53'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 15:30:00'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  duration: '108317ms',
  listingsProcessed: 39,
  level: 'info',
  message: 'Scheduled scraping completed',
  timestamp: '2025-07-12 15:31:48'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 15:35:00'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  duration: '115676ms',
  listingsProcessed: 39,
  level: 'info',
  message: 'Scheduled scraping completed',
  timestamp: '2025-07-12 15:36:55'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 15:40:00'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  duration: '115955ms',
  listingsProcessed: 39,
  level: 'info',
  message: 'Scheduled scraping completed',
  timestamp: '2025-07-12 15:41:55'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 15:45:00'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  duration: '119631ms',
  listingsProcessed: 39,
  level: 'info',
  message: 'Scheduled scraping completed',
  timestamp: '2025-07-12 15:46:59'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 15:50:00'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  duration: '116937ms',
  listingsProcessed: 39,
  level: 'info',
  message: 'Scheduled scraping completed',
  timestamp: '2025-07-12 15:51:56'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 15:55:00'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  duration: '119653ms',
  listingsProcessed: 39,
  level: 'info',
  message: 'Scheduled scraping completed',
  timestamp: '2025-07-12 15:56:59'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 16:00:00'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  duration: '120963ms',
  listingsProcessed: 39,
  level: 'info',
  message: 'Scheduled scraping completed',
  timestamp: '2025-07-12 16:02:00'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 16:05:00'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  duration: '118443ms',
  listingsProcessed: 39,
  level: 'info',
  message: 'Scheduled scraping completed',
  timestamp: '2025-07-12 16:06:58'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 16:10:00'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  duration: '114385ms',
  listingsProcessed: 39,
  level: 'info',
  message: 'Scheduled scraping completed',
  timestamp: '2025-07-12 16:11:54'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 16:15:00'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  duration: '121008ms',
  listingsProcessed: 39,
  level: 'info',
  message: 'Scheduled scraping completed',
  timestamp: '2025-07-12 16:17:01'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 16:20:00'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  duration: '118060ms',
  listingsProcessed: 39,
  level: 'info',
  message: 'Scheduled scraping completed',
  timestamp: '2025-07-12 16:21:58'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 16:25:00'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  duration: '115474ms',
  listingsProcessed: 39,
  level: 'info',
  message: 'Scheduled scraping completed',
  timestamp: '2025-07-12 16:26:55'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 16:30:00'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  duration: '117744ms',
  listingsProcessed: 39,
  level: 'info',
  message: 'Scheduled scraping completed',
  timestamp: '2025-07-12 16:31:57'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 16:35:00'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  duration: '116303ms',
  listingsProcessed: 39,
  level: 'info',
  message: 'Scheduled scraping completed',
  timestamp: '2025-07-12 16:36:56'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 16:40:00'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  duration: '116007ms',
  listingsProcessed: 39,
  level: 'info',
  message: 'Scheduled scraping completed',
  timestamp: '2025-07-12 16:41:56'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 16:45:00'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  duration: '115958ms',
  listingsProcessed: 39,
  level: 'info',
  message: 'Scheduled scraping completed',
  timestamp: '2025-07-12 16:46:55'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 16:50:00'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  duration: '115173ms',
  listingsProcessed: 39,
  level: 'info',
  message: 'Scheduled scraping completed',
  timestamp: '2025-07-12 16:51:55'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 16:55:00'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  duration: '111868ms',
  listingsProcessed: 39,
  level: 'info',
  message: 'Scheduled scraping completed',
  timestamp: '2025-07-12 16:56:51'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 17:00:00'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  duration: '114529ms',
  listingsProcessed: 39,
  level: 'info',
  message: 'Scheduled scraping completed',
  timestamp: '2025-07-12 17:01:54'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 17:05:00'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  duration: '111713ms',
  listingsProcessed: 39,
  level: 'info',
  message: 'Scheduled scraping completed',
  timestamp: '2025-07-12 17:06:51'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 17:10:00'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  duration: '119791ms',
  listingsProcessed: 39,
  level: 'info',
  message: 'Scheduled scraping completed',
  timestamp: '2025-07-12 17:11:59'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 17:15:00'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  duration: '118006ms',
  listingsProcessed: 39,
  level: 'info',
  message: 'Scheduled scraping completed',
  timestamp: '2025-07-12 17:16:58'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 17:20:00'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  duration: '113360ms',
  listingsProcessed: 39,
  level: 'info',
  message: 'Scheduled scraping completed',
  timestamp: '2025-07-12 17:21:53'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 17:25:00'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  duration: '113952ms',
  listingsProcessed: 39,
  level: 'info',
  message: 'Scheduled scraping completed',
  timestamp: '2025-07-12 17:26:53'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 17:30:00'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  duration: '119023ms',
  listingsProcessed: 39,
  level: 'info',
  message: 'Scheduled scraping completed',
  timestamp: '2025-07-12 17:31:59'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 17:35:00'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  duration: '116563ms',
  listingsProcessed: 39,
  level: 'info',
  message: 'Scheduled scraping completed',
  timestamp: '2025-07-12 17:36:56'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 17:40:00'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  duration: '113470ms',
  listingsProcessed: 39,
  level: 'info',
  message: 'Scheduled scraping completed',
  timestamp: '2025-07-12 17:41:53'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 17:45:00'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  duration: '113574ms',
  listingsProcessed: 39,
  level: 'info',
  message: 'Scheduled scraping completed',
  timestamp: '2025-07-12 17:46:53'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 17:50:00'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  duration: '119286ms',
  listingsProcessed: 39,
  level: 'info',
  message: 'Scheduled scraping completed',
  timestamp: '2025-07-12 17:51:59'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 17:55:00'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  duration: '117218ms',
  listingsProcessed: 39,
  level: 'info',
  message: 'Scheduled scraping completed',
  timestamp: '2025-07-12 17:56:57'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 18:00:00'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  duration: '114493ms',
  listingsProcessed: 39,
  level: 'info',
  message: 'Scheduled scraping completed',
  timestamp: '2025-07-12 18:01:54'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 18:05:00'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  duration: '118621ms',
  listingsProcessed: 39,
  level: 'info',
  message: 'Scheduled scraping completed',
  timestamp: '2025-07-12 18:06:58'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 18:10:00'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  duration: '115495ms',
  listingsProcessed: 39,
  level: 'info',
  message: 'Scheduled scraping completed',
  timestamp: '2025-07-12 18:11:55'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 18:15:00'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  duration: '116795ms',
  listingsProcessed: 39,
  level: 'info',
  message: 'Scheduled scraping completed',
  timestamp: '2025-07-12 18:16:56'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 18:20:00'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  duration: '116548ms',
  listingsProcessed: 39,
  level: 'info',
  message: 'Scheduled scraping completed',
  timestamp: '2025-07-12 18:21:56'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 18:25:00'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  duration: '113298ms',
  listingsProcessed: 39,
  level: 'info',
  message: 'Scheduled scraping completed',
  timestamp: '2025-07-12 18:26:53'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 18:30:00'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  duration: '109691ms',
  listingsProcessed: 39,
  level: 'info',
  message: 'Scheduled scraping completed',
  timestamp: '2025-07-12 18:31:49'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 18:35:00'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  duration: '112497ms',
  listingsProcessed: 39,
  level: 'info',
  message: 'Scheduled scraping completed',
  timestamp: '2025-07-12 18:36:52'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 18:40:00'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  duration: '118675ms',
  listingsProcessed: 39,
  level: 'info',
  message: 'Scheduled scraping completed',
  timestamp: '2025-07-12 18:41:58'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 18:45:00'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 18:45:15',
  level: 'info',
  category: 'ai_operation',
  operation: 'listing_matching',
  status: 'success',
  details: 90,
  environment: 'development',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 18:45:15',
  level: 'info',
  category: 'ai_operation',
  operation: 'listing_summarization',
  status: 'success',
  details: 'english',
  environment: 'development',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 18:45:18',
  level: 'info',
  category: 'ai_operation',
  operation: 'market_analysis',
  status: 'success',
  details: '3077 Mk Rotterdam (Oud Ijsselmonde)',
  environment: 'development',
  message: 'AI Operation'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  duration: '115192ms',
  listingsProcessed: 39,
  level: 'info',
  message: 'Scheduled scraping completed',
  timestamp: '2025-07-12 18:46:55'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 18:50:00'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 18:50:08',
  level: 'info',
  category: 'ai_operation',
  operation: 'listing_matching',
  status: 'success',
  details: 80,
  environment: 'development',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 18:50:09',
  level: 'info',
  category: 'ai_operation',
  operation: 'listing_summarization',
  status: 'success',
  details: 'english',
  environment: 'development',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 18:50:11',
  level: 'info',
  category: 'ai_operation',
  operation: 'market_analysis',
  status: 'success',
  details: '1015 Kv Amsterdam (Jordaan)',
  environment: 'development',
  message: 'AI Operation'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  duration: '114890ms',
  listingsProcessed: 39,
  level: 'info',
  message: 'Scheduled scraping completed',
  timestamp: '2025-07-12 18:51:54'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 18:55:00'
}
{
  service: 'zakmakelaar-api',
  port: '3000',
  environment: 'development',
  endpoints: {
    api: 'http://localhost:3000',
    docs: 'http://localhost:3000/api-docs',
    health: 'http://localhost:3000/health'
  },
  level: 'info',
  message: '🚀 ZakMakelaar API Server Started',
  timestamp: '2025-07-12 19:35:00'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 19:40:00'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  duration: '114855ms',
  listingsProcessed: 39,
  level: 'info',
  message: 'Scheduled scraping completed',
  timestamp: '2025-07-12 19:41:54'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 19:45:00'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  duration: '113446ms',
  listingsProcessed: 39,
  level: 'info',
  message: 'Scheduled scraping completed',
  timestamp: '2025-07-12 19:46:53'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 19:50:00'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  duration: '113133ms',
  listingsProcessed: 39,
  level: 'info',
  message: 'Scheduled scraping completed',
  timestamp: '2025-07-12 19:51:53'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 19:55:00'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/health',
  ip: '::1',
  userAgent: 'curl/8.12.1',
  statusCode: 200,
  responseTime: '8ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-12 19:56:33'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  duration: '116183ms',
  listingsProcessed: 39,
  level: 'info',
  message: 'Scheduled scraping completed',
  timestamp: '2025-07-12 19:56:56'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'POST',
  url: '/api/auth/login',
  ip: '::1',
  userAgent: 'curl/8.12.1',
  statusCode: 401,
  responseTime: '18ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-12 19:57:39'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/api/auth/login',
  ip: '::ffff:127.0.0.1',
  userAgent: 'Augment-VSCode/1.0',
  statusCode: 404,
  responseTime: '1ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-12 19:57:51'
}
{
  component: 'database',
  service: 'zakmakelaar-api',
  operation: 'search',
  collection: 'listings',
  duration: '7ms',
  level: 'info',
  message: 'Database Operation',
  timestamp: '2025-07-12 19:58:27'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/api/listings',
  ip: '::ffff:127.0.0.1',
  userAgent: 'Augment-VSCode/1.0',
  statusCode: 200,
  responseTime: '13ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-12 19:58:27'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/api/auth/register',
  ip: '::ffff:127.0.0.1',
  userAgent: 'Augment-VSCode/1.0',
  statusCode: 404,
  responseTime: '1ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-12 19:58:35'
}
{
  service: 'zakmakelaar-api',
  port: '3000',
  environment: 'development',
  endpoints: {
    api: 'http://localhost:3000',
    docs: 'http://localhost:3000/api-docs',
    health: 'http://localhost:3000/health'
  },
  level: 'info',
  message: '🚀 ZakMakelaar API Server Started',
  timestamp: '2025-07-12 19:59:29'
}
{
  service: 'zakmakelaar-api',
  port: '3000',
  environment: 'development',
  endpoints: {
    api: 'http://localhost:3000',
    docs: 'http://localhost:3000/api-docs',
    health: 'http://localhost:3000/health'
  },
  level: 'info',
  message: '🚀 ZakMakelaar API Server Started',
  timestamp: '2025-07-12 19:59:41'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/api/auth/test',
  ip: '::ffff:127.0.0.1',
  userAgent: 'Augment-VSCode/1.0',
  statusCode: 200,
  responseTime: '2ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-12 19:59:50'
}
{
  service: 'zakmakelaar-api',
  port: '3000',
  environment: 'development',
  endpoints: {
    api: 'http://localhost:3000',
    docs: 'http://localhost:3000/api-docs',
    health: 'http://localhost:3000/health'
  },
  level: 'info',
  message: '🚀 ZakMakelaar API Server Started',
  timestamp: '2025-07-12 20:00:00'
}
{
  service: 'zakmakelaar-api',
  port: '3000',
  environment: 'development',
  endpoints: {
    api: 'http://localhost:3000',
    docs: 'http://localhost:3000/api-docs',
    health: 'http://localhost:3000/health'
  },
  level: 'info',
  message: '🚀 ZakMakelaar API Server Started',
  timestamp: '2025-07-12 20:00:17'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'POST',
  url: '/api/auth/login',
  ip: '::1',
  userAgent: 'curl/8.12.1',
  statusCode: 401,
  responseTime: '19ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-12 20:00:37'
}
{
  service: 'zakmakelaar-api',
  port: '3000',
  environment: 'development',
  endpoints: {
    api: 'http://localhost:3000',
    docs: 'http://localhost:3000/api-docs',
    health: 'http://localhost:3000/health'
  },
  level: 'info',
  message: '🚀 ZakMakelaar API Server Started',
  timestamp: '2025-07-12 20:00:51'
}
{
  service: 'zakmakelaar-api',
  port: '3000',
  environment: 'development',
  endpoints: {
    api: 'http://localhost:3000',
    docs: 'http://localhost:3000/api-docs',
    health: 'http://localhost:3000/health'
  },
  level: 'info',
  message: '🚀 ZakMakelaar API Server Started',
  timestamp: '2025-07-12 20:01:02'
}
{
  service: 'zakmakelaar-api',
  port: '3000',
  environment: 'development',
  endpoints: {
    api: 'http://localhost:3000',
    docs: 'http://localhost:3000/api-docs',
    health: 'http://localhost:3000/health'
  },
  level: 'info',
  message: '🚀 ZakMakelaar API Server Started',
  timestamp: '2025-07-12 20:01:14'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'POST',
  url: '/api/auth/register',
  ip: '::1',
  userAgent: 'curl/8.12.1',
  statusCode: 201,
  responseTime: '87ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-12 20:01:37'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'POST',
  url: '/api/auth/login',
  ip: '::1',
  userAgent: 'curl/8.12.1',
  statusCode: 200,
  responseTime: '57ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-12 20:01:48'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 20:05:00'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/health',
  ip: '::ffff:************',
  userAgent: 'okhttp/4.12.0',
  statusCode: 200,
  responseTime: '6ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-12 20:05:02'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/health',
  ip: '::ffff:************',
  userAgent: 'okhttp/4.12.0',
  statusCode: 200,
  responseTime: '5ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-12 20:05:17'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/health',
  ip: '::ffff:************',
  userAgent: 'okhttp/4.12.0',
  statusCode: 200,
  responseTime: '3ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-12 20:05:20'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'POST',
  url: '/api/auth/register',
  ip: '::ffff:************',
  userAgent: 'okhttp/4.12.0',
  statusCode: 201,
  responseTime: '68ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-12 20:05:20'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'POST',
  url: '/api/auth/register',
  ip: '::ffff:************',
  userAgent: 'curl/8.12.1',
  statusCode: 201,
  responseTime: '65ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-12 20:06:07'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  duration: '114013ms',
  listingsProcessed: 39,
  level: 'info',
  message: 'Scheduled scraping completed',
  timestamp: '2025-07-12 20:06:54'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/health',
  ip: '::ffff:************',
  userAgent: 'okhttp/4.12.0',
  statusCode: 200,
  responseTime: '5ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-12 20:07:33'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/health',
  ip: '::ffff:************',
  userAgent: 'okhttp/4.12.0',
  statusCode: 200,
  responseTime: '3ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-12 20:07:49'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/health',
  ip: '::ffff:************',
  userAgent: 'okhttp/4.12.0',
  statusCode: 200,
  responseTime: '3ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-12 20:08:07'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'POST',
  url: '/api/auth/register',
  ip: '::ffff:************',
  userAgent: 'okhttp/4.12.0',
  statusCode: 201,
  responseTime: '67ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-12 20:08:07'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/health',
  ip: '::ffff:************',
  userAgent: 'okhttp/4.12.0',
  statusCode: 200,
  responseTime: '2ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-12 20:08:11'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/health',
  ip: '::ffff:************',
  userAgent: 'okhttp/4.12.0',
  statusCode: 200,
  responseTime: '3ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-12 20:08:20'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'POST',
  url: '/api/auth/register',
  ip: '::ffff:************',
  userAgent: 'okhttp/4.12.0',
  statusCode: 201,
  responseTime: '57ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-12 20:08:20'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'POST',
  url: '/api/auth/logout',
  ip: '::ffff:************',
  userAgent: 'okhttp/4.12.0',
  statusCode: 404,
  responseTime: '1ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-12 20:08:20'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'POST',
  url: '/api/auth/login',
  ip: '::ffff:************',
  userAgent: 'okhttp/4.12.0',
  statusCode: 200,
  responseTime: '55ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-12 20:08:20'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/api/auth/me',
  ip: '::ffff:************',
  userAgent: 'okhttp/4.12.0',
  statusCode: 401,
  responseTime: '1ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-12 20:08:20'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/health',
  ip: '::ffff:************',
  userAgent: 'okhttp/4.12.0',
  statusCode: 200,
  responseTime: '3ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-12 20:08:27'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/health',
  ip: '::ffff:************',
  userAgent: 'okhttp/4.12.0',
  statusCode: 200,
  responseTime: '3ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-12 20:08:36'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'POST',
  url: '/api/auth/register',
  ip: '::ffff:************',
  userAgent: 'okhttp/4.12.0',
  statusCode: 201,
  responseTime: '57ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-12 20:08:36'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'POST',
  url: '/api/auth/logout',
  ip: '::ffff:************',
  userAgent: 'okhttp/4.12.0',
  statusCode: 404,
  responseTime: '1ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-12 20:08:36'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'POST',
  url: '/api/auth/login',
  ip: '::ffff:************',
  userAgent: 'okhttp/4.12.0',
  statusCode: 200,
  responseTime: '59ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-12 20:08:36'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/api/auth/me',
  ip: '::ffff:************',
  userAgent: 'okhttp/4.12.0',
  statusCode: 200,
  responseTime: '5ms',
  userId: new ObjectId('6872b2b419532d2cdf341d40'),
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-12 20:08:36'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/health',
  ip: '::ffff:************',
  userAgent: 'okhttp/4.12.0',
  statusCode: 200,
  responseTime: '3ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-12 20:08:38'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/health',
  ip: '::ffff:************',
  userAgent: 'okhttp/4.12.0',
  statusCode: 200,
  responseTime: '3ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-12 20:08:52'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/health',
  ip: '::ffff:************',
  userAgent: 'okhttp/4.12.0',
  statusCode: 200,
  responseTime: '4ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-12 20:09:57'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'POST',
  url: '/api/auth/register',
  ip: '::ffff:************',
  userAgent: 'okhttp/4.12.0',
  statusCode: 201,
  responseTime: '56ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-12 20:09:57'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'POST',
  url: '/api/auth/logout',
  ip: '::ffff:************',
  userAgent: 'okhttp/4.12.0',
  statusCode: 404,
  responseTime: '0ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-12 20:09:57'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'POST',
  url: '/api/auth/login',
  ip: '::ffff:************',
  userAgent: 'okhttp/4.12.0',
  statusCode: 200,
  responseTime: '55ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-12 20:09:57'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/api/auth/me',
  ip: '::ffff:************',
  userAgent: 'okhttp/4.12.0',
  statusCode: 200,
  responseTime: '4ms',
  userId: new ObjectId('6872b30519532d2cdf341d46'),
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-12 20:09:58'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 20:10:00'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/health',
  ip: '::ffff:************',
  userAgent: 'okhttp/4.12.0',
  statusCode: 200,
  responseTime: '4ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-12 20:11:18'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'POST',
  url: '/api/auth/register',
  ip: '::ffff:************',
  userAgent: 'okhttp/4.12.0',
  statusCode: 201,
  responseTime: '61ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-12 20:11:18'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'POST',
  url: '/api/auth/logout',
  ip: '::ffff:************',
  userAgent: 'okhttp/4.12.0',
  statusCode: 404,
  responseTime: '0ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-12 20:11:18'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'POST',
  url: '/api/auth/login',
  ip: '::ffff:************',
  userAgent: 'okhttp/4.12.0',
  statusCode: 200,
  responseTime: '56ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-12 20:11:19'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/api/auth/me',
  ip: '::ffff:************',
  userAgent: 'okhttp/4.12.0',
  statusCode: 200,
  responseTime: '7ms',
  userId: new ObjectId('6872b35619532d2cdf341d9a'),
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-12 20:11:19'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/health',
  ip: '::ffff:************',
  userAgent: 'okhttp/4.12.0',
  statusCode: 200,
  responseTime: '3ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-12 20:11:28'
}
{
  service: 'zakmakelaar-api',
  port: '3000',
  environment: 'development',
  endpoints: {
    api: 'http://localhost:3000',
    docs: 'http://localhost:3000/api-docs',
    health: 'http://localhost:3000/health'
  },
  level: 'info',
  message: '🚀 ZakMakelaar API Server Started',
  timestamp: '2025-07-12 20:11:41'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/health',
  ip: '::ffff:************',
  userAgent: 'okhttp/4.12.0',
  statusCode: 200,
  responseTime: '7ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-12 20:11:55'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/health',
  ip: '::ffff:************',
  userAgent: 'okhttp/4.12.0',
  statusCode: 200,
  responseTime: '3ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-12 20:12:16'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'POST',
  url: '/api/auth/register',
  ip: '::ffff:************',
  userAgent: 'okhttp/4.12.0',
  statusCode: 201,
  responseTime: '85ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-12 20:12:16'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'POST',
  url: '/api/auth/logout',
  ip: '::ffff:************',
  userAgent: 'okhttp/4.12.0',
  statusCode: 200,
  responseTime: '1ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-12 20:12:16'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'POST',
  url: '/api/auth/login',
  ip: '::ffff:************',
  userAgent: 'okhttp/4.12.0',
  statusCode: 200,
  responseTime: '57ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-12 20:12:16'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/api/auth/me',
  ip: '::ffff:************',
  userAgent: 'okhttp/4.12.0',
  statusCode: 200,
  responseTime: '8ms',
  userId: new ObjectId('6872b39028e8d868f5e61f1e'),
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-12 20:12:16'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/health',
  ip: '::ffff:************',
  userAgent: 'okhttp/4.12.0',
  statusCode: 200,
  responseTime: '4ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-12 20:14:39'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 20:15:00'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/health',
  ip: '::ffff:************',
  userAgent: 'okhttp/4.12.0',
  statusCode: 200,
  responseTime: '3ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-12 20:15:19'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'POST',
  url: '/api/auth/register',
  ip: '::ffff:************',
  userAgent: 'okhttp/4.12.0',
  statusCode: 201,
  responseTime: '60ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-12 20:15:19'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'POST',
  url: '/api/auth/logout',
  ip: '::ffff:************',
  userAgent: 'okhttp/4.12.0',
  statusCode: 200,
  responseTime: '1ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-12 20:15:19'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'POST',
  url: '/api/auth/login',
  ip: '::ffff:************',
  userAgent: 'okhttp/4.12.0',
  statusCode: 200,
  responseTime: '56ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-12 20:15:19'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/api/auth/me',
  ip: '::ffff:************',
  userAgent: 'okhttp/4.12.0',
  statusCode: 200,
  responseTime: '4ms',
  userId: new ObjectId('6872b44728e8d868f5e61f64'),
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-12 20:15:19'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/health',
  ip: '::ffff:************',
  userAgent: 'okhttp/4.12.0',
  statusCode: 200,
  responseTime: '3ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-12 20:15:39'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'POST',
  url: '/api/auth/register',
  ip: '::ffff:************',
  userAgent: 'okhttp/4.12.0',
  statusCode: 201,
  responseTime: '67ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-12 20:15:39'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'POST',
  url: '/api/auth/logout',
  ip: '::ffff:************',
  userAgent: 'okhttp/4.12.0',
  statusCode: 200,
  responseTime: '0ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-12 20:15:39'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'POST',
  url: '/api/auth/login',
  ip: '::ffff:************',
  userAgent: 'okhttp/4.12.0',
  statusCode: 200,
  responseTime: '67ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-12 20:15:39'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/api/auth/me',
  ip: '::ffff:************',
  userAgent: 'okhttp/4.12.0',
  statusCode: 200,
  responseTime: '7ms',
  userId: new ObjectId('6872b45b28e8d868f5e61f6a'),
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-12 20:15:39'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/health',
  ip: '::ffff:************',
  userAgent: 'okhttp/4.12.0',
  statusCode: 200,
  responseTime: '9ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-12 20:15:51'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/health',
  ip: '::ffff:************',
  userAgent: 'okhttp/4.12.0',
  statusCode: 200,
  responseTime: '3ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-12 20:15:56'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'POST',
  url: '/api/auth/register',
  ip: '::ffff:************',
  userAgent: 'okhttp/4.12.0',
  statusCode: 201,
  responseTime: '62ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-12 20:16:01'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'POST',
  url: '/api/auth/logout',
  ip: '::ffff:************',
  userAgent: 'okhttp/4.12.0',
  statusCode: 200,
  responseTime: '0ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-12 20:16:01'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'POST',
  url: '/api/auth/login',
  ip: '::ffff:************',
  userAgent: 'okhttp/4.12.0',
  statusCode: 200,
  responseTime: '58ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-12 20:16:01'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/api/auth/me',
  ip: '::ffff:************',
  userAgent: 'okhttp/4.12.0',
  statusCode: 200,
  responseTime: '5ms',
  userId: new ObjectId('6872b47128e8d868f5e61f7e'),
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-12 20:16:01'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  duration: '118136ms',
  listingsProcessed: 39,
  level: 'info',
  message: 'Scheduled scraping completed',
  timestamp: '2025-07-12 20:16:58'
}
{
  component: 'database',
  service: 'zakmakelaar-api',
  operation: 'search',
  collection: 'listings',
  duration: '3ms',
  level: 'info',
  message: 'Database Operation',
  timestamp: '2025-07-12 20:17:38'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/api/listings?limit=10&sortBy=dateAdded&sortOrder=desc',
  ip: '::ffff:************',
  userAgent: 'okhttp/4.12.0',
  statusCode: 200,
  responseTime: '8ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-12 20:17:38'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'POST',
  url: '/api/auth/logout',
  ip: '::ffff:************',
  userAgent: 'okhttp/4.12.0',
  statusCode: 200,
  responseTime: '0ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-12 20:17:43'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'POST',
  url: '/api/auth/login',
  ip: '::ffff:************',
  userAgent: 'okhttp/4.12.0',
  statusCode: 200,
  responseTime: '60ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-12 20:18:21'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/health',
  ip: '::ffff:************',
  userAgent: 'okhttp/4.12.0',
  statusCode: 200,
  responseTime: '5ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-12 20:18:58'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/health',
  ip: '::ffff:************',
  userAgent: 'okhttp/4.12.0',
  statusCode: 200,
  responseTime: '2ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-12 20:19:10'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/api/auth/me',
  ip: '::ffff:************',
  userAgent: 'okhttp/4.12.0',
  statusCode: 200,
  responseTime: '5ms',
  userId: new ObjectId('6866e89fa6499049b3805e29'),
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-12 20:19:10'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'POST',
  url: '/api/auth/logout',
  ip: '::ffff:************',
  userAgent: 'okhttp/4.12.0',
  statusCode: 200,
  responseTime: '0ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-12 20:19:20'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/health',
  ip: '::ffff:************',
  userAgent: 'okhttp/4.12.0',
  statusCode: 200,
  responseTime: '4ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-12 20:19:46'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'POST',
  url: '/api/auth/login',
  ip: '::ffff:************',
  userAgent: 'okhttp/4.12.0',
  statusCode: 200,
  responseTime: '57ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-12 20:19:56'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 20:20:00'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  duration: '121533ms',
  listingsProcessed: 39,
  level: 'info',
  message: 'Scheduled scraping completed',
  timestamp: '2025-07-12 20:22:01'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/api/auth/me',
  ip: '::ffff:************',
  userAgent: 'okhttp/4.12.0',
  statusCode: 304,
  responseTime: '8ms',
  userId: new ObjectId('6866e89fa6499049b3805e29'),
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-12 20:22:22'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/api/auth/me',
  ip: '::ffff:************',
  userAgent: 'okhttp/4.12.0',
  statusCode: 304,
  responseTime: '4ms',
  userId: new ObjectId('6866e89fa6499049b3805e29'),
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-12 20:22:22'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'POST',
  url: '/api/auth/logout',
  ip: '::ffff:************',
  userAgent: 'okhttp/4.12.0',
  statusCode: 200,
  responseTime: '0ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-12 20:22:22'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/api/auth/me',
  ip: '::ffff:************',
  userAgent: 'okhttp/4.12.0',
  statusCode: 401,
  responseTime: '2ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-12 20:22:27'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'POST',
  url: '/api/auth/logout',
  ip: '::ffff:************',
  userAgent: 'okhttp/4.12.0',
  statusCode: 200,
  responseTime: '0ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-12 20:22:27'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/api/auth/me',
  ip: '::ffff:************',
  userAgent: 'okhttp/4.12.0',
  statusCode: 401,
  responseTime: '1ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-12 20:22:38'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'POST',
  url: '/api/auth/logout',
  ip: '::ffff:************',
  userAgent: 'okhttp/4.12.0',
  statusCode: 200,
  responseTime: '1ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-12 20:22:38'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'POST',
  url: '/api/auth/login',
  ip: '::ffff:************',
  userAgent: 'okhttp/4.12.0',
  statusCode: 401,
  responseTime: '4ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-12 20:24:16'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'POST',
  url: '/api/auth/login',
  ip: '::ffff:************',
  userAgent: 'okhttp/4.12.0',
  statusCode: 200,
  responseTime: '56ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-12 20:24:54'
}
{
  component: 'database',
  service: 'zakmakelaar-api',
  operation: 'search',
  collection: 'listings',
  duration: '3ms',
  level: 'info',
  message: 'Database Operation',
  timestamp: '2025-07-12 20:24:55'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/api/listings?limit=10&sortBy=dateAdded&sortOrder=desc',
  ip: '::ffff:************',
  userAgent: 'okhttp/4.12.0',
  statusCode: 304,
  responseTime: '7ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-12 20:24:55'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/api/listings?limit=10&sortBy=dateAdded&sortOrder=desc',
  ip: '::ffff:************',
  userAgent: 'okhttp/4.12.0',
  statusCode: 304,
  responseTime: '1ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-12 20:24:55'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 20:25:00'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/api/listings/stats',
  ip: '::ffff:************',
  userAgent: 'okhttp/4.12.0',
  statusCode: 400,
  responseTime: '2ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-12 20:26:27'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  duration: '116776ms',
  listingsProcessed: 39,
  level: 'info',
  message: 'Scheduled scraping completed',
  timestamp: '2025-07-12 20:26:56'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/api/listings/stats',
  ip: '::ffff:************',
  userAgent: 'okhttp/4.12.0',
  statusCode: 400,
  responseTime: '1ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-12 20:27:58'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 20:30:00'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/health',
  ip: '::ffff:************',
  userAgent: 'okhttp/4.12.0',
  statusCode: 200,
  responseTime: '3ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-12 20:30:12'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/api/auth/me',
  ip: '::ffff:************',
  userAgent: 'okhttp/4.12.0',
  statusCode: 304,
  responseTime: '7ms',
  userId: new ObjectId('6866e89fa6499049b3805e29'),
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-12 20:30:12'
}
{
  component: 'database',
  service: 'zakmakelaar-api',
  operation: 'search',
  collection: 'listings',
  duration: '3ms',
  level: 'info',
  message: 'Database Operation',
  timestamp: '2025-07-12 20:30:14'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/api/listings?limit=10&sortBy=dateAdded&sortOrder=desc',
  ip: '::ffff:************',
  userAgent: 'okhttp/4.12.0',
  statusCode: 304,
  responseTime: '6ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-12 20:30:14'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/api/listings/stats',
  ip: '::ffff:************',
  userAgent: 'okhttp/4.12.0',
  statusCode: 400,
  responseTime: '1ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-12 20:30:14'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  duration: '120515ms',
  listingsProcessed: 39,
  level: 'info',
  message: 'Scheduled scraping completed',
  timestamp: '2025-07-12 20:32:00'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/health',
  ip: '::ffff:************',
  userAgent: 'okhttp/4.12.0',
  statusCode: 200,
  responseTime: '5ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-12 20:32:12'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/api/auth/me',
  ip: '::ffff:************',
  userAgent: 'okhttp/4.12.0',
  statusCode: 304,
  responseTime: '9ms',
  userId: new ObjectId('6866e89fa6499049b3805e29'),
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-12 20:32:12'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/api/listings/stats',
  ip: '::ffff:************',
  userAgent: 'okhttp/4.12.0',
  statusCode: 400,
  responseTime: '1ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-12 20:32:14'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-07-12 20:35:00'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  duration: '116036ms',
  listingsProcessed: 39,
  level: 'info',
  message: 'Scheduled scraping completed',
  timestamp: '2025-07-12 20:36:56'
}
