const express = require("express");
const router = express.Router();
const aiService = require("../services/aiService");
const auth = require("../middleware/auth");
const { logHelpers } = require("../services/logger");
const Listing = require("../models/Listing");

/**
 * @swagger
 * /api/ai/match:
 *   post:
 *     summary: AI-powered listing matching
 *     tags: [AI]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - listing
 *               - userPreferences
 *             properties:
 *               listing:
 *                 type: object
 *                 properties:
 *                   title: { type: string }
 *                   price: { type: string }
 *                   location: { type: string }
 *                   size: { type: string }
 *                   rooms: { type: string }
 *                   propertyType: { type: string }
 *               userPreferences:
 *                 type: object
 *                 properties:
 *                   location: { type: string }
 *                   budget: { type: number }
 *                   rooms: { type: number }
 *                   propertyType: { type: string }
 *     responses:
 *       200:
 *         description: AI matching result
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 score: { type: number }
 *                 matchReasoning: { type: string }
 *                 keyHighlights: { type: array }
 *                 potentialConcerns: { type: array }
 *                 recommendation: { type: string }
 */
router.post("/match", auth, async (req, res) => {
  try {
    const { userPreferences } = req.body;
    const startTime = Date.now();

    // Log incoming payload
    console.log(
      "[AI MATCH] Incoming payload:",
      JSON.stringify({ userPreferences }, null, 2)
    );

    // Fetch only the 10 most recent listings from the database
    const listings = await Listing.find({}).sort({ dateAdded: -1 }).limit(10);
    if (!listings.length) {
      return res
        .status(404)
        .json({ success: false, error: "No listings found" });
    }

    // For each listing, get the AI match score
    let bestMatch = null;
    let bestScore = -1;
    let bestResult = null;
    for (const listing of listings) {
      try {
        const result = await aiService.matchListingToUser(
          listing,
          userPreferences
        );
        if (
          result &&
          typeof result.score === "number" &&
          result.score > bestScore
        ) {
          bestScore = result.score;
          bestMatch = listing;
          bestResult = result;
        }
      } catch (err) {
        console.error("[AI MATCH] Error matching listing:", listing._id, err);
      }
    }

    if (!bestResult) {
      return res
        .status(404)
        .json({ success: false, error: "No suitable match found" });
    }

    // Log AI service result
    console.log(
      "[AI MATCH] AI service result:",
      JSON.stringify(bestResult, null, 2)
    );

    const duration = Date.now() - startTime;
    logHelpers.logAiPerformance("listing_matching", duration, "claude-3-haiku");

    res.json({
      success: true,
      data: bestResult,
      bestListing: bestMatch,
      performance: {
        duration: `${duration}ms`,
      },
    });
  } catch (error) {
    // Log error
    console.error("[AI MATCH] Error:", error);
    logHelpers.logAiOperation("listing_matching", "error", error.message);
    res.status(500).json({
      success: false,
      error: "AI matching failed",
      details: error.message,
    });
  }
});

/**
 * @swagger
 * /api/ai/contract/analyze:
 *   post:
 *     summary: Analyze rental contract for legal compliance
 *     tags: [AI]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - contractText
 *             properties:
 *               contractText: { type: string }
 *               language: { type: string, default: "dutch" }
 *     responses:
 *       200:
 *         description: Contract analysis result
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 riskLevel: { type: string }
 *                 complianceScore: { type: number }
 *                 keyClauses: { type: array }
 *                 potentialIssues: { type: array }
 *                 recommendations: { type: array }
 *                 summary: { type: string }
 *                 legalAdvice: { type: string }
 */
router.post("/contract/analyze", auth, async (req, res) => {
  try {
    const { contractText, language = "dutch" } = req.body;
    const startTime = Date.now();

    const result = await aiService.analyzeContract(contractText, language);

    const duration = Date.now() - startTime;
    logHelpers.logAiPerformance("contract_analysis", duration, "gpt-4o-mini");

    res.json({
      success: true,
      data: result,
      performance: {
        duration: `${duration}ms`,
      },
    });
  } catch (error) {
    logHelpers.logAiOperation("contract_analysis", "error", error.message);
    res.status(500).json({
      success: false,
      error: "Contract analysis failed",
      details: error.message,
    });
  }
});

/**
 * @swagger
 * /api/ai/application/generate:
 *   post:
 *     summary: Generate personalized application message
 *     tags: [AI]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - listing
 *               - userProfile
 *             properties:
 *               listing:
 *                 type: object
 *                 properties:
 *                   title: { type: string }
 *                   location: { type: string }
 *                   price: { type: string }
 *               userProfile:
 *                 type: object
 *                 properties:
 *                   name: { type: string }
 *                   income: { type: number }
 *                   occupation: { type: string }
 *               template: { type: string, default: "professional" }
 *     responses:
 *       200:
 *         description: Generated application message
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message: { type: string }
 *                 template: { type: string }
 *                 generatedAt: { type: string }
 */
router.post("/application/generate", auth, async (req, res) => {
  try {
    const { listing, userProfile, template = "professional" } = req.body;
    const startTime = Date.now();

    const result = await aiService.generateApplicationMessage(
      listing,
      userProfile,
      template
    );

    const duration = Date.now() - startTime;
    logHelpers.logAiPerformance(
      "application_generation",
      duration,
      "gpt-4o-mini"
    );

    res.json({
      success: true,
      data: result,
      performance: {
        duration: `${duration}ms`,
      },
    });
  } catch (error) {
    logHelpers.logAiOperation("application_generation", "error", error.message);
    res.status(500).json({
      success: false,
      error: "Application generation failed",
      details: error.message,
    });
  }
});

/**
 * @swagger
 * /api/ai/market/analyze:
 *   post:
 *     summary: Analyze market trends and provide predictions
 *     tags: [AI]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - location
 *               - propertyType
 *             properties:
 *               location: { type: string }
 *               propertyType: { type: string }
 *               historicalData: { type: object }
 *     responses:
 *       200:
 *         description: Market analysis result
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 marketTrend: { type: string }
 *                 pricePrediction: { type: string }
 *                 demandLevel: { type: string }
 *                 keyInsights: { type: array }
 *                 recommendations: { type: array }
 *                 confidenceScore: { type: number }
 */
router.post("/market/analyze", auth, async (req, res) => {
  try {
    const { location, propertyType, historicalData = {} } = req.body;
    const startTime = Date.now();

    const result = await aiService.analyzeMarketTrends(
      location,
      propertyType,
      historicalData
    );

    const duration = Date.now() - startTime;
    logHelpers.logAiPerformance("market_analysis", duration, "gpt-4o-mini");

    res.json({
      success: true,
      data: result,
      performance: {
        duration: `${duration}ms`,
      },
    });
  } catch (error) {
    logHelpers.logAiOperation("market_analysis", "error", error.message);
    res.status(500).json({
      success: false,
      error: "Market analysis failed",
      details: error.message,
    });
  }
});

/**
 * @swagger
 * /api/ai/listing/summarize:
 *   post:
 *     summary: Generate smart listing summary
 *     tags: [AI]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - listing
 *             properties:
 *               listing:
 *                 type: object
 *                 properties:
 *                   title: { type: string }
 *                   price: { type: string }
 *                   location: { type: string }
 *                   size: { type: string }
 *                   rooms: { type: string }
 *                   propertyType: { type: string }
 *               language: { type: string, default: "english" }
 *     responses:
 *       200:
 *         description: Listing summary
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 summary: { type: string }
 *                 language: { type: string }
 *                 generatedAt: { type: string }
 */
router.post("/listing/summarize", auth, async (req, res) => {
  try {
    const { listing, language = "english" } = req.body;
    const startTime = Date.now();

    const result = await aiService.summarizeListing(listing, language);

    const duration = Date.now() - startTime;
    logHelpers.logAiPerformance(
      "listing_summarization",
      duration,
      "llama-3.1-8b"
    );

    res.json({
      success: true,
      data: result,
      performance: {
        duration: `${duration}ms`,
      },
    });
  } catch (error) {
    logHelpers.logAiOperation("listing_summarization", "error", error.message);
    res.status(500).json({
      success: false,
      error: "Listing summarization failed",
      details: error.message,
    });
  }
});

/**
 * @swagger
 * /api/ai/translate:
 *   post:
 *     summary: Translate real estate content
 *     tags: [AI]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - content
 *               - fromLanguage
 *               - toLanguage
 *             properties:
 *               content: { type: string }
 *               fromLanguage: { type: string }
 *               toLanguage: { type: string }
 *     responses:
 *       200:
 *         description: Translated content
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 original: { type: string }
 *                 translation: { type: string }
 *                 fromLanguage: { type: string }
 *                 toLanguage: { type: string }
 *                 translatedAt: { type: string }
 */
router.post("/translate", auth, async (req, res) => {
  try {
    const { content, fromLanguage, toLanguage } = req.body;
    const startTime = Date.now();

    const result = await aiService.translateContent(
      content,
      fromLanguage,
      toLanguage
    );

    const duration = Date.now() - startTime;
    logHelpers.logAiPerformance(
      "content_translation",
      duration,
      "gemini-flash-1.5"
    );

    res.json({
      success: true,
      data: result,
      performance: {
        duration: `${duration}ms`,
      },
    });
  } catch (error) {
    logHelpers.logAiOperation("content_translation", "error", error.message);
    res.status(500).json({
      success: false,
      error: "Translation failed",
      details: error.message,
    });
  }
});

module.exports = router;
