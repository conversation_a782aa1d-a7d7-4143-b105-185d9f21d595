// Test script for price extraction with the improved logic
const testPrices = [
  '€3,950 per month',
  '€ 4 per maand',
  '€3.950 per maand',
  '€3,950',
  '€ 3,950 per month',
  '€ 3.950,00 per maand',
  '€ 1.234.567,89 per maand'
];

function extractPrice(priceString) {
  console.log(`\nTesting price: "${priceString}"`);
  
  // Clean up HTML entities, non-breaking spaces, and extra content
  let normalizedPrice = priceString
    .replace(/&nbsp;/g, " ")
    .replace(/\u00A0/g, " ") // Non-breaking space character (160)
    .replace(/\s+/g, " ")
    .trim();

  // Extract only the price part (before any newlines or extra content)
  const priceLineMatch = normalizedPrice.match(/^([^\\n]+)/);
  if (priceLineMatch) {
    normalizedPrice = priceLineMatch[1].trim();
  }

  // Handle common Dutch price formats
  if (
    normalizedPrice.toLowerCase().includes("op aanvraag") ||
    normalizedPrice.toLowerCase().includes("on request")
  ) {
    normalizedPrice = "Prijs op aanvraag";
    console.log(`Result: ${normalizedPrice}`);
    return normalizedPrice;
  } else {
    // Extract numeric value and format consistently
    const priceMatch = normalizedPrice.match(/€\s*([\d.,]+)/);
    if (priceMatch) {
      // Handle European number formats where comma can be thousands separator
      let extractedPrice = priceMatch[1].trim();
      
      console.log(`Extracted: ${extractedPrice}`);
      
      let numericPrice;
      
      // Handle European number format with both thousands separator and decimal (e.g., 3.950,00)
      if (extractedPrice.match(/\d{1,3}[.]\d{3},[0-9]{2}$/)) {
        console.log('Format detected: European with thousands separator and decimal comma');
        // Format: 3.950,00 (period as thousands, comma as decimal)
        numericPrice = parseFloat(
          extractedPrice.replace(/\./g, '').replace(',', '.')
        );
      }
      // Handle format with thousands separator (e.g., 3,950 or 3.950)
      else if (extractedPrice.match(/\d{1,3}[,.]\d{3}$/)) {
        console.log('Format detected: Thousands separator format');
        numericPrice = parseFloat(extractedPrice.replace(/[.,]/g, ''));
      }
      // Regular decimal format
      else {
        console.log('Format detected: Regular decimal format');
        numericPrice = parseFloat(extractedPrice.replace(',', '.'));
      }
      
      console.log(`Numeric: ${numericPrice}`);
      
      if (numericPrice > 0) {
        const formattedPrice = `€ ${Math.round(numericPrice).toLocaleString('nl-NL')} per maand`;
        console.log(`Result: ${formattedPrice}`);
        return formattedPrice;
      }
    }
  }
  
  console.log("Failed to extract price");
  return "Prijs op aanvraag";
}

// Test all price formats
testPrices.forEach(price => extractPrice(price));
