const cheerio = require("cheerio");
const Listing = require("../../models/Listing");
const { sendAlerts } = require("../alertService");
const {
  browserPool,
  validateAndNormalizeListing,
  setupPageStealth,
  autoScroll,
  getRandomDelay,
  scrapingMetrics,
  isRetryableError
} = require("../scraperUtils");

// Helper function to fetch detailed listing information
const fetchListingDetails = async (browser, url) => {
  let detailPage = null;
  try {
    detailPage = await browser.newPage();
    await setupPageStealth(detailPage);

    // Set cookies to avoid cookie banners
    await detailPage.setCookie({
      name: "cookie_consent",
      value: "accepted",
      domain: ".huurwoningen.nl",
      httpOnly: false,
      secure: true,
    });

    await detailPage.goto(url, {
      waitUntil: "networkidle2",
      timeout: 30000,
    });

    // Wait for content to load
    await new Promise((r) => setTimeout(r, 2000));
    
    // Scroll to load all content
    await autoScroll(detailPage);

    const detailHtml = await detailPage.content();
    const $ = cheerio.load(detailHtml);

    // Initialize all possible fields
    let price = null;
    let size = null;
    let bedrooms = null;
    let rooms = null;
    let description = null;
    let year = null;
    let interior = null;
    let propertyType = "woning";
    let energyLabel = null;
    let availableFrom = null;
    let garden = null;
    let balcony = null;
    let parking = null;
    let heating = null;
    let isolation = null;

    // Extract price from the main price element
    const priceElement = $(".price");
    if (priceElement.length) {
      price = priceElement.text().trim();
      console.log(`Found price: ${price}`);
    }

    // Extract description
    const descriptionElement = $(".description");
    if (descriptionElement.length) {
      description = descriptionElement.text().trim()
        .replace(/\s+/g, ' ')
        .substring(0, 1000); // Limit description length
      console.log(`Found description: ${description.substring(0, 50)}...`);
    }

    // Extract property details from specifications sections
    const detailSections = [
      ".property-features", 
      ".property-info", 
      ".property-details",
      ".listing-features",
      ".listing-details"
    ];

    for (const sectionSelector of detailSections) {
      const section = $(sectionSelector);
      if (!section.length) continue;

      // Look for detail rows/items in this section
      section.find("li, .feature-item, .detail-item, .property-feature").each((i, el) => {
        const text = $(el).text().trim();
        
        // Size
        const sizeMatch = text.match(/(\d+)\s*m²|woonoppervlakte:\s*(\d+)\s*m²/i);
        if (sizeMatch && !size) {
          size = `${sizeMatch[1] || sizeMatch[2]} m²`;
          console.log(`Found size: ${size}`);
        }

        // Rooms
        const roomMatch = text.match(/(\d+)\s*kamer|kamers:\s*(\d+)/i);
        if (roomMatch && !rooms) {
          rooms = roomMatch[1] || roomMatch[2];
          console.log(`Found rooms: ${rooms}`);
        }

        // Bedrooms
        const bedroomMatch = text.match(/(\d+)\s*slaapkamer|slaapkamers:\s*(\d+)/i);
        if (bedroomMatch && !bedrooms) {
          bedrooms = bedroomMatch[1] || bedroomMatch[2];
          console.log(`Found bedrooms: ${bedrooms}`);
        }

        // Construction year
        const yearMatch = text.match(/bouwjaar:\s*(\d{4})|\b(19|20)\d{2}\b/i);
        if (yearMatch && !year) {
          year = yearMatch[1] || yearMatch[2];
          console.log(`Found year: ${year}`);
        }

        // Interior
        if (!interior) {
          if (text.toLowerCase().includes("gemeubileerd") || text.toLowerCase().includes("furnished")) {
            interior = "Gemeubileerd";
            console.log(`Found interior: ${interior}`);
          } else if (text.toLowerCase().includes("gestoffeerd") || text.toLowerCase().includes("semi-furnished")) {
            interior = "Gestoffeerd";
            console.log(`Found interior: ${interior}`);
          } else if (text.toLowerCase().includes("kaal") || text.toLowerCase().includes("unfurnished")) {
            interior = "Kaal";
            console.log(`Found interior: ${interior}`);
          }
        }

        // Property type
        if (text.toLowerCase().includes("appartement")) {
          propertyType = "appartement";
          console.log(`Found property type: ${propertyType}`);
        } else if (text.toLowerCase().includes("huis") || text.toLowerCase().includes("woning")) {
          propertyType = "huis";
          console.log(`Found property type: ${propertyType}`);
        } else if (text.toLowerCase().includes("kamer") && !text.toLowerCase().includes("slaapkamer")) {
          propertyType = "kamer";
          console.log(`Found property type: ${propertyType}`);
        } else if (text.toLowerCase().includes("studio")) {
          propertyType = "studio";
          console.log(`Found property type: ${propertyType}`);
        }

        // Energy label
        const energyLabelMatch = text.match(/energielabel:\s*([A-G][+\-]?)/i);
        if (energyLabelMatch && !energyLabel) {
          energyLabel = energyLabelMatch[1];
          console.log(`Found energy label: ${energyLabel}`);
        }

        // Available from
        const availableMatch = text.match(/beschikbaar vanaf:\s*([\d\-\s\/\w]+)/i);
        if (availableMatch && !availableFrom) {
          availableFrom = availableMatch[1].trim();
          console.log(`Found availability: ${availableFrom}`);
        }

        // Garden
        if (text.toLowerCase().includes("tuin") && !garden) {
          garden = "Ja";
          console.log(`Found garden: ${garden}`);
        }

        // Balcony
        if (text.toLowerCase().includes("balkon") && !balcony) {
          balcony = "Ja";
          console.log(`Found balcony: ${balcony}`);
        }

        // Parking
        if ((text.toLowerCase().includes("parkeer") || text.toLowerCase().includes("garage")) && !parking) {
          parking = "Ja";
          console.log(`Found parking: ${parking}`);
        }

        // Heating
        const heatingMatch = text.match(/verwarming:\s*([\w\s\-]+)/i);
        if (heatingMatch && !heating) {
          heating = heatingMatch[1].trim();
          console.log(`Found heating: ${heating}`);
        }

        // Isolation
        const isolationMatch = text.match(/isolatie:\s*([\w\s\-,]+)/i);
        if (isolationMatch && !isolation) {
          isolation = isolationMatch[1].trim();
          console.log(`Found isolation: ${isolation}`);
        }
      });
    }

    return { 
      price, 
      size, 
      bedrooms, 
      rooms,
      description,
      year,
      interior,
      propertyType,
      energyLabel,
      availableFrom,
      garden,
      balcony,
      parking,
      heating,
      isolation
    };
  } catch (error) {
    console.log(`Error fetching details for ${url}:`, error.message);
    return { 
      price: null, 
      size: null, 
      bedrooms: null,
      rooms: null,
      description: null,
      year: null,
      interior: null
    };
  } finally {
    if (detailPage) {
      await detailPage.close();
    }
  }
};

const scrapeHuurwoningen = async (retryCount = 0, maxRetries = 3) => {
  if (retryCount === 0) {
    scrapingMetrics.recordScrapeStart();
  }

  let browser = null;
  let page = null;
  let listingsSaved = 0;
  let duplicatesSkipped = 0;

  try {
    browser = await browserPool.getBrowser();
    page = await browser.newPage();

    await setupPageStealth(page);

    // Set cookies to avoid cookie banners
    await page.setCookie({
      name: "cookie_consent",
      value: "accepted",
      domain: ".huurwoningen.nl",
      httpOnly: false,
      secure: true,
    });

    const listings = [];

    // Scrape multiple cities for better coverage
    const searchUrls = [
      "https://www.huurwoningen.nl/in/amsterdam/",
      "https://www.huurwoningen.nl/in/rotterdam/",
      "https://www.huurwoningen.nl/in/den-haag/",
      "https://www.huurwoningen.nl/in/utrecht/",
      "https://www.huurwoningen.nl/in/eindhoven/",
      "https://www.huurwoningen.nl/in/groningen/",
    ];

    for (const searchUrl of searchUrls) {
      try {
        console.log(`Scraping Huurwoningen: ${searchUrl}`);

        await page.goto(searchUrl, {
          waitUntil: "networkidle2",
          timeout: 60000,
        });

        // Add random delay
        await new Promise((resolve) =>
          setTimeout(resolve, getRandomDelay(2000, 4000))
        );

        // Scroll to load more content
        await autoScroll(page);

        const html = await page.content();
        const $ = cheerio.load(html);

        // Extract listings from the page
        const cityListings = [];

        // Look for listing containers - they appear to be in a specific structure
        $("main section").each((sectionIndex, section) => {
          $(section)
            .find("div")
            .each((divIndex, element) => {
              const $element = $(element);

              // Check if this div contains a listing link
              const linkElement = $element.find('a[href*="/huren/"]').first();
              if (!linkElement.length) return;

              const href = linkElement.attr("href");
              if (!href) return;

              // Extract title from the link text or nearby heading
              let title = linkElement.text().trim();
              if (!title) {
                title = $element.find("h2, h3").first().text().trim();
              }
              if (!title) return;

              // Extract location - look for text patterns that indicate location
              let location = "";
              $element.find("*").each((i, el) => {
                const text = $(el).text().trim();
                // Look for postal code patterns (4 digits + 2 letters + city name)
                const locationMatch = text.match(
                  /(\d{4}\s*[A-Z]{2})\s+([A-Za-z\s\-]+)/
                );
                if (locationMatch && !location) {
                  location = `${locationMatch[1]} ${locationMatch[2]}`.trim();
                }
              });

              // Extract price
              let price = "";
              $element.find("*").each((i, el) => {
                const text = $(el).text().trim();
                const priceMatch = text.match(/€\s*[\d.,]+\s*per\s*maand/i);
                if (priceMatch && !price) {
                  price = priceMatch[0];
                }
              });

              // Extract additional details
              let size = null;
              let rooms = null;
              let year = null;
              let interior = null;

              $element.find("li, span, div").each((i, el) => {
                const text = $(el).text().trim();

                // Size in m²
                const sizeMatch = text.match(/(\d+)\s*m²/);
                if (sizeMatch && !size) {
                  size = `${sizeMatch[1]} m²`;
                }

                // Number of rooms/bedrooms
                const roomMatch = text.match(/(\d+)\s*kamer/);
                if (roomMatch && !rooms) {
                  rooms = `${roomMatch[1]} kamers`;
                }

                // Build year
                const yearMatch = text.match(/(19|20)\d{2}/);
                if (yearMatch && !year) {
                  year = yearMatch[0];
                }

                // Interior type
                if (text.includes("Kaal") && !interior) interior = "Kaal";
                if (text.includes("Gestoffeerd") && !interior)
                  interior = "Gestoffeerd";
                if (text.includes("Gemeubileerd") && !interior)
                  interior = "Gemeubileerd";
              });

              // Build full URL
              const url = href.startsWith("http")
                ? href
                : `https://www.huurwoningen.nl${href}`;

              // Determine property type from title
              let propertyType = "woning";
              const titleLower = title.toLowerCase();
              if (titleLower.includes("appartement"))
                propertyType = "appartement";
              else if (titleLower.includes("huis")) propertyType = "huis";
              else if (titleLower.includes("kamer")) propertyType = "kamer";
              else if (titleLower.includes("studio")) propertyType = "studio";

              if (title && url && location) {
                const listingData = validateAndNormalizeListing({
                  title,
                  price: price || "Prijs op aanvraag",
                  location,
                  url,
                  propertyType,
                  size,
                  rooms,
                  year,
                  interior,
                  source: "huurwoningen.nl",
                });

                if (listingData) {
                  console.log(
                    `Huurwoningen found: ${listingData.title} - ${listingData.price} - ${listingData.location}`
                  );
                  cityListings.push(listingData);
                }
              }
            });
        });

        listings.push(...cityListings);
        console.log(`Found ${cityListings.length} listings from ${searchUrl}`);

        // Add delay between cities
        await new Promise((resolve) =>
          setTimeout(resolve, getRandomDelay(3000, 6000))
        );
      } catch (cityError) {
        console.error(`Error scraping ${searchUrl}:`, cityError.message);
        // Continue with other cities even if one fails
      }
    }

    console.log(`Found ${listings.length} total listings from Huurwoningen.`);

    // Enrich listings with detailed information
    const enrichedListings = [];
    for (const listingData of listings) {
      try {
        console.log(`Fetching details for: ${listingData.title} (${listingData.url})`);
        
        // Add random delay between requests
        await new Promise(resolve => setTimeout(resolve, getRandomDelay(1500, 3000)));
        
        // Fetch detailed information
        const details = await fetchListingDetails(browser, listingData.url);
        
        // Merge details with basic listing data
        if (details.price) listingData.price = details.price;
        if (details.size) listingData.size = details.size;
        if (details.bedrooms) listingData.bedrooms = details.bedrooms;
        if (details.rooms) listingData.rooms = details.rooms;
        if (details.description) listingData.description = details.description;
        if (details.year) listingData.year = details.year;
        if (details.interior) listingData.interior = details.interior;
        if (details.propertyType) listingData.propertyType = details.propertyType;
        
        // Store extended details as JSON
        const extendedDetails = {};
        if (details.energyLabel) extendedDetails.energyLabel = details.energyLabel;
        if (details.availableFrom) extendedDetails.availableFrom = details.availableFrom;
        if (details.garden) extendedDetails.garden = details.garden;
        if (details.balcony) extendedDetails.balcony = details.balcony;
        if (details.parking) extendedDetails.parking = details.parking;
        if (details.heating) extendedDetails.heating = details.heating;
        if (details.isolation) extendedDetails.isolation = details.isolation;
        
        // Only add extendedDetails if we have any
        if (Object.keys(extendedDetails).length > 0) {
          listingData.extendedDetails = JSON.stringify(extendedDetails);
        }
        
        // Validate and normalize the enriched listing
        const enrichedListing = validateAndNormalizeListing(listingData);
        if (enrichedListing) {
          console.log(`Enriched listing: ${enrichedListing.title}`);
          enrichedListings.push(enrichedListing);
        }
      } catch (detailError) {
        console.error(`Error enriching listing ${listingData.title}:`, detailError.message);
        // Still add the basic listing if we couldn't get details
        enrichedListings.push(listingData);
      }
    }
    
    console.log(`Enriched ${enrichedListings.length} listings with details.`);

    // Process listings with better error handling
    for (const listingData of enrichedListings) {
      try {
        const newListing = new Listing(listingData);
        await newListing.save();
        console.log(`Saved listing: ${newListing.title}`);
        listingsSaved++;
        sendAlerts(newListing);
      } catch (error) {
        if (error.code === 11000) {
          // Duplicate key error
          console.log(`Skipping duplicate listing: ${listingData.title}`);
          duplicatesSkipped++;
        } else {
          console.error(`Error saving listing ${listingData.title}:`, error);
        }
      }
    }

    scrapingMetrics.recordScrapeSuccess(
      listings.length,
      listingsSaved,
      duplicatesSkipped
    );
    return listings;
  } catch (error) {
    console.error(
      `Error during Huurwoningen scraping (attempt ${retryCount + 1}/${
        maxRetries + 1
      }):`,
      error
    );

    // Record failure only on first attempt
    if (retryCount === 0) {
      scrapingMetrics.recordScrapeFailure(error);
    }

    // Retry logic for transient errors
    if (retryCount < maxRetries && isRetryableError(error)) {
      console.log(
        `Retrying Huurwoningen scraping in ${(retryCount + 1) * 5} seconds...`
      );
      await new Promise((resolve) =>
        setTimeout(resolve, (retryCount + 1) * 5000)
      );
      return scrapeHuurwoningen(retryCount + 1, maxRetries);
    }

    console.log(
      `Huurwoningen scraping failed after ${
        maxRetries + 1
      } attempts. Saved: ${listingsSaved}, Duplicates: ${duplicatesSkipped}`
    );
    return [];
  } finally {
    // Close the page to free up resources
    if (page) {
      try {
        await page.close();
      } catch (closeError) {
        console.error("Error closing Huurwoningen page:", closeError);
      }
    }
    console.log(
      `Huurwoningen scraping completed. Saved: ${listingsSaved}, Duplicates: ${duplicatesSkipped}`
    );
  }
};

module.exports = {
  scrapeHuurwoningen,
  fetchListingDetails
};
