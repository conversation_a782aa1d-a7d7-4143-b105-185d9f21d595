{
  component: 'database',
  service: 'zakmakelaar-api',
  operation: 'stats',
  collection: 'listings',
  duration: '10ms',
  error: 'Failed to optimize pipeline :: caused by :: Invalid Regex in $regexFind: PCRE2 does not support \\F, \\L, \\l, \\N{name}, \\U, or \\u',
  stack: 'MongoServerError: Failed to optimize pipeline :: caused by :: Invalid Regex in $regexFind: PCRE2 does not support \\F, \\L, \\l, \\N{name}, \\U, or \\u\n' +
    '    at Connection.sendCommand (C:\\Users\\<USER>\\Documents\\FREELANCE\\ZAKMAKELAAR\\zakmakelaar-backend\\node_modules\\mongodb\\lib\\cmap\\connection.js:305:27)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n' +
    '    at async Connection.command (C:\\Users\\<USER>\\Documents\\FREELANCE\\ZAKMAKELAAR\\zakmakelaar-backend\\node_modules\\mongodb\\lib\\cmap\\connection.js:333:26)\n' +
    '    at async Server.command (C:\\Users\\<USER>\\Documents\\FREELANCE\\ZAKMAKELAAR\\zakmakelaar-backend\\node_modules\\mongodb\\lib\\sdam\\server.js:171:29)\n' +
    '    at async AggregateOperation.executeCommand (C:\\Users\\<USER>\\Documents\\FREELANCE\\ZAKMAKELAAR\\zakmakelaar-backend\\node_modules\\mongodb\\lib\\operations\\command.js:76:16)\n' +
    '    at async AggregateOperation.execute (C:\\Users\\<USER>\\Documents\\FREELANCE\\ZAKMAKELAAR\\zakmakelaar-backend\\node_modules\\mongodb\\lib\\operations\\aggregate.js:86:16)\n' +
    '    at async tryOperation (C:\\Users\\<USER>\\Documents\\FREELANCE\\ZAKMAKELAAR\\zakmakelaar-backend\\node_modules\\mongodb\\lib\\operations\\execute_operation.js:207:20)\n' +
    '    at async executeOperation (C:\\Users\\<USER>\\Documents\\FREELANCE\\ZAKMAKELAAR\\zakmakelaar-backend\\node_modules\\mongodb\\lib\\operations\\execute_operation.js:75:16)\n' +
    '    at async AggregationCursor._initialize (C:\\Users\\<USER>\\Documents\\FREELANCE\\ZAKMAKELAAR\\zakmakelaar-backend\\node_modules\\mongodb\\lib\\cursor\\aggregation_cursor.js:56:26)\n' +
    '    at async AggregationCursor.cursorInit (C:\\Users\\<USER>\\Documents\\FREELANCE\\ZAKMAKELAAR\\zakmakelaar-backend\\node_modules\\mongodb\\lib\\cursor\\abstract_cursor.js:633:27)',
  level: 'error',
  message: 'Database Operation Failed',
  timestamp: '2025-07-14 21:26:17'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  duration: '1ms',
  error: 'scrapeFunda is not a function',
  stack: 'TypeError: scrapeFunda is not a function\n' +
    '    at Job.job (C:\\Users\\<USER>\\Documents\\FREELANCE\\ZAKMAKELAAR\\zakmakelaar-backend\\src\\index.js:408:9)\n' +
    '    at Job.invoke (C:\\Users\\<USER>\\Documents\\FREELANCE\\ZAKMAKELAAR\\zakmakelaar-backend\\node_modules\\node-schedule\\lib\\Job.js:171:15)\n' +
    '    at C:\\Users\\<USER>\\Documents\\FREELANCE\\ZAKMAKELAAR\\zakmakelaar-backend\\node_modules\\node-schedule\\lib\\Invocation.js:268:28\n' +
    '    at Timeout._onTimeout (C:\\Users\\<USER>\\Documents\\FREELANCE\\ZAKMAKELAAR\\zakmakelaar-backend\\node_modules\\node-schedule\\lib\\Invocation.js:228:7)\n' +
    '    at listOnTimeout (node:internal/timers:594:17)\n' +
    '    at process.processTimers (node:internal/timers:529:7)',
  level: 'error',
  message: 'Scheduled scraping failed',
  timestamp: '2025-07-14 22:00:00'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  duration: '1ms',
  error: 'scrapeFunda is not a function',
  stack: 'TypeError: scrapeFunda is not a function\n' +
    '    at Job.job (C:\\Users\\<USER>\\Documents\\FREELANCE\\ZAKMAKELAAR\\zakmakelaar-backend\\src\\index.js:408:9)\n' +
    '    at Job.invoke (C:\\Users\\<USER>\\Documents\\FREELANCE\\ZAKMAKELAAR\\zakmakelaar-backend\\node_modules\\node-schedule\\lib\\Job.js:171:15)\n' +
    '    at C:\\Users\\<USER>\\Documents\\FREELANCE\\ZAKMAKELAAR\\zakmakelaar-backend\\node_modules\\node-schedule\\lib\\Invocation.js:268:28\n' +
    '    at Timeout._onTimeout (C:\\Users\\<USER>\\Documents\\FREELANCE\\ZAKMAKELAAR\\zakmakelaar-backend\\node_modules\\node-schedule\\lib\\Invocation.js:228:7)\n' +
    '    at listOnTimeout (node:internal/timers:594:17)\n' +
    '    at process.processTimers (node:internal/timers:529:7)',
  level: 'error',
  message: 'Scheduled scraping failed',
  timestamp: '2025-07-14 22:05:00'
}
{
  component: 'database',
  service: 'zakmakelaar-api',
  operation: 'stats',
  collection: 'listings',
  duration: '2ms',
  error: 'Failed to optimize pipeline :: caused by :: Invalid Regex in $regexFind: PCRE2 does not support \\F, \\L, \\l, \\N{name}, \\U, or \\u',
  stack: 'MongoServerError: Failed to optimize pipeline :: caused by :: Invalid Regex in $regexFind: PCRE2 does not support \\F, \\L, \\l, \\N{name}, \\U, or \\u\n' +
    '    at Connection.sendCommand (C:\\Users\\<USER>\\Documents\\FREELANCE\\ZAKMAKELAAR\\zakmakelaar-backend\\node_modules\\mongodb\\lib\\cmap\\connection.js:305:27)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n' +
    '    at async Connection.command (C:\\Users\\<USER>\\Documents\\FREELANCE\\ZAKMAKELAAR\\zakmakelaar-backend\\node_modules\\mongodb\\lib\\cmap\\connection.js:333:26)\n' +
    '    at async Server.command (C:\\Users\\<USER>\\Documents\\FREELANCE\\ZAKMAKELAAR\\zakmakelaar-backend\\node_modules\\mongodb\\lib\\sdam\\server.js:171:29)\n' +
    '    at async AggregateOperation.executeCommand (C:\\Users\\<USER>\\Documents\\FREELANCE\\ZAKMAKELAAR\\zakmakelaar-backend\\node_modules\\mongodb\\lib\\operations\\command.js:76:16)\n' +
    '    at async AggregateOperation.execute (C:\\Users\\<USER>\\Documents\\FREELANCE\\ZAKMAKELAAR\\zakmakelaar-backend\\node_modules\\mongodb\\lib\\operations\\aggregate.js:86:16)\n' +
    '    at async tryOperation (C:\\Users\\<USER>\\Documents\\FREELANCE\\ZAKMAKELAAR\\zakmakelaar-backend\\node_modules\\mongodb\\lib\\operations\\execute_operation.js:207:20)\n' +
    '    at async executeOperation (C:\\Users\\<USER>\\Documents\\FREELANCE\\ZAKMAKELAAR\\zakmakelaar-backend\\node_modules\\mongodb\\lib\\operations\\execute_operation.js:75:16)\n' +
    '    at async AggregationCursor._initialize (C:\\Users\\<USER>\\Documents\\FREELANCE\\ZAKMAKELAAR\\zakmakelaar-backend\\node_modules\\mongodb\\lib\\cursor\\aggregation_cursor.js:56:26)\n' +
    '    at async AggregationCursor.cursorInit (C:\\Users\\<USER>\\Documents\\FREELANCE\\ZAKMAKELAAR\\zakmakelaar-backend\\node_modules\\mongodb\\lib\\cursor\\abstract_cursor.js:633:27)',
  level: 'error',
  message: 'Database Operation Failed',
  timestamp: '2025-07-14 23:08:31'
}
