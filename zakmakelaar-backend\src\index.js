const express = require("express");
const mongoose = require("mongoose");
const schedule = require("node-schedule");
const swaggerJsdoc = require("swagger-jsdoc");
const swaggerUi = require("swagger-ui-express");
const cors = require("cors");
const helmet = require("helmet");
const morgan = require("morgan");
const path = require("path");

const config = require("./config/config");
const { connectDB } = require("./config/database");
const authRoutes = require("./routes/auth");
const scraperRoutes = require("./routes/scraper");
const listingRoutes = require("./routes/listing");
const {
  scrapePararius,
  scrapeFunda,
  scrapeHuurwoningen,
  cleanup,
} = require("./services/scraper");
const { loggers, requestLogger } = require("./services/logger");
const cacheService = require("./services/cacheService");

// Import middleware
const { globalErrorHandler, AppError } = require("./middleware/errorHandler");
const { generalLimiter } = require("./middleware/rateLimiter");

const app = express();
const port = config.port;

// Security middleware
app.use(helmet()); // Set security headers
app.use(
  cors({
    origin: config.corsOrigin,
    credentials: true,
  })
);

// Rate limiting
app.use(generalLimiter);

// Logging middleware - use our custom logger
app.use(requestLogger);

// Body parsing middleware
app.use(express.json({ limit: "10mb" }));
app.use(express.urlencoded({ extended: true, limit: "10mb" }));

// Connect to database
connectDB();

// Swagger definition
const swaggerOptions = {
  swaggerDefinition: {
    openapi: "3.0.0",
    info: {
      title: "ZakMakelaar API Documentation",
      version: "1.0.0",
      description:
        "Real Estate Listings API with advanced search, caching, and monitoring capabilities",
      contact: {
        name: "ZakMakelaar API Support",
        email: "<EMAIL>",
      },
    },
    servers: [
      {
        url: `http://localhost:${port}`,
        description: "Development server",
      },
    ],
    tags: [
      {
        name: "System",
        description: "System health and status endpoints",
      },
      {
        name: "Listings",
        description: "Property listings management and search",
      },
      {
        name: "Search",
        description: "Advanced search functionality and suggestions",
      },
      {
        name: "Authentication",
        description: "User authentication and profile management",
      },
      {
        name: "Scraper",
        description: "Web scraping operations for property data",
      },
      {
        name: "Agent",
        description: "AI Agent management and monitoring",
      },
      {
        name: "AI",
        description:
          "AI-powered features including matching, analysis, and automation",
      },
      {
        name: "Monitoring",
        description: "System monitoring and performance metrics",
      },
    ],
    components: {
      securitySchemes: {
        bearerAuth: {
          type: "http",
          scheme: "bearer",
          bearerFormat: "JWT",
        },
      },
      schemas: {
        User: {
          type: "object",
          properties: {
            _id: { type: "string" },
            email: { type: "string", format: "email" },
            role: {
              type: "string",
              enum: ["user", "admin"],
              default: "user",
            },
            preferences: {
              type: "object",
              properties: {
                location: { type: "string" },
                budget: { type: "number" },
                rooms: { type: "number" },
                propertyType: {
                  type: "string",
                  enum: ["apartment", "house", "studio", "room", "any"],
                },
                minSize: { type: "number" },
                maxSize: { type: "number" },
                interior: {
                  type: "string",
                  enum: ["kaal", "gestoffeerd", "gemeubileerd", "any"],
                },
                parking: { type: "boolean" },
                balcony: { type: "boolean" },
                garden: { type: "boolean" },
                furnished: { type: "boolean" },
                petsAllowed: { type: "boolean" },
                smokingAllowed: { type: "boolean" },
                studentFriendly: { type: "boolean" },
                expatFriendly: { type: "boolean" },
                commuteTime: { type: "number" },
                preferredNeighborhoods: {
                  type: "array",
                  items: { type: "string" },
                },
                excludedNeighborhoods: {
                  type: "array",
                  items: { type: "string" },
                },
              },
            },
            aiSettings: {
              type: "object",
              properties: {
                matchThreshold: { type: "number", default: 70 },
                alertFrequency: {
                  type: "string",
                  enum: ["immediate", "hourly", "daily"],
                  default: "immediate",
                },
                preferredLanguage: {
                  type: "string",
                  enum: ["dutch", "english"],
                  default: "english",
                },
                includeMarketAnalysis: { type: "boolean", default: true },
                includeContractAnalysis: { type: "boolean", default: true },
                autoGenerateApplications: { type: "boolean", default: false },
                applicationTemplate: {
                  type: "string",
                  enum: ["professional", "casual", "student", "expat"],
                  default: "professional",
                },
              },
            },
            createdAt: { type: "string", format: "date-time" },
            lastActive: { type: "string", format: "date-time" },
          },
        },
        Listing: {
          type: "object",
          properties: {
            _id: { type: "string" },
            title: { type: "string" },
            price: { type: "string" },
            location: { type: "string" },
            url: { type: "string" },
            propertyType: { type: "string" },
            size: { type: "string" },
            rooms: { type: "string" },
            year: { type: "string" },
            interior: { type: "string" },
            source: { type: "string" },
            description: { type: "string" },
            timestamp: { type: "string", format: "date-time" },
          },
        },
        AgentStatus: {
          type: "object",
          properties: {
            isActive: { type: "boolean" },
            lastActivity: { type: "string", format: "date-time" },
            currentTask: { type: "string" },
            autonomyLevel: { type: "number", minimum: 0, maximum: 100 },
            performance: {
              type: "object",
              properties: {
                totalApplications: { type: "number" },
                successRate: { type: "number" },
                averageResponseTime: { type: "number" },
                autonomyRate: { type: "number" },
                userSatisfactionScore: { type: "number" },
              },
            },
            lastUpdated: { type: "string", format: "date-time" },
          },
        },
        AgentMetrics: {
          type: "object",
          properties: {
            totalScrapes: { type: "number" },
            successfulScrapes: { type: "number" },
            failedScrapes: { type: "number" },
            successRate: { type: "number" },
            averageScrapingTime: { type: "number" },
            uptime: { type: "number" },
            lastScrapeTime: { type: "string", format: "date-time" },
            autonomyLevel: { type: "number" },
            autonomyRate: { type: "number" },
          },
        },
        Error: {
          type: "object",
          properties: {
            status: { type: "string", example: "error" },
            message: { type: "string" },
            statusCode: { type: "number" },
          },
        },
      },
    },
  },
  apis: ["./src/routes/*.js"], // Path to the API docs
};

const swaggerDocs = swaggerJsdoc(swaggerOptions);
app.use("/api-docs", swaggerUi.serve, swaggerUi.setup(swaggerDocs));

// All routes enabled
app.use("/api/auth", authRoutes);
app.use("/api", scraperRoutes);
app.use("/api", listingRoutes);
app.use("/api/ai", require("./routes/ai"));
app.use("/api/agent", require("./routes/agent"));
app.use("/api/monitoring", require("./routes/monitoring"));

// Serve static files from public directory
app.use(express.static(path.join(__dirname, "../public")));

// Monitoring dashboard route
app.get("/monitoring", (req, res) => {
  res.sendFile(path.join(__dirname, "../public/monitoring-dashboard.html"));
});

// Health check endpoint
/**
 * @swagger
 * /:
 *   get:
 *     summary: API status and information
 *     tags: [System]
 *     responses:
 *       200:
 *         description: API is running successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 message:
 *                   type: string
 *                   example: ZakMakelaar API is running!
 *                 version:
 *                   type: string
 *                   example: 1.0.0
 *                 environment:
 *                   type: string
 *                   example: development
 *                 timestamp:
 *                   type: string
 *                   format: date-time
 *                   description: Current server timestamp
 */
app.get("/", (req, res) => {
  res.json({
    status: "success",
    message: "ZakMakelaar API is running!",
    version: "1.0.0",
    environment: config.nodeEnv,
    timestamp: new Date().toISOString(),
  });
});

/**
 * @swagger
 * /health:
 *   get:
 *     summary: Comprehensive health check
 *     description: Returns detailed health information about all system components including database, cache, and services
 *     tags: [System]
 *     responses:
 *       200:
 *         description: System health information
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: healthy
 *                 uptime:
 *                   type: number
 *                   description: Server uptime in seconds
 *                 timestamp:
 *                   type: string
 *                   format: date-time
 *                   description: Current server timestamp
 *                 database:
 *                   type: string
 *                   enum: [connected, disconnected]
 *                   description: MongoDB connection status
 *                 cache:
 *                   type: string
 *                   enum: [connected, disconnected]
 *                   description: Redis cache connection status
 *                 services:
 *                   type: object
 *                   properties:
 *                     database:
 *                       type: boolean
 *                       description: Database service availability
 *                     cache:
 *                       type: boolean
 *                       description: Cache service availability
 *                     scraper:
 *                       type: boolean
 *                       description: Scraper service availability
 *       500:
 *         description: Health check failed
 */
app.get("/health", async (req, res) => {
  const cacheStats = await cacheService.getStats();

  res.json({
    status: "healthy",
    uptime: process.uptime(),
    timestamp: new Date().toISOString(),
    database:
      mongoose.connection.readyState === 1 ? "connected" : "disconnected",
    cache: cacheStats ? "connected" : "disconnected",
    services: {
      database: mongoose.connection.readyState === 1,
      cache: cacheStats !== null,
      scraper: true, // Always true if server is running
    },
  });
});

// Handle undefined routes
app.all("*", (req, res, next) => {
  next(new AppError(`Can't find ${req.originalUrl} on this server!`, 404));
});

// Global error handling middleware (must be last)
app.use(globalErrorHandler);

// Schedule the scraper to run at configured intervals
const scrapingInterval = `*/${config.scrapingIntervalMinutes} * * * *`;
schedule.scheduleJob(scrapingInterval, async () => {
  const startTime = Date.now();
  loggers.scraper.info(
    `Starting scheduled scraping (every ${config.scrapingIntervalMinutes} minutes)`
  );

  try {
    // Run all three scrapers
    const [fundaResult, pariusResult, huurwoningenResult] =
      await Promise.allSettled([
        scrapeFunda(),
        scrapePararius(),
        scrapeHuurwoningen(),
      ]);

    let totalListings = 0;
    if (fundaResult.status === "fulfilled") {
      totalListings += fundaResult.value?.length || 0;
    } else {
      loggers.scraper.error("Funda scraping failed", {
        error: fundaResult.reason,
      });
    }

    if (pariusResult.status === "fulfilled") {
      totalListings += pariusResult.value?.length || 0;
    } else {
      loggers.scraper.error("Pararius scraping failed", {
        error: pariusResult.reason,
      });
    }

    if (huurwoningenResult.status === "fulfilled") {
      totalListings += huurwoningenResult.value?.length || 0;
    } else {
      loggers.scraper.error("Huurwoningen scraping failed", {
        error: huurwoningenResult.reason,
      });
    }

    const result = { length: totalListings };
    const duration = Date.now() - startTime;
    loggers.scraper.info("Scheduled scraping completed", {
      duration: `${duration}ms`,
      listingsProcessed: result?.length || 0,
    });
  } catch (error) {
    const duration = Date.now() - startTime;
    loggers.scraper.error("Scheduled scraping failed", {
      duration: `${duration}ms`,
      error: error.message,
      stack: error.stack,
    });
  }
});

// Graceful shutdown handling
const gracefulShutdown = async (signal) => {
  console.log(`👋 ${signal} RECEIVED. Shutting down gracefully`);

  try {
    // Cleanup scraper resources
    await cleanup();

    // Close server
    server.close(() => {
      console.log("💥 Process terminated!");
      process.exit(0);
    });
  } catch (error) {
    console.error("Error during graceful shutdown:", error);
    process.exit(1);
  }
};

process.on("SIGTERM", () => gracefulShutdown("SIGTERM"));
process.on("SIGINT", () => gracefulShutdown("SIGINT"));

process.on("unhandledRejection", async (err) => {
  console.log("UNHANDLED REJECTION! 💥 Shutting down...");
  console.log(err.name, err.message);

  try {
    await cleanup();
  } catch (cleanupError) {
    console.error("Error during cleanup:", cleanupError);
  }

  server.close(() => {
    process.exit(1);
  });
});

const server = app.listen(port, () => {
  loggers.app.info("🚀 ZakMakelaar API Server Started", {
    port,
    environment: config.nodeEnv,
    endpoints: {
      api: `http://localhost:${port}`,
      docs: `http://localhost:${port}/api-docs`,
      health: `http://localhost:${port}/health`,
    },
  });

  // Console output for development
  if (config.nodeEnv === "development") {
    console.log(`🚀 Server is running on port ${port}`);
    console.log(`📚 API Documentation: http://localhost:${port}/api-docs`);
    console.log(`🏥 Health Check: http://localhost:${port}/health`);
    console.log(`🌍 Environment: ${config.nodeEnv}`);
  }
});
