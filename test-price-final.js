// Test script for the improved price extraction logic
const testPrices = [
  '€3,950 per month',
  '€ 4 per maand',
  '€3.950 per maand',
  '€3,950',
  '€ 3,950 per month',
  '€ 3.950,00 per maand',
  '€ 1.234.567,89 per maand'
];

function extractPrice(priceString) {
  console.log(`\nTesting price: "${priceString}"`);
  
  // Clean up HTML entities, non-breaking spaces, and extra content
  let normalizedPrice = priceString
    .replace(/&nbsp;/g, " ")
    .replace(/\u00A0/g, " ") // Non-breaking space character (160)
    .replace(/\s+/g, " ")
    .trim();

  // Extract only the price part (before any newlines or extra content)
  const priceLineMatch = normalizedPrice.match(/^([^\\n]+)/);
  if (priceLineMatch) {
    normalizedPrice = priceLineMatch[1].trim();
  }

  // Handle common Dutch price formats
  if (
    normalizedPrice.toLowerCase().includes("op aanvraag") ||
    normalizedPrice.toLowerCase().includes("on request")
  ) {
    normalizedPrice = "Prijs op aanvraag";
    console.log(`Result: ${normalizedPrice}`);
    return normalizedPrice;
  } else {
    // Extract numeric value and format consistently
    const priceMatch = normalizedPrice.match(/€\s*([\d.,]+)/);
    if (priceMatch) {
      // Handle European number formats where comma can be thousands separator
      let extractedPrice = priceMatch[1].trim();
      
      console.log(`Extracted: ${extractedPrice}`);
      
      let numericPrice;
      let formatType = '';
      
      // Case 1: Format with multiple thousands separators and decimal comma (e.g., 1.234.567,89)
      if (extractedPrice.match(/\\d{1,3}(\\.\\d{3})+,\\d+$/)) {
        formatType = 'Multiple thousands separators with decimal comma';
        numericPrice = parseFloat(
          extractedPrice.replace(/\\./g, '').replace(',', '.')
        );
      }
      // Case 2: Format with single thousands separator and decimal comma (e.g., 3.950,00)
      else if (extractedPrice.match(/\\d{1,3}\\.\\d{3},\\d+$/)) {
        formatType = 'Single thousands separator with decimal comma';
        numericPrice = parseFloat(
          extractedPrice.replace(/\\./g, '').replace(',', '.')
        );
      }
      // Case 3: Format with comma as thousands separator (e.g., 3,950)
      else if (extractedPrice.match(/\\d{1,3},\\d{3}$/)) {
        formatType = 'Comma as thousands separator';
        numericPrice = parseFloat(extractedPrice.replace(/,/g, ''));
      }
      // Case 4: Format with period as thousands separator (e.g., 3.950)
      else if (extractedPrice.match(/\\d{1,3}\\.\\d{3}$/)) {
        formatType = 'Period as thousands separator';
        numericPrice = parseFloat(extractedPrice.replace(/\\./g, ''));
      }
      // Case 5: Regular decimal format or other formats
      else {
        formatType = 'Regular decimal format or other';
        numericPrice = parseFloat(extractedPrice.replace(',', '.'));
      }
      
      console.log(`Format detected: ${formatType}`);
      console.log(`Numeric: ${numericPrice}`);
      
      if (numericPrice > 0) {
        const formattedPrice = `€ ${Math.round(numericPrice).toLocaleString('nl-NL')} per maand`;
        console.log(`Result: ${formattedPrice}`);
        return formattedPrice;
      }
    }
  }
  
  console.log("Failed to extract price");
  return "Prijs op aanvraag";
}

// Test each price format individually
testPrices.forEach((price, index) => {
  console.log(`\n--- TEST CASE ${index + 1} ---`);
  extractPrice(price);
});
