{"name": "@tanstack/query-codemods", "private": true, "description": "Collection of codemods to make the migration easier.", "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/TanStack/query.git", "directory": "packages/query-codemods"}, "homepage": "https://tanstack.com/query", "funding": {"type": "github", "url": "https://github.com/sponsors/tanner<PERSON>ley"}, "scripts": {"clean": "premove ./coverage ./dist-ts", "test:eslint": "eslint ./src", "test:lib": "vitest", "test:lib:dev": "pnpm run test:lib --watch"}, "type": "module", "exports": {"./package.json": "./package.json"}, "sideEffects": false, "files": ["src", "!src/jest.config.js", "!src/**/__testfixtures__", "!src/**/__tests__"], "devDependencies": {"@types/jscodeshift": "17.3.0", "jscodeshift": "17.3.0"}}