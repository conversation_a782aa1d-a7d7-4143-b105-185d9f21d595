const mongoose = require("mongoose");
const { scrapeHuurwoningen } = require("./services/scraper");
const config = require("./config/config");

// Connect to MongoDB
mongoose
  .connect(config.mongoURI, {
    maxPoolSize: 10,
    serverSelectionTimeoutMS: 5000,
    socketTimeoutMS: 45000,
  })
  .then(() => {
    console.log("✅ MongoDB Connected for testing");
  })
  .catch((err) => {
    console.error("❌ MongoDB connection error:", err);
    process.exit(1);
  });

async function testHuurwoningenScraper() {
  console.log("🚀 Starting Huurwoningen.nl scraper test...");
  console.log("=" .repeat(50));

  try {
    const startTime = Date.now();
    const listings = await scrapeHuurwoningen();
    const duration = Date.now() - startTime;

    console.log("\n📊 Test Results:");
    console.log(`⏱️  Duration: ${duration}ms (${(duration / 1000).toFixed(2)}s)`);
    console.log(`📋 Listings found: ${listings.length}`);

    if (listings.length > 0) {
      console.log("\n🏠 Sample listings:");
      listings.slice(0, 5).forEach((listing, index) => {
        console.log(`\n${index + 1}. ${listing.title}`);
        console.log(`   💰 Price: ${listing.price}`);
        console.log(`   📍 Location: ${listing.location}`);
        console.log(`   🏷️  Type: ${listing.propertyType}`);
        console.log(`   📐 Size: ${listing.size || 'N/A'}`);
        console.log(`   🚪 Rooms: ${listing.rooms || 'N/A'}`);
        console.log(`   📅 Year: ${listing.year || 'N/A'}`);
        console.log(`   🪑 Interior: ${listing.interior || 'N/A'}`);
        console.log(`   🔗 URL: ${listing.url}`);
      });

      if (listings.length > 5) {
        console.log(`\n... and ${listings.length - 5} more listings`);
      }
    } else {
      console.log("❌ No listings found. Check the scraper implementation.");
    }

    console.log("\n✅ Test completed successfully!");
  } catch (error) {
    console.error("❌ Test failed:", error);
    console.error("Stack trace:", error.stack);
  } finally {
    // Close the database connection
    await mongoose.connection.close();
    console.log("🔌 Database connection closed");
    process.exit(0);
  }
}

// Handle graceful shutdown
process.on("SIGINT", async () => {
  console.log("\n🛑 Test interrupted by user");
  await mongoose.connection.close();
  process.exit(0);
});

process.on("unhandledRejection", async (err) => {
  console.error("❌ Unhandled Promise Rejection:", err);
  await mongoose.connection.close();
  process.exit(1);
});

// Start the test
testHuurwoningenScraper();
