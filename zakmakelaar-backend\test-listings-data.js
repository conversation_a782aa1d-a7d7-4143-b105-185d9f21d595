const mongoose = require('mongoose');
const Listing = require('./src/models/Listing');

// Connect to MongoDB
mongoose.connect('mongodb://localhost:27017/zakmakelaar', {
  useNewUrlParser: true,
  useUnifiedTopology: true,
});

async function testListingsData() {
  try {
    console.log('🔍 Testing listings data...');
    
    // Count total listings
    const totalCount = await Listing.countDocuments();
    console.log(`📊 Total listings in database: ${totalCount}`);
    
    if (totalCount === 0) {
      console.log('❌ No listings found in database!');
      console.log('💡 You may need to run the scraper to populate data.');
      return;
    }
    
    // Get first 5 listings
    const listings = await Listing.find().limit(5);
    console.log(`📋 First ${listings.length} listings:`);
    
    listings.forEach((listing, index) => {
      console.log(`\n🏠 Listing ${index + 1}:`);
      console.log(`  Title: ${listing.title}`);
      console.log(`  Price: "${listing.price}" (${typeof listing.price})`);
      console.log(`  Location: ${listing.location}`);
      console.log(`  Rooms: "${listing.rooms}" (${typeof listing.rooms})`);
      console.log(`  Source: ${listing.source}`);
      console.log(`  Date Added: ${listing.dateAdded}`);
      
      // Test price parsing
      const numericPrice = parseFloat(listing.price.replace(/[^\d.-]/g, ''));
      console.log(`  Parsed Price: ${numericPrice} (${typeof numericPrice})`);
      console.log(`  Is Valid Number: ${!isNaN(numericPrice)}`);
    });
    
    // Test price patterns
    console.log('\n💰 Price analysis:');
    const pricePatterns = await Listing.aggregate([
      {
        $group: {
          _id: null,
          prices: { $push: '$price' },
          count: { $sum: 1 }
        }
      }
    ]);
    
    if (pricePatterns.length > 0) {
      const uniquePrices = [...new Set(pricePatterns[0].prices)].slice(0, 10);
      console.log('Sample price formats:');
      uniquePrices.forEach(price => {
        console.log(`  "${price}"`);
      });
    }
    
    console.log('\n✅ Listings data test completed!');
    
  } catch (error) {
    console.error('❌ Error testing listings data:', error);
  } finally {
    mongoose.connection.close();
  }
}

testListingsData();
