{"name": "is-extendable", "description": "Returns true if a value is any of the object types: array, regexp, plain object, function or date. This is useful for determining if a value can be extended, e.g. \"can the value have keys?\"", "version": "0.1.1", "homepage": "https://github.com/jonschlinkert/is-extendable", "author": "<PERSON> (https://github.com/jonschlinkert)", "repository": "jonschlinkert/is-extendable", "bugs": {"url": "https://github.com/jonschlinkert/is-extendable/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "devDependencies": {"mocha": "*"}, "keywords": ["array", "assign", "check", "date", "extend", "extensible", "function", "is", "object", "regex", "test"], "verbiage": {"related": {"list": ["isobject", "is-plain-object", "kind-of", "is-extendable", "is-equal-shallow", "extend-shallow", "assign-deep"]}}}