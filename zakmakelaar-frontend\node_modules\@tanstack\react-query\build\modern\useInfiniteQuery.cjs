"use strict";
"use client";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// src/useInfiniteQuery.ts
var useInfiniteQuery_exports = {};
__export(useInfiniteQuery_exports, {
  useInfiniteQuery: () => useInfiniteQuery
});
module.exports = __toCommonJS(useInfiniteQuery_exports);
var import_query_core = require("@tanstack/query-core");
var import_useBaseQuery = require("./useBaseQuery.cjs");
function useInfiniteQuery(options, queryClient) {
  return (0, import_useBaseQuery.useBaseQuery)(
    options,
    import_query_core.InfiniteQueryObserver,
    queryClient
  );
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  useInfiniteQuery
});
//# sourceMappingURL=useInfiniteQuery.cjs.map