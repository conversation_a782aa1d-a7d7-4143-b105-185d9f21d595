const mongoose = require("mongoose");
const Listing = require("./src/models/Listing");

async function addCompleteListings() {
  try {
    await mongoose.connect("mongodb://localhost:27017/zakmakelaar");
    console.log("✅ Connected to MongoDB");

    const completeListings = [
      {
        title: "Luxury Canal Apartment in Amsterdam",
        price: "€ 2,800 per maand",
        location: "Amsterdam, Noord-Holland",
        url: "https://example.com/luxury-canal-apartment-" + Date.now(),
        size: "95 m²",
        bedrooms: "3",
        rooms: "4",
        propertyType: "Apartment",
        description:
          "Stunning luxury apartment overlooking the famous Amsterdam canals. This beautifully renovated property features high ceilings, original wooden beams, and modern amenities. Located in the heart of the historic city center with easy access to museums, restaurants, and public transport.",
        year: "1890",
        interior: "Gemeubileerd",
        source: "funda.nl",
        images: [
          "https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?w=600&h=400&fit=crop",
          "https://images.unsplash.com/photo-1560448075-bb485b067938?w=600&h=400&fit=crop",
          "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=600&h=400&fit=crop",
        ],
      },
      {
        title: "Modern Penthouse in Rotterdam",
        price: "€ 3,500 per maand",
        location: "Rotterdam, Zuid-Holland",
        url:
          "https://example.com/modern-penthouse-rotterdam-" + (Date.now() + 1),
        size: "120 m²",
        bedrooms: "2",
        rooms: "3",
        propertyType: "Penthouse",
        description:
          "Spectacular penthouse with panoramic city views and a large private terrace. This contemporary property features floor-to-ceiling windows, a modern open kitchen, and premium finishes throughout. Perfect for professionals seeking luxury living in the heart of Rotterdam.",
        year: "2020",
        interior: "Gestoffeerd",
        source: "pararius.nl",
        images: [
          "https://images.unsplash.com/photo-1545324418-cc1a3fa10c00?w=600&h=400&fit=crop",
          "https://images.unsplash.com/photo-1484154218962-a197022b5858?w=600&h=400&fit=crop",
        ],
      },
      {
        title: "Charming Family House in Utrecht",
        price: "€ 2,200 per maand",
        location: "Utrecht, Utrecht",
        url: "https://example.com/family-house-utrecht-" + (Date.now() + 2),
        size: "110 m²",
        bedrooms: "4",
        rooms: "6",
        propertyType: "House",
        description:
          "Beautiful family home in a quiet residential neighborhood. Features include a spacious living room, modern kitchen, four bedrooms, and a lovely garden. Close to schools, parks, and public transport. Ideal for families with children.",
        year: "2005",
        interior: "Gestoffeerd",
        source: "funda.nl",
        images: [
          "https://images.unsplash.com/photo-1568605114967-8130f3a36994?w=600&h=400&fit=crop",
          "https://images.unsplash.com/photo-1570129477492-45c003edd2be?w=600&h=400&fit=crop",
        ],
      },
      {
        title: "Stylish Studio in The Hague",
        price: "€ 1,400 per maand",
        location: "Den Haag, Zuid-Holland",
        url: "https://example.com/stylish-studio-hague-" + (Date.now() + 3),
        size: "45 m²",
        bedrooms: "1",
        rooms: "2",
        propertyType: "Studio",
        description:
          "Modern studio apartment in the diplomatic quarter of The Hague. Efficiently designed with a sleeping area, living space, and compact kitchen. Great location near government buildings, international organizations, and the beach.",
        year: "2018",
        interior: "Kaal",
        source: "kamernet.nl",
        images: [
          "https://images.unsplash.com/photo-1522708323590-d24dbb6b0267?w=600&h=400&fit=crop",
        ],
      },
    ];

    // Add the complete listings
    const created = await Listing.insertMany(completeListings);
    console.log(`✅ Added ${created.length} complete listings with full data`);

    // Show what was created
    console.log("\n📋 Added listings:");
    created.forEach((listing, index) => {
      console.log(`${index + 1}. ${listing.title}`);
      console.log(`   Price: ${listing.price}`);
      console.log(`   Size: ${listing.size}`);
      console.log(`   Bedrooms: ${listing.bedrooms}`);
      console.log(`   Year: ${listing.year}`);
      console.log(`   Images: ${listing.images?.length || 0}`);
      console.log("");
    });

    console.log("🎉 Complete listings added successfully!");
  } catch (error) {
    console.error("❌ Error:", error);
  } finally {
    await mongoose.connection.close();
    console.log("🔌 Disconnected");
  }
}

addCompleteListings();
