import React, { useState } from "react";
import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  SafeAreaView,
  Modal,
} from "react-native";
import { useRouter } from "expo-router";
import SimpleConnectionTest from "../components/SimpleConnectionTest";
import AuthLogicTest from "../components/AuthLogicTest";
import HomeScreenTest from "../components/HomeScreenTest";

export default function WelcomeScreen() {
  const router = useRouter();
  const [showConnectionTest, setShowConnectionTest] = useState(false);
  const [showAuthTest, setShowAuthTest] = useState(false);
  const [showHomeTest, setShowHomeTest] = useState(false);

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.welcomeContainer}>
        <View style={styles.logoLarge}>
          <Text style={styles.logoLargeText}>ZM</Text>
        </View>
        <Text style={styles.welcomeTitle}>Welcome to ZakMakelaar!</Text>
        <Text style={styles.welcomeSubtitle}>
          Your AI-powered rental assistant for the Dutch market.
        </Text>
        <TouchableOpacity
          style={styles.primaryButton}
          onPress={() => router.push("/login")}
        >
          <Text style={styles.primaryButtonText}>Get Started</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.testButton}
          onPress={() => setShowConnectionTest(true)}
        >
          <Text style={styles.testButtonText}>Test Backend Connection</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.testButton}
          onPress={() => setShowAuthTest(true)}
        >
          <Text style={styles.testButtonText}>Test Auth Logic</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.testButton}
          onPress={() => setShowHomeTest(true)}
        >
          <Text style={styles.testButtonText}>Test Home Screen</Text>
        </TouchableOpacity>
      </View>

      {/* Connection Test Modal */}
      <Modal
        visible={showConnectionTest}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <SafeAreaView style={{ flex: 1 }}>
          <View style={styles.modalHeader}>
            <TouchableOpacity
              onPress={() => setShowConnectionTest(false)}
              style={styles.closeButton}
            >
              <Text style={styles.closeButtonText}>Close</Text>
            </TouchableOpacity>
          </View>
          <SimpleConnectionTest />
        </SafeAreaView>
      </Modal>

      {/* Auth Logic Test Modal */}
      <Modal
        visible={showAuthTest}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <SafeAreaView style={{ flex: 1 }}>
          <View style={styles.modalHeader}>
            <TouchableOpacity
              onPress={() => setShowAuthTest(false)}
              style={styles.closeButton}
            >
              <Text style={styles.closeButtonText}>Close</Text>
            </TouchableOpacity>
          </View>
          <AuthLogicTest />
        </SafeAreaView>
      </Modal>

      {/* Home Screen Test Modal */}
      <Modal
        visible={showHomeTest}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <SafeAreaView style={{ flex: 1 }}>
          <View style={styles.modalHeader}>
            <TouchableOpacity
              onPress={() => setShowHomeTest(false)}
              style={styles.closeButton}
            >
              <Text style={styles.closeButtonText}>Close</Text>
            </TouchableOpacity>
          </View>
          <HomeScreenTest />
        </SafeAreaView>
      </Modal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f3f4f6",
  },
  welcomeContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: 32,
    backgroundColor: "#ffffff",
    margin: 16,
    borderRadius: 24,
  },
  logoLarge: {
    width: 150,
    height: 150,
    backgroundColor: "#f72585",
    borderRadius: 75,
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 32,
  },
  logoLargeText: {
    fontSize: 48,
    fontWeight: "bold",
    color: "#ffffff",
  },
  welcomeTitle: {
    fontSize: 32,
    fontWeight: "bold",
    color: "#1f2937",
    textAlign: "center",
    marginBottom: 16,
  },
  welcomeSubtitle: {
    fontSize: 18,
    color: "#6b7280",
    textAlign: "center",
    marginBottom: 48,
    lineHeight: 24,
  },
  primaryButton: {
    width: "100%",
    backgroundColor: "#f72585",
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 12,
    alignItems: "center",
  },
  primaryButtonText: {
    color: "#ffffff",
    fontSize: 18,
    fontWeight: "600",
  },
  testButton: {
    width: "100%",
    backgroundColor: "#6b7280",
    paddingVertical: 10,
    paddingHorizontal: 24,
    borderRadius: 8,
    alignItems: "center",
    marginTop: 16,
  },
  testButtonText: {
    color: "#ffffff",
    fontSize: 14,
    fontWeight: "500",
  },
  modalHeader: {
    flexDirection: "row",
    justifyContent: "flex-end",
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#e5e7eb",
  },
  closeButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: "#f3f4f6",
    borderRadius: 6,
  },
  closeButtonText: {
    color: "#374151",
    fontSize: 14,
    fontWeight: "500",
  },
});
