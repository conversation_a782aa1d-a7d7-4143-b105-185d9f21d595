const Listing = require('../models/Listing');
const { logHelpers } = require('./logger');

class SearchService {
  constructor() {
    this.defaultLimit = 20;
    this.maxLimit = 100;
  }

  // Build MongoDB aggregation pipeline for advanced search
  buildSearchPipeline(searchParams) {
    const pipeline = [];
    const matchStage = {};

    // Text search
    if (searchParams.q) {
      matchStage.$text = { $search: searchParams.q };
    }

    // Location search (case-insensitive partial match)
    if (searchParams.location) {
      matchStage.location = { 
        $regex: searchParams.location, 
        $options: 'i' 
      };
    }

    // Price range
    if (searchParams.minPrice || searchParams.maxPrice) {
      matchStage.price = {};
      if (searchParams.minPrice) {
        matchStage.price.$gte = parseFloat(searchParams.minPrice);
      }
      if (searchParams.maxPrice) {
        matchStage.price.$lte = parseFloat(searchParams.maxPrice);
      }
    }

    // Property type
    if (searchParams.propertyType) {
      matchStage.propertyType = { 
        $regex: searchParams.propertyType, 
        $options: 'i' 
      };
    }

    // Number of rooms
    if (searchParams.minRooms || searchParams.maxRooms) {
      matchStage.rooms = {};
      if (searchParams.minRooms) {
        matchStage.rooms.$gte = parseInt(searchParams.minRooms);
      }
      if (searchParams.maxRooms) {
        matchStage.rooms.$lte = parseInt(searchParams.maxRooms);
      }
    }

    // Date range
    if (searchParams.dateFrom || searchParams.dateTo) {
      matchStage.dateAdded = {};
      if (searchParams.dateFrom) {
        matchStage.dateAdded.$gte = new Date(searchParams.dateFrom);
      }
      if (searchParams.dateTo) {
        matchStage.dateTo.$lte = new Date(searchParams.dateTo);
      }
    }

    // Add match stage if we have conditions
    if (Object.keys(matchStage).length > 0) {
      pipeline.push({ $match: matchStage });
    }

    // Add text score for text search
    if (searchParams.q) {
      pipeline.push({
        $addFields: {
          score: { $meta: 'textScore' }
        }
      });
    }

    // Sorting
    const sortStage = {};
    if (searchParams.q) {
      // Sort by text score first for text search
      sortStage.score = { $meta: 'textScore' };
    }

    // Add secondary sort
    const sortBy = searchParams.sortBy || 'dateAdded';
    const sortOrder = searchParams.sortOrder === 'asc' ? 1 : -1;
    sortStage[sortBy] = sortOrder;

    pipeline.push({ $sort: sortStage });

    return pipeline;
  }

  // Advanced search with aggregation
  async search(searchParams) {
    const startTime = Date.now();
    
    try {
      // Pagination
      const page = parseInt(searchParams.page) || 1;
      const limit = Math.min(parseInt(searchParams.limit) || this.defaultLimit, this.maxLimit);
      const skip = (page - 1) * limit;

      // Build aggregation pipeline
      const pipeline = this.buildSearchPipeline(searchParams);

      // Add pagination
      const paginationPipeline = [
        ...pipeline,
        {
          $facet: {
            data: [
              { $skip: skip },
              { $limit: limit }
            ],
            count: [
              { $count: 'total' }
            ]
          }
        }
      ];

      // Execute aggregation
      const [result] = await Listing.aggregate(paginationPipeline);
      const listings = result.data;
      const totalCount = result.count[0]?.total || 0;

      // Calculate pagination info
      const totalPages = Math.ceil(totalCount / limit);
      const hasNextPage = page < totalPages;
      const hasPrevPage = page > 1;

      const duration = Date.now() - startTime;
      logHelpers.logDbOperation('search', 'listings', duration);

      return {
        listings,
        pagination: {
          currentPage: page,
          totalPages,
          totalCount,
          hasNextPage,
          hasPrevPage,
          limit
        },
        searchParams,
        performance: {
          duration: `${duration}ms`,
          resultsFound: totalCount
        }
      };
    } catch (error) {
      const duration = Date.now() - startTime;
      logHelpers.logDbOperation('search', 'listings', duration, error);
      throw error;
    }
  }

  // Get search suggestions based on existing data
  async getSearchSuggestions(query, type = 'location') {
    const startTime = Date.now();
    
    try {
      let pipeline = [];
      
      switch (type) {
        case 'location':
          pipeline = [
            {
              $match: {
                location: { $regex: query, $options: 'i' }
              }
            },
            {
              $group: {
                _id: '$location',
                count: { $sum: 1 }
              }
            },
            {
              $sort: { count: -1 }
            },
            {
              $limit: 10
            },
            {
              $project: {
                _id: 0,
                suggestion: '$_id',
                count: 1
              }
            }
          ];
          break;
          
        case 'propertyType':
          pipeline = [
            {
              $match: {
                propertyType: { $regex: query, $options: 'i' }
              }
            },
            {
              $group: {
                _id: '$propertyType',
                count: { $sum: 1 }
              }
            },
            {
              $sort: { count: -1 }
            },
            {
              $limit: 10
            },
            {
              $project: {
                _id: 0,
                suggestion: '$_id',
                count: 1
              }
            }
          ];
          break;
          
        default:
          throw new Error('Invalid suggestion type');
      }

      const suggestions = await Listing.aggregate(pipeline);
      
      const duration = Date.now() - startTime;
      logHelpers.logDbOperation('suggestions', 'listings', duration);

      return suggestions;
    } catch (error) {
      const duration = Date.now() - startTime;
      logHelpers.logDbOperation('suggestions', 'listings', duration, error);
      throw error;
    }
  }

  // Get search statistics
  async getSearchStats() {
    const startTime = Date.now();
    
    try {
      const stats = await Listing.aggregate([
        {
          $group: {
            _id: null,
            totalListings: { $sum: 1 },
            avgPrice: { $avg: '$price' },
            minPrice: { $min: '$price' },
            maxPrice: { $max: '$price' },
            locations: { $addToSet: '$location' },
            propertyTypes: { $addToSet: '$propertyType' }
          }
        },
        {
          $project: {
            _id: 0,
            totalListings: 1,
            avgPrice: { $round: ['$avgPrice', 2] },
            minPrice: 1,
            maxPrice: 1,
            uniqueLocations: { $size: '$locations' },
            uniquePropertyTypes: { $size: '$propertyTypes' },
            locations: { $slice: ['$locations', 20] },
            propertyTypes: { $slice: ['$propertyTypes', 20] }
          }
        }
      ]);

      const duration = Date.now() - startTime;
      logHelpers.logDbOperation('stats', 'listings', duration);

      return stats[0] || {};
    } catch (error) {
      const duration = Date.now() - startTime;
      logHelpers.logDbOperation('stats', 'listings', duration, error);
      throw error;
    }
  }
}

module.exports = new SearchService();
