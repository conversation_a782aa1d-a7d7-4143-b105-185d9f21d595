import React, { useEffect, useState } from 'react';
import { View, ActivityIndicator, StyleSheet, Text } from 'react-native';
import { useAuthStore } from '../store/authStore';
import { apiService } from '../services/api';

interface AppInitializerProps {
  children: React.ReactNode;
}

export default function AppInitializer({ children }: AppInitializerProps) {
  const [isInitializing, setIsInitializing] = useState(true);
  const [initError, setInitError] = useState<string | null>(null);
  const { checkAuthStatus } = useAuthStore();

  useEffect(() => {
    initializeApp();
  }, []);

  const initializeApp = async () => {
    try {
      // Check API health
      try {
        await apiService.healthCheck();
        console.log('API health check passed');
      } catch (error) {
        console.warn('API health check failed:', error);
        // Don't fail initialization if health check fails
        // The app can still work offline or with cached data
      }

      // Check authentication status
      await checkAuthStatus();

      setIsInitializing(false);
    } catch (error: any) {
      console.error('App initialization failed:', error);
      setInitError(error.message || 'Failed to initialize app');
      setIsInitializing(false);
    }
  };

  if (isInitializing) {
    return (
      <View style={styles.container}>
        <View style={styles.logoContainer}>
          <Text style={styles.logoText}>ZM</Text>
        </View>
        <Text style={styles.appName}>ZakMakelaar</Text>
        <ActivityIndicator 
          size="large" 
          color="#f72585" 
          style={styles.loader}
        />
        <Text style={styles.loadingText}>Initializing...</Text>
      </View>
    );
  }

  if (initError) {
    return (
      <View style={styles.container}>
        <View style={styles.logoContainer}>
          <Text style={styles.logoText}>ZM</Text>
        </View>
        <Text style={styles.appName}>ZakMakelaar</Text>
        <Text style={styles.errorText}>
          Failed to initialize app: {initError}
        </Text>
        <Text style={styles.retryText}>
          Please check your internet connection and restart the app.
        </Text>
      </View>
    );
  }

  return <>{children}</>;
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f3f4f6',
    padding: 20,
  },
  logoContainer: {
    width: 80,
    height: 80,
    backgroundColor: '#f72585',
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  logoText: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#ffffff',
  },
  appName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 32,
  },
  loader: {
    marginBottom: 16,
  },
  loadingText: {
    fontSize: 16,
    color: '#6b7280',
  },
  errorText: {
    fontSize: 16,
    color: '#dc2626',
    textAlign: 'center',
    marginBottom: 16,
  },
  retryText: {
    fontSize: 14,
    color: '#6b7280',
    textAlign: 'center',
  },
});
