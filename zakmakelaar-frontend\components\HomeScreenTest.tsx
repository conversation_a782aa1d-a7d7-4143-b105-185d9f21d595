import React, { useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, ScrollView, Alert } from 'react-native';
import { useListingsStore } from '../store/listingsStore';
import { listingsService } from '../services/listingsService';

export default function HomeScreenTest() {
  const [results, setResults] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const { 
    recentListings, 
    totalCount, 
    fetchRecentListings, 
    searchListings,
    clearListings 
  } = useListingsStore();

  const addResult = (result: string) => {
    setResults(prev => [...prev, result]);
    console.log(result);
  };

  const testHomeScreenFeatures = async () => {
    setIsLoading(true);
    setResults([]);
    
    try {
      addResult('🏠 Testing Home Screen Features...');
      
      // Test 1: Fetch recent listings
      addResult('🔍 Testing recent listings fetch...');
      await fetchRecentListings(5);
      addResult(`✅ Recent listings: ${recentListings.length} found`);
      addResult(`📊 Total count: ${totalCount}`);
      
      // Test 2: Test search functionality
      addResult('🔍 Testing search functionality...');
      await searchListings('Amsterdam');
      const searchResults = useListingsStore.getState().recentListings;
      addResult(`✅ Search results: ${searchResults.length} found for "Amsterdam"`);
      
      // Test 3: Test stats endpoint
      addResult('🔍 Testing stats endpoint...');
      try {
        const statsResponse = await listingsService.getListingsStats();
        if (statsResponse.success) {
          addResult(`✅ Stats loaded: ${JSON.stringify(statsResponse.data)}`);
        } else {
          addResult(`⚠️ Stats failed: ${statsResponse.message}`);
        }
      } catch (error: any) {
        addResult(`⚠️ Stats error: ${error.message}`);
      }
      
      // Test 4: Clear and reload
      addResult('🔍 Testing clear and reload...');
      clearListings();
      await fetchRecentListings(3);
      const finalListings = useListingsStore.getState().recentListings;
      addResult(`✅ Reload complete: ${finalListings.length} listings`);
      
      addResult('🎉 Home screen test completed!');
      
    } catch (error: any) {
      addResult('💥 Test failed with exception');
      addResult(`Error: ${error.message}`);
      console.error('Home screen test error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const testQuickActions = () => {
    Alert.alert(
      'Quick Actions Test',
      'Testing the quick action buttons functionality',
      [
        { text: 'Preferences', onPress: () => addResult('📱 Preferences action triggered') },
        { text: 'Apply', onPress: () => addResult('📄 Apply action triggered') },
        { text: 'Review', onPress: () => addResult('📋 Review action triggered') },
        { text: 'Saved', onPress: () => addResult('❤️ Saved action triggered') },
        { text: 'Cancel', style: 'cancel' }
      ]
    );
  };

  const clearResults = () => {
    setResults([]);
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Home Screen Test</Text>
      
      <View style={styles.statusContainer}>
        <Text style={styles.statusText}>
          Current Listings: {recentListings.length}
        </Text>
        <Text style={styles.statusText}>
          Total Count: {totalCount}
        </Text>
      </View>
      
      <View style={styles.buttonContainer}>
        <TouchableOpacity 
          style={[styles.button, styles.primaryButton, isLoading && styles.buttonDisabled]} 
          onPress={testHomeScreenFeatures}
          disabled={isLoading}
        >
          <Text style={styles.buttonText}>
            {isLoading ? 'Testing...' : 'Test Home Features'}
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={[styles.button, styles.secondaryButton]} 
          onPress={testQuickActions}
          disabled={isLoading}
        >
          <Text style={styles.buttonText}>Test Quick Actions</Text>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={[styles.button, styles.clearButton]} 
          onPress={clearResults}
          disabled={isLoading}
        >
          <Text style={styles.buttonText}>Clear Results</Text>
        </TouchableOpacity>
      </View>
      
      <ScrollView style={styles.resultsContainer}>
        {results.map((result, index) => (
          <Text key={index} style={styles.resultText}>
            {result}
          </Text>
        ))}
        {results.length === 0 && (
          <Text style={styles.emptyText}>
            No results yet. Tap a test button to start.
          </Text>
        )}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#f3f4f6',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 16,
    textAlign: 'center',
  },
  statusContainer: {
    backgroundColor: '#ffffff',
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
  },
  statusText: {
    fontSize: 14,
    color: '#374151',
    marginBottom: 4,
  },
  buttonContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginBottom: 16,
  },
  button: {
    flex: 1,
    minWidth: 100,
    paddingVertical: 10,
    paddingHorizontal: 12,
    borderRadius: 6,
    alignItems: 'center',
  },
  primaryButton: {
    backgroundColor: '#f72585',
  },
  secondaryButton: {
    backgroundColor: '#3b82f6',
  },
  clearButton: {
    backgroundColor: '#6b7280',
  },
  buttonDisabled: {
    backgroundColor: '#9ca3af',
  },
  buttonText: {
    color: '#ffffff',
    fontSize: 12,
    fontWeight: '600',
  },
  resultsContainer: {
    flex: 1,
    backgroundColor: '#ffffff',
    borderRadius: 8,
    padding: 16,
  },
  resultText: {
    fontSize: 13,
    color: '#374151',
    marginBottom: 6,
    fontFamily: 'monospace',
  },
  emptyText: {
    fontSize: 14,
    color: '#6b7280',
    fontStyle: 'italic',
    textAlign: 'center',
    marginTop: 20,
  },
});
